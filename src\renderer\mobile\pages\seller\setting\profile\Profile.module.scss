.profile {
    padding: 0px 0px 0px 16px;
    display: flex;
    flex-direction: column;
    height: calc(100% - 75px);

    .heading {
        font-family: Noto Sans Display;
        font-size: 16px;
        font-weight: normal;
        line-height: 1.4;
        text-align: center;
        color: #fff;
        padding: 12px 0px 12px 0px;
        margin-top: 0px;
        border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 16px;

        span {
            margin: 0 auto;
        }
    }

    label {
        height: 19px;
        // flex-grow: 1;
        opacity: 0.7;
        font-family: Noto Sans Display;
        font-size: 12px;
        line-height: 1.6;
        text-align: left;
        color: #fff;
    }

    .inputField,.textHoverMask {
            width: 100%;
            height: 40px;
            padding: 10px 8px 10px 12px;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.1);
            border: none;
            outline: none;
            // margin-top: 4px;
            font-family: Noto Sans Display;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.6;
            text-align: left;
            color: #fff;
            margin-bottom: 16px;
            display: block;

            &::placeholder {
                color: rgba(255, 255, 255, 0.5);
            }

            &:focus {
                border: solid 0.5px #fff;
                background-color: rgba(0, 0, 0, 0.5);
            }
    }

    .btnSection {
        margin-top: auto;
        padding-right: 16px;

        button {
            width: 100%;
            height: 40px;
            font-family: Noto Sans Display;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 12px;
            border-radius: 8px;
            background-color: #70ff00;
            color: #000;

            &[disabled] {
                background-color: rgba(255, 255, 255, 0.3);
                color: rgba(0, 0, 0, 0.75);
                font-weight: normal;
            }
        }
    }
    .errorMsg{
        border: 1px solid #ff0000;
        &:focus{
            border: 1px solid #ff0000;
        }
    }
}

.settingsContent{
    overflow: auto;
    padding-right: 16px;
}

// Autocomplete Input Style

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    background-color: #636363;
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.2);
    padding-right: 4px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    border-radius: 0px 0px 4px 4px;
    margin-top: 1px;
  }
  
  .listAutoComletePanel.listAutoComletePanel {
    width: 100%;
    max-height: 300px;
    padding: 6px 4px 6px 10px;
    margin-top: 4px;
  
    &::-webkit-scrollbar {
      width: 6px;
    }
  
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  
    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 20px;
  
    }
  
    li {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      box-shadow: none;
      padding: 6px 8px;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      border-radius: 2px;
      min-height: 36px;
  
      &[aria-selected="true"] {
        background-color: #fff !important;
        color: #000;
      }
    }
  }

.companyInput {
    resize: none;
    width: 100%;
    height: 40px;
    padding: 10px 8px 10px 12px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    outline: none;
    margin-top: 4px;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;

    &::placeholder {
        color: #bbb;
    }
}

.stateDropdown {
    display: flex;
    gap: 12px;

    .grid1 {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        width: auto;
        max-width: 50%;
    }
}

.inputSectionRecevingHours{
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    .daylbl1{
        font-family: Noto Sans Display;
        font-size: 16px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: #fff;
        flex: 0 63px;
    }
    .gridReceivingHoursDropdown{
        display: flex;
        grid-gap:12px;
        flex-grow: 1;
    }
}

// Dropdown Style
.Dropdownpaper.Dropdownpaper {
    padding: 6px 4px 6px 12px;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2);
    background-color: #636363;
    overflow: hidden;
    border-radius: 0px 0px 4px 4px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;

    ul {
        overflow: auto;
        padding-right: 4px;
        padding-top: 0px;
        padding-bottom: 0px;
        max-height: 225px;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        li {
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            padding: 6px 12px;
            border-radius: 4px;
            min-height: 32px;

            &[aria-disabled="true"] {
                color: #fff;
                padding: 6px 0px;
                opacity: unset;
            }

            &[aria-selected="true"] {
                background-color: #fff;
                color: #000;
            }
        }
    }
}
