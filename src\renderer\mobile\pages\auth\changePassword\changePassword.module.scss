
.heading {
    font-family: <PERSON>o Sans Display;
    font-size: 16px;
    font-weight: normal;
    line-height: 1.4;
    text-align: center;
    color: #fff;
    padding: 12px 0px 12px 0px;
    margin-top: 0px;
    border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    margin:0px 24px;

    span {
        margin: 0 auto;
    }
}
.resetPasscontainer {
    padding: 24px;
    display: flex;
    flex-direction: column;
    height: calc(100% - 124px);

    .resetPassTitle {
        font-family: Noto Sans;
        font-size: 18px;
        font-weight: bold;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        margin-bottom: 4px;
        padding: 0px 12px;
    }

    &.changePassPopup {
        width: 100%;

        .resetPassTitle {
            margin-bottom: 16px;
            padding: 0px;
        }

        .changePassInnerContent {
            padding: 0px;
        }
    }

    .noteText {
        font-family: Not<PERSON> Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: rgba(255, 255, 255, 0.75);
        margin-bottom: 16px;
        padding: 0px 12px;
    }

    .changePassInnerContent {
        flex-grow: 1;
        padding: 0px 12px;

        .FormInputGroup {
            display: flex;
            flex-direction: column;
            margin-bottom: 12px;

            .lblInput {
                opacity: 0.7;
                font-family: Noto Sans Display;
                font-size: 12px;
                font-weight: normal;
                line-height: 1.6;
                text-align: left;
                color: #fff;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                margin-bottom: 4px;
            }

            .inputSection {
                font-family: Noto Sans;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                width: 100%;
                height: 40px;
                padding: 10px 8px 10px 12px;
                border-radius: 4px;
                background-color: rgba(255, 255, 255, 0.1);
                flex: 1 1;

                &.errorEmail.errorEmail {
                    border: solid 1px #f00;
                    color: rgba(0, 0, 0, 0.75);

                    &:focus {
                        border: solid 1px #f00;
                    }
                }


                input {
                    font-family: Noto Sans;
                    font-size: 14px;
                    font-weight: normal;
                    text-align: left;
                    color: #fff;
                    border: 0px;
                    background-color: transparent;
                    padding: 0px;
                    width: 100%;

                    &:focus {
                        outline: none;
                        box-shadow: none;
                    }

                    &::placeholder {
                        color: rgba(255, 255, 255, 0.5);
                        font-weight: normal;
                    }
                }

                .showPassBtn {
                    padding: 0px;
                    display: flex;
                }
            }
        }

        .currentPassword.currentPassword {
            flex-direction: column;
            display: flex;

            .FormInputGroup {
                margin-bottom: 0px;
            }

            .forgotPasswordTxt {
                font-family: Noto Sans;
                font-size: 14px;
                font-weight: normal;
                line-height: 1.6;
                letter-spacing: normal;
                text-align: left;
                color: rgba(255, 255, 255, 0.5);
                margin: 3px 0px 12px 0px;
                display: inline-flex;
                margin-left: auto;
                cursor: pointer;
            }
        }


    }

    .btnSection {
        width: 100%;
        display: flex;

        button {
            width: 100%;
            height: 40px;
            font-family: Noto Sans Display;
            font-size: 14px;
            font-weight: 600;
            padding: 10px 12px;
            border-radius: 8px;
            background-color: #70ff00;
            color: #000;

            &[disabled] {
                background-color: rgba(255, 255, 255, 0.3);
                color: rgba(0, 0, 0, 0.75);
                font-weight: normal;
            }
        }
    }
}



.passwordErrorContainer {
    position: relative;

    .errorBorder {
        position: absolute;
        right: -13px;
        top: 46px;
        border-radius: 0px 3px 3px 0px;
        border: solid 1px #f00;
        width: 13px;
        height: 82px;
        border-left: 0;

        svg {
            position: relative;
            top: 32px;
            right: -6px;
            width: 12px;
            height: 12px;
        }
    }
}