@import url(../../../seller/setting/profile/Profile.module.scss);

.uploadFileBtn.uploadFileBtn {
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 10px 8px 10px 12px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    margin-bottom: 0px;
    opacity: unset;

    button {
        margin-left: 9px;
        opacity: 0.5;
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: #fff;
    }

    input {
        display: none;
    }
}
.uploadFileStatus {
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 10px 8px 10px 12px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    font-family: Noto Sans Display;
    font-size: 14px;
    line-height: 1.4;
    text-align: left;
    color: #fff;

    .uploadedFileName {
        margin-right: auto;
    }
}
.viewBtn {
    text-decoration: none;
    padding-right: 4px;
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: 300;
    line-height: 1.6;
    color: #fff;
    &:hover,
    &:focus{
        color: #70ff00;
        outline: none;
    }
}
.orText {
    padding-right: 4px;
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: 300;
    line-height: 1.6;
    color: #fff;
}
.profileComponent{
    padding-right: 16px;
    input{
        display: none;
    }
}