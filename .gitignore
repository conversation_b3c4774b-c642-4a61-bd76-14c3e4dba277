# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/scripts/nodeUploader/node_modules

# Capacitor specific
capacitor-cordova-ios-plugins/
capacitor-cordova-android-plugins/

# iOS specific
ios/App/public/
ios/App/Pods/
ios/App/Podfile.lock
ios/App/*.xcworkspace
ios/App/*.xcuserstate
ios/App/*.xcodeproj
ios/App/fastlane/report.xml
ios/App/fastlane/Preview.html
ios/App/fastlane/screenshots/**/*.png
ios/App/fastlane/test_output
ios/App/App/Assets.xcassets/AppIcon.appiconset/
ios/App/App/Assets.xcassets/LaunchImage.launchimage/
ios/App/App/Assets.xcassets/LaunchStoryboard.imageset/

# Android specific
android/capacitor-cordova-android-plugins/
android/app/build/
android/.gradle/
android/app/src/main/assets/public/
android/app/release/
android/app/*/build

# Various IDEs/System files
.DS_Store
Thumbs.db
*.swp
*.swo
*.tmp
.idea/
*.iml
*.hprof
*.ipr
*.iws
*.jks
*.keystore
*.user
*.log
*.tmp
*.bak
*.sublime-workspace
*.sublime-project

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS generated files
.DS_Store*
._*
.Spotlight-V100
.Trashes
ehthumbs.db
[Tt]humbs.db

/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
/out
/.webpack
stats.html

#ignored for proper merging. These file changes shold be manually added in bitbucket. 
/ios/App/App/GoogleService-Info.plist
/android/app/google-services.json