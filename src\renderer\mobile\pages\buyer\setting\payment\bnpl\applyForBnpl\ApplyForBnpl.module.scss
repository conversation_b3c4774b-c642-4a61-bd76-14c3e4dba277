@import url(../../../profile/Profile.module.scss);

.applyForNetTermsBtn {
    font-family: Noto Sans Display;
    font-size: 14px;
    line-height: 1.6;
    text-align: left;
    color: #70ff00;
}

.radiobtnSectionTop {
    display: flex;
    margin-bottom: 12px;
}

// Radio Button Style
.containerRadio.containerRadio {
    display: inline-block;
    position: relative;
    cursor: pointer;
    padding-left: 25px;
    text-align: left;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: 300;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    margin-right: auto;

    input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }

    .checkmark {
        position: absolute;
        top: 3px;
        left: 0;
        z-index: 1;
        width: 15px;
        height: 15px;
        border: solid 1px #fff;
        background-color: #6a6a6a;
        border-radius: 50%;
    }

    .checkmark:after {
        content: "";
        position: absolute;
        display: none;
    }

    input:checked~.checkmark {
        background-color: transparent;
        border: solid 1px #70ff00;
    }

    input:checked~.checkmark:after {
        display: block;
    }

    .checkmark:after {
        left: 3px;
        top: 3px;
        width: 6.7px;
        height: 6.7px;
        background-color: #70ff00;
        border-radius: 50%;
    }
}

.submitAppMain {
    padding: 52px 16px 24px 16px;
    display: flex;
    flex-direction: column;
    height: 100%;

    p {
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: 300;
        line-height: 1.4;
        text-align: center;
        color: #fff;
        margin-bottom: 24px;

        .hereLink {
            color: #70ff00;
        }
    }

    .submitAppChk {
        margin-left: 33px;
        margin-right: 26px;
        padding-left: 24px;

        .lblSubmitApp {
            opacity: 0.7;
            font-family: Noto Sans Display;
            font-size: 12px;
            font-weight: 300;
            line-height: 1.4;
            text-align: left;
            color: #fff;
        }

        .checkmark {
            top: 7px
        }
    }


    .submitAppBtn {
        width: 100%;
        height: 40px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 10px 24px;
        border-radius: 8px;
        background-color: #70ff00;
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.4;
        color: #000;
        margin-top: auto;

        &[disabled] {
            opacity: 0.5;
            border: solid 0.5px #fff;
            background-color: #6d6d6d;
            color: #fff;
        }
    }
}

.closePopupBtn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 99;
}

.submitAppSuccessPopup {
    padding: 52px 16px 24px 16px;
    display: flex;
    flex-direction: column;
    height: 100%;

    .successPopupTitle {
        font-family: Noto Sans Display;
        font-size: 24px;
        font-weight: 600;
        line-height: 1.4;
        text-align: center;
        color: #70ff00;
        margin-bottom: 10px;
        padding: 0px;
    }

    p {
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: 300;
        line-height: 1.4;
        text-align: center;
        color: #fff;
        margin-bottom: 12px;
    }

    .gotItBtn {
        width: 100%;
        height: 40px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 10px 24px;
        border-radius: 8px;
        border: solid 0.5px #fff;
        background-color: #fff;
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.4;
        color: #000;
        margin-top: auto;
    }
}
.desiredcreditLine1{
    &.desiredcreditLineInput{
        position: relative;
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        .desiredcreditLineDollarSign{
          position: absolute;
          font-family: Noto Sans Display;
          font-size: 14px;
          line-height: 1.6;
          text-align: left;
          color: #fff;
          display: flex;
          align-items: center;
          padding-left: 12px;
        }
        input{
            margin-bottom: 0px;
            text-indent:12px;
        }
    }
}
.errorMsg{
    border: 1px solid #ff0000;
    &:focus{
        border: 1px solid #ff0000;
    }
}