@import url(../../setting/profile/Profile.module.scss);

.cashInAdvancedSection{
    margin-top: 16px;
    margin-bottom: 16px;
}

// Radio Button Style
.containerRadio.containerRadio {
    display: inline-block;
    position: relative;
    cursor: pointer;
    padding-left: 25px;
    text-align: left;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: 300;
    line-height: 1.6;
    text-align: left;
    color: #fff;

    input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }

    .checkmark {
        position: absolute;
        top: 3px;
        left: 0;
        z-index: 1;
        width: 15px;
        height: 15px;
        border: solid 1px #fff;
        background-color: #6a6a6a;
        border-radius: 50%;
    }

    .checkmark:after {
        content: "";
        position: absolute;
        display: none;
    }

    input:checked~.checkmark {
        background-color: transparent;
        border: solid 1px #70ff00;
    }

    input:checked~.checkmark:after {
        display: block;
    }

    .checkmark:after {
        left: 3px;
        top: 3px;
        width: 6.7px;
        height: 6.7px;
        background-color: #70ff00;
        border-radius: 50%;
    }
}

.bnplStatusNoteContainer{
    padding: 6px 0px;
 .bnplStatusNote {
      padding: 5px 8px;
      border-radius: 6px;
      border: solid 1px #ff5d47;
      background-color: #ffefed;
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: left;
      color: #ff5d47;

      p {
          margin: 0;
          span{
            white-space: nowrap;
          }
      }
  }
}

.disableSection{
    opacity: 0.3;
    pointer-events: none;
}