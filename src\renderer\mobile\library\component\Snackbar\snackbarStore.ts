import { create } from 'zustand';

const snackbarStoreInit = {
    openSnackbar: false,
    snackbarMessage: '',
    snackbarMessageHeader: '',
    snackbarSeverity: 'alert',
    snackbarActions: null,
    snackbarCloseHandler: null,
    snackbarTimer: null,
}

const useSnackbarStore = create((set) => ({
    ...snackbarStoreInit,
    setSnackbarOpen: (isOpen: boolean) => set({ openSnackbar: isOpen }),
    setSnackbarMessageHeader: (msgHeader: string) => set({ snackbarMessageHeader: msgHeader }),
    setSnackbarMessage: (msg: string) => set({ snackbarMessage: msg }),
    setSnackbarSeverity: (sev: string) => set({ snackbarSeverity: sev }),
    setSnackbarActions: (action: any) => set({ snackbarActions: action }),
    showToastSnackbar: (msg: string, sev: string, actions:any, closeHandler:any, msgHeader: string, timer: number|null) => set({openSnackbar: true,snackbarMessage: msg, snackbarMessageHeader: msgHeader, snackbarSeverity: sev, snackbarActions: actions, snackbarCloseHandler:closeHandler, snackbarTimer:timer}),
    resetSnackbarStore: () => set((state:any) => ({
        ...snackbarStoreInit
    }))
}));

export default useSnackbarStore;