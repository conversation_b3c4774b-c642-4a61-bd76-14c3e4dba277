import { useQuery } from '@tanstack/react-query';
import { Auth } from 'aws-amplify'; 
import { reactQueryKeys } from '../../common';
import { CognitoUser } from 'amazon-cognito-identity-js';
 
const getUser = (): Promise<CognitoUser>  => {
  return Auth.currentAuthenticatedUser().then((res) => {
    return res;
  });
};

const useCognitoUser = () => {
  return useQuery([reactQueryKeys.cognitoUser], getUser, {
    retry: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    onError(err) {
      return err;
    },
  });
};

export default useCognitoUser;
