import { IonContent, IonPage, useIonRouter } from "@ionic/react";
import { useEffect, useRef } from "react";
import styles from "./Welcome.module.scss";
import video1 from '../../../assets/mobile-images/welcomepage.mp4';
import clsx from "clsx";


function OnboardingMobileWelcome() {
    const videoRef: any = useRef();
    const router = useIonRouter();
    useEffect(() =>{
        function goToMainPage(){
            router.push("/onboarding-detail",{animate:true})
        }
  
        document.addEventListener('keydown',goToMainPage)
        document.addEventListener('click',goToMainPage)
        return(()=>{
          document.removeEventListener('keydown',goToMainPage)
          document.removeEventListener('click',goToMainPage)
        })
      },[router])
    const handleVideoEnded = () => {
        router.push("/onboarding-detail",{animate:true})
    }
    if (videoRef.current) {
        videoRef.current.addEventListener('ended', handleVideoEnded);
    }
    return (
        <IonPage>
            <IonContent>
                <div>
                    <div className={clsx(styles.welcomePage, 'bgBlurContent')}>
                        <video src={video1} autoPlay={true} ref={videoRef} />
                    </div>
                </div>
            </IonContent>
        </IonPage>
    );
}
export default OnboardingMobileWelcome;
