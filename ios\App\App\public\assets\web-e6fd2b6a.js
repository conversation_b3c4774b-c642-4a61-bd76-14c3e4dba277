import{bJ as t}from"./vendor-74ddad81.js";const e={status:"success",version:"",downloaded:"1970-01-01T00:00:00.000Z",id:"builtin",checksum:""};class s extends t{async setStatsUrl(n){console.warn("Cannot setStatsUrl in web",n)}async setUpdateUrl(n){console.warn("Cannot setUpdateUrl in web",n)}async setChannelUrl(n){console.warn("Cannot setChannelUrl in web",n)}async download(n){return console.warn("Cannot download version in web",n),e}async next(n){return console.warn("Cannot set next version in web",n),e}async isAutoUpdateEnabled(){return console.warn("Cannot get isAutoUpdateEnabled in web"),{enabled:!1}}async set(n){console.warn("Cannot set active bundle in web",n)}async getDeviceId(){return console.warn("Cannot get ID in web"),{deviceId:"default"}}async getBuiltinVersion(){return console.warn("Cannot get version in web"),{version:"default"}}async getPluginVersion(){return console.warn("Cannot get plugin version in web"),{version:"default"}}async delete(n){console.warn("Cannot delete bundle in web",n)}async list(){return console.warn("Cannot list bundles in web"),{bundles:[]}}async reset(n){console.warn("Cannot reset version in web",n)}async current(){return console.warn("Cannot get current bundle in web"),{bundle:e,native:"0.0.0"}}async reload(){console.warn("Cannot reload current bundle in web")}async getLatest(){return console.warn("Cannot getLatest current bundle in web"),{version:"0.0.0",message:"Cannot getLatest current bundle in web"}}async setChannel(n){return console.warn("Cannot setChannel in web",n),{status:"error",error:"Cannot setChannel in web"}}async unsetChannel(n){console.warn("Cannot unsetChannel in web",n)}async setCustomId(n){console.warn("Cannot setCustomId in web",n)}async getChannel(){return console.warn("Cannot getChannel in web"),{status:"error",error:"Cannot getChannel in web"}}async notifyAppReady(){return console.warn("Cannot notify App Ready in web"),{bundle:e}}async setMultiDelay(n){console.warn("Cannot setMultiDelay in web",n==null?void 0:n.delayConditions)}async setDelay(n){console.warn("Cannot setDelay in web",n)}async cancelDelay(){console.warn("Cannot cancelDelay in web")}}export{s as CapacitorUpdaterWeb};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
