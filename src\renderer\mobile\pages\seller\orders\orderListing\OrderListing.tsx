// @ts-nocheck
import clsx from 'clsx';
import styles from './OrderListing.module.scss';
import { useEffect, useState } from 'react';
import { commomKeys, mobileRoutes, orderPageConst, purchaseOrder } from '../../../../library/common';
import axios from 'axios';
import { getPurchaseOrderFilteredList } from '../../../../common/commonFunctionality';
import { ReactComponent as YellowWarningIcon } from '../../../../../assets/mobile-images/yellow-warning.svg';
import { ReactComponent as OrderListArrowIcon } from '../../../../../assets/mobile-images/order_list_icon.svg';
import { ReactComponent as OrderCheckIcon } from '../../../../../assets/images/order-check.svg';
import { formatToTwoDecimalPlaces } from '../../../../library/helper';
import Dialog from '../../../../components/Dialog/Dialog';
import useDialogStore from '../../../../components/Dialog/DialogStore';
import DialogPopup from '../../../../library/component/DialogPopup/DialogPopup';
import { IonContent, IonItem, IonItemOption, IonItemOptions, IonItemSliding, IonLabel, IonList, IonPage, useIonRouter, useIonViewDidEnter, useIonViewWillEnter, useIonViewWillLeave } from '@ionic/react';
import moment from 'moment';
import 'moment-timezone';
import { getSocketConnection, useSellerOrderStore, useGlobalStore } from '@bryzos/giss-ui-library';

const OrderListing = () => {
    // const navigate = useNavigate();
    // const location = useLocation();
    // const locationState = location.state;
    const router = useIonRouter();
    const {
        userData,
        backNavigation,
        setBackNavigation,
        navigationStateForNotification,
        setNavigationStateForNotification,
        setViewedOrdersListForBadgeCount,
        viewedOrdersListForBadgeCount,
        sellerSettingsData,
        setShowLoader,
        showLoader, productMapping , referenceData
    }: any = useGlobalStore();
    const{
        setShowAlertForNewPo,
        filterPoStoreBy,
        setFilterPoStoreBy,
        filteredPoList,
        setFilteredPoList,
        setOrderToBeShownInOrderAccept,
        searchByProductOrZipValue,
        setSearchByProductOrZipValue,
        isOrderReadyForNavigate
    } = useSellerOrderStore();
    const purchaseOrdersList = useSellerOrderStore((state: any) => state.ordersCart);
    let stateRef: any[];
    let availableAfter: any;
    if (referenceData) {
        stateRef = referenceData?.ref_states;
        availableAfter = referenceData.ref_general_settings?.find((setting: any) => setting.name === "SELLER_AVAIL_IN_MINUTES").value;
    }

    const [popupMessage, setPopupMessage] = useState("");
    const [dialogTitle, setDialogTitle] = useState('');
    const [dialogContent, setDialogContent] = useState('');
    const [dialogBtnTitle, setDialogBtnTitle] = useState('');
    const [dialogType, setDialogType] = useState('');
    // const [ notificationId ] = useState(orderId);
    const [showDialogPopup, setShowDialogPopup] = useState(false);
    const { showCommonDialog, resetDialogStore } = useDialogStore();

    useEffect(() => {
        setShowAlertForNewPo(false);
        return (() => {
            setBackNavigation(-1)
        })
    }, [])

    useIonViewWillEnter(()=>{
        setShowLoader(true);
    })

    useIonViewWillLeave(()=>{
        resetDialogStore();
    },[])

    useEffect(() => {
        const poNumber = navigationStateForNotification?.referenceId;
        if (poNumber && purchaseOrdersList?.length > 0 && isOrderReadyForNavigate) {
            const index = purchaseOrdersList?.findIndex((order: any) => order.buyer_po_number === poNumber);
            // setNavigationStateForNotification(null);
            setNavigationStateForNotification(null);
            if (index >= 0) {
                navigateToAcceptOrder(null, purchaseOrdersList[index], index, false);
            }
            else {
                showCommonDialog(orderPageConst.orderNotAvaialableMsg,[{name: commomKeys.errorBtnTitle, action: resetDialogStore}]);
                // setDialogTitle(commomKeys.info);
                // setDialogContent(orderPageConst.orderNotAvaialableMsg);
                // setDialogBtnTitle(commomKeys.errorBtnTitle);
                // setDialogType(commomKeys.actionStatus.error);
                // setShowDialogPopup(true);
            }
        }else if(poNumber && purchaseOrdersList?.length === 0){
            showCommonDialog(orderPageConst.orderNotAvaialableMsg,[{name: commomKeys.errorBtnTitle, action: resetDialogStore}]);
        }
    }, [navigationStateForNotification, purchaseOrdersList, isOrderReadyForNavigate]);

    useEffect(() => {
        if(purchaseOrdersList){
            const viewedOrderList = purchaseOrdersList?.reduce((list: any, currentOrder: any) => {
                if (currentOrder.claimed_by === purchaseOrder.readyToClaim) {
                    list.push(currentOrder.buyer_po_number);
                }
                return list;
            }, []);
            const payload = {
                data: {
                    "viewedOrdersListForBadgeCount": viewedOrderList
                }
            }
            try {
                axios.post(import.meta.env.VITE_API_NOTIFICATION_SERVICE + '/notification/saveUserUiEventData', payload).then(() => {
                    setViewedOrdersListForBadgeCount(viewedOrderList);
                });
            } catch (error) {
                console.error('error updating userJsonData: ', error);
            }
            const poListToBeShown = getPurchaseOrderFilteredList(purchaseOrdersList, filterPoStoreBy, searchByProductOrZipValue, productMapping);
            setFilteredPoList(poListToBeShown);
        }
    }, [filterPoStoreBy, searchByProductOrZipValue, purchaseOrdersList]);

    useEffect(()=>{
        if(router.routeInfo.pathname === mobileRoutes.orderListing){
            if(purchaseOrdersList === null || !getSocketConnection()?.connected) {
                if(!showLoader)
                setShowLoader(true);
            }
            else{
                setTimeout(() => {
                    setShowLoader(false)
                }, 1000);
            }
        }
    }, [purchaseOrdersList, showLoader, getSocketConnection()?.connected])

    const changePoOrderFilterBy = (filter: any) => {
        setFilterPoStoreBy(filter);
    }

    const navigateToAcceptOrder = ($event: any, purchaseOrder: any, index: any, showPopup: any) => {
        $event?.stopPropagation();
        setPopupMessage("");
        setOrderToBeShownInOrderAccept(purchaseOrder);
        // const data = { purchaseOrder, showPopup}; //changes required:send po_number and get the index in accept order component
        const data: any = { showPopup, index };
        router.push("/accept-order",{animate:true,direction:'forward'}, undefined, data)
        // navigate(routes.acceptOrderPage, { state: data });
    };

    const purchaseOrderTemplate = filteredPoList?.map((order: any, index: any) => {
        const state = stateRef?.find((stateDetail: any) => stateDetail.id == order.state_id).code;
        const deliveryDate = moment.utc(order?.delivery_date).tz('America/Chicago').format('M/DD/YY');
        let iconStatus =
            // <Tooltip
            //     title={activeOrderTooltip()}
            //     arrow
            //     placement={'bottom-start'}
            //     disableInteractive
            //     TransitionComponent={Fade}
            //     TransitionProps={{ timeout: 600 }}
            //     classes={{
            //         tooltip: 'orderPageTooltip',
            //     }}
            // >
            <div>
                <OrderCheckIcon />
            </div>
        // </Tooltip>;
        let showAvailableTime = false;
        let availableTime = '';
        const materialValue = +order.seller_po_price;
        const salesTaxValue = +order.seller_sales_tax;
        const totalOrderValue = (materialValue + salesTaxValue).toFixed(2);
        const createdDate = order.payment_method === purchaseOrder.paymentMethodBNPL ? order.created_date : order.ach_po_approved_date;
        const distinctHeaderSet = new Set();
        order.items.forEach((item: any) => distinctHeaderSet.add(item.shape));
        let poHeader = '';
        let loop = 0;
        distinctHeaderSet.forEach((item) => {
            poHeader += item;
            if (loop < distinctHeaderSet.size - 1) poHeader += ', ';
            loop++;
        })
        if (order.claimed_by === purchaseOrder.pending) {
            iconStatus = <div><YellowWarningIcon /></div>;
            showAvailableTime = true;
            availableTime = moment.utc(createdDate)
                .local()
                .add((+availableAfter) + 1, 'minutes')
                .format('h:mm a');
        }
        
        const handleSlide = ($event) => {
            const amount = $event.detail.amount;
            if(amount < -100){
                navigateToAcceptOrder($event, order,index, true)
            }
          };

        return (<li key={index}>
            <IonList className='orderList'>
            <IonItemSliding onIonDrag={handleSlide}>
                <IonItemOptions className='acceptBgOrderList' side="start">
                    <IonItemOption color="success" key={index} expandable>Swipe to <br/>Accept</IonItemOption>
                </IonItemOptions>
                <IonItem>
                <button className={styles.liOfOrder} id='orderListElement' key={index} onClick={($event) => navigateToAcceptOrder($event, order, index, false)}>
                  <div className={styles.iconGrid}>
                      {iconStatus}
                  </div>
                <div className={styles.orderDetailsGrid}>
                    <div className={styles.firstLine}>{poHeader}</div>
                    <div className={styles.gridContent}>
                        <div className={styles.colGrid2}>
                            <label className={styles.orderDetailslbl}>Total Order Value</label>
                            <div className={styles.orderDetailsTxt}>$ {formatToTwoDecimalPlaces(totalOrderValue)}</div>
                        </div>
                        <div className={styles.colGrid1}>
                            <label className={clsx(styles.orderDetailslbl, styles.textRight)}>Total Weight</label>
                            <div className={clsx(styles.orderDetailsTxt, styles.textRight)}>{formatToTwoDecimalPlaces(order.total_weight)} LBS</div>
                        </div>
                    </div>
                    <div className={styles.gridContent}>
                        <div className={styles.colGrid2}>
                            <label className={styles.orderDetailslbl}>Due Date</label>
                            <div className={styles.orderDetailsTxt}>{deliveryDate}</div>
                        </div>

                        <div className={styles.colGrid1}>
                            <label className={clsx(styles.orderDetailslbl, styles.textRight)}>Delivery Destination</label>
                            <div className={clsx(styles.orderDetailsTxt, styles.textRight)}>{order.city}, {state} {order.zip}</div>
                        </div>

                    </div>
                    {/* {showAvailableTime && <div className={styles.availabeAt} >Available @ {availableTime} </div>} */}
                    
                  </div>
                
            </button> 
                </IonItem>
                {/* <IonItemOptions side="end">
                    <IonItemOption color="success">Accept</IonItemOption>
                </IonItemOptions> */}
            </IonItemSliding>
            </IonList>

        </li>);
    });

    return (
        <IonPage>
            <IonContent>
                <>
                    <div className={styles.orderContent}>
                        <div className={clsx(styles.orderSearch, 'orderSearchBg')}>
                            {/* <input
                                type='search'
                                name='search'
                                className={styles.orderSearch}
                                placeholder='Search Orders by Product or Zip'
                                value={searchByProductOrZipValue}
                                onChange={$event => setSearchByProductOrZipValue($event.target.value)}
                                autoComplete="off"
                            /> */}
                            <div className={styles.btnOfOrderSearch}>
                                <button className={filterPoStoreBy === '' ? styles.activeBtn : ''} onClick={() => { changePoOrderFilterBy('') }}>All</button>
                                <button className={filterPoStoreBy === purchaseOrder.readyToClaim ? styles.activeBtn : ''} onClick={() => { changePoOrderFilterBy(purchaseOrder.readyToClaim) }}>Available</button>
                                <button className={filterPoStoreBy === purchaseOrder.pending ? styles.activeBtn : ''} onClick={() => { changePoOrderFilterBy(purchaseOrder.pending) }}>Upcoming</button>
                            </div>
                        </div>

                        <div className={styles.orderHeading}>
                        {filterPoStoreBy === '' ?
                            <>
                            <div>POs Now Available To Claim</div>
                            <div className={styles.bigHeading}>Swipe Right <span className={styles.aTag}><OrderListArrowIcon/></span> to make a sale. Tap an order to see line item detail.</div>
                            </>
                          : 
                          
                          "" 
                         
                         }
                           
                            {filterPoStoreBy === purchaseOrder.pending ?
                                <>
                                    <div className={styles.upcomingHeading}>Upcoming orders will be available soon to accept & fulfill</div>
                                    <div className={styles.upcomingSmallHeading}>Tap on the order to see the line item detail</div>
                                </>
                                :
                                ""
                            }
                            {filterPoStoreBy === purchaseOrder.readyToClaim ?
                            <>
                               <div>POs Now Available To Claim</div>
                                <div className={styles.bigHeading}>Swipe Right <span className={styles.aTag}><OrderListArrowIcon/></span> to make a sale. Tap an order to see line item detail.</div>
                            </>
                               
                            :
                                ""
                            }

                      </div>
                       
                        <div className={styles.listOfOrder}>
                            <ul id='ordersList'>
                                {purchaseOrderTemplate}
                            </ul>
                        </div>
                        {/* <div className={styles.btnSection}>
                        <div>
                            <button onClick={() =>{ navigate(backNavigation); setBackNavigation(-1) }} className={styles.backBtn}>Back</button>
                        </div>
                        <div className={styles.termsAndPatent}>
                            <div className={styles.TermsandConditions} onClick={() => navigate(routes.TnCPage, { state: { isViewMode: true, navigateTo: routes.orderPage } })}>Terms and Conditions</div>
                            <div className={styles.patentPendingText}>Patent Pending</div>
                        </div>
                    </div> */}
                    </div>
                    <Dialog
                        open={!!popupMessage}
                        transitionDuration={200}
                        hideBackdrop
                        classes={{
                            root: styles.ErrorDialog,
                            paper: styles.dialogContent
                        }}
                    >
                        <>
                            <p>{popupMessage}</p>
                            <button className={styles.submitBtn} onClick={() => setPopupMessage('')}>Ok</button>
                        </>
                    </Dialog>
                    <DialogPopup
                        dialogTitle={dialogTitle}
                        dialogContent={dialogContent}
                        dialogBtnTitle={dialogBtnTitle}
                        type={dialogType}
                        open={showDialogPopup}
                        onClose={() => setShowDialogPopup(false)}
                    />
                </>
            </IonContent>
        </IonPage>
    )
}
export default OrderListing;