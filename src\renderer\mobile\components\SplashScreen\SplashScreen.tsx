import React, { useEffect, useState } from 'react';
import { ReactComponent as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../assets/mobile-images/B_With_Shadow.svg';
import styles from './SplashScreen.module.scss'; // Assuming you're using SCSS modules
import { IonContent, IonPage } from '@ionic/react';
import { useSplashScreenStore } from '../../library/stores/splashScreenStore';
import LinearProgressBar from '../../library/component/LinearProgressBar/LinearProgressBar';

const SplashScreen = () => {
  const [isFadingOut, setIsFadingOut] = useState(false);
  const {capgoUpdateProgess,capgoUpdateDisplayText, resetSplashScreenStore} = useSplashScreenStore()

  useEffect(() => {
    const fadeOutTimer = setTimeout(() => {
      setIsFadingOut(true);
    }, 3000);    

    return () => {
      clearTimeout(fadeOutTimer)
      resetSplashScreenStore()
    }
  }, []);

  return (
    <IonPage>
      <IonContent>
        <div className={styles.splashContainer}>
          <div
            className={`${styles.bryzosLogo} ${
              isFadingOut ? styles.fadeOut : styles.fadeIn
            }`}
          >
            <BryzosLogo />
          </div>
          <p className={styles.capgoUpdateTxt}>{capgoUpdateDisplayText}</p> <br></br>
         {capgoUpdateProgess > 0 &&  <LinearProgressBar  progress={capgoUpdateProgess} /> }
        </div>
      </IonContent>
    </IonPage>
  );
};

export default SplashScreen;
