// @ts-nocheck
import { IonPage, IonContent, useIonRouter, useIonLoading } from '@ionic/react';
import styles from './MyReports.module.scss';
import { useState } from 'react';
import { downloadFiles } from '../../../../library/helper';
import { Dialog } from '@mui/material';
import clsx from 'clsx';
import { fileType, snackbarSeverityType } from '../../../../library/common';
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import { ReactComponent as FolderDownload } from '../../../../../assets/mobile-images/folder-download.svg';
import useSnackbarStore from '../../../../library/component/Snackbar/snackbarStore';
import { useGlobalStore } from '@bryzos/giss-ui-library';

const MyReports = () => {
    const [openErrorDialog, setOpenErrorDialog] = useState(false);
    const router = useIonRouter();
    const {showToastSnackbar, resetSnackbarStore, setSnackbarOpen}: any = useSnackbarStore();
    const [present, dismiss] = useIonLoading();
    const {setShowLoader} = useGlobalStore();

    const downloadReports = (fileUrl, fileName, fileType) => {
        const handleSnackbarClose = async () => {
            setSnackbarOpen(false);
          };

          setShowLoader(true);
        const showError = downloadFiles(fileUrl, fileName, fileType)
        showError.then(res => {
            setShowLoader(false);
            if (res) {
                setOpenErrorDialog(false);
            } else {
                // showToastSnackbar('No data found. Please try again in sometime', snackbarSeverityType.alert, null, handleSnackbarClose);
                setOpenErrorDialog(true);
            }
        })
        .catch(e => {
            setShowLoader(false);
        });

    }

    const routeBackToSetting = ()=>{
        router.push('/setting',{animate:true,direction:'forward'});
    }
    return (
        <IonPage>
            <IonContent>
                <div className={styles.myReports}>
                    <h2 className={styles.heading}><BackBtnArrowIcon onClick={routeBackToSetting}/><span>My Reports</span></h2>
                    <button  onClick={() => { downloadReports(import.meta.env.VITE_API_SERVICE + `/user/purchaseOrderExcel`, 'Puchase Order History', fileType.excelSheet) }}><FolderDownload/><span>Purchase Order History</span></button>
                    
                    <button  onClick={() => { downloadReports(import.meta.env.VITE_API_SERVICE + `/user/payableStatementExcel`, 'Payment History', fileType.excelSheet) }}><FolderDownload/><span>Funding History (Accounts Receivable Statement)</span></button>
                     
                </div>
                
            <>
                <Dialog
                    open={openErrorDialog}
                    onClose={(event) => setOpenErrorDialog(false)}
                    transitionDuration={200}
                    hideBackdrop
                    classes={{
                        root: styles.ErrorDialog,
                        paper: styles.dialogContent
                    }}

                >
                    <p className={styles.containOfError}>No data found. Please try again in sometime</p>
                    <button className={styles.okForErrorBtn} onClick={(event) => setOpenErrorDialog(false)}>Ok</button>
                </Dialog>
            </>
            </IonContent>
        </IonPage>
    )
}
export default MyReports;