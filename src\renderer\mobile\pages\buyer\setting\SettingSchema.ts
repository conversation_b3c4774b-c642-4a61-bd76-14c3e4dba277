// @ts-nocheck
import * as yup from "yup";
import { emailPattern, emojiRemoverRegex, removeCommaFromCurrency } from "../../../library/helper";
const isEmail = (email) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  return regex.test(email) && emailPattern.test(email);
}
export const profileSchema = yup.object({
  first_name:yup.string().trim().required('Name is not valid'),
  last_name:yup.string().trim().required('Name is not valid'),
  email_id:yup.string().trim().required('Email/Phone is not valid').matches(emailPattern,'Email/Phone is not valid').max(50, 'please do not enter more than 50 characters'),
  phone:yup.string().matches(/^\(\d{3}\) \d{3}-\d{4}$/, "Email/Phone is not valid").required("Phone number is required"),

})

export const companyInfoSchema = yup.object({
    companyName: yup.string().trim().required('Company Name is not valid').test('emoji-not-valid', 'Emoji is not allowed', (value) => !emojiRemoverRegex.test(value)),
    yourCompany: yup.string().trim().required('Your Company is not valid').test('emoji-not-valid', 'Emoji is not allowed', (value) => !emojiRemoverRegex.test(value)),
    companyAddressLine: yup.string().trim().required('Company Address is not valid').test('emoji-not-valid', 'Emoji is not allowed', (value) => !emojiRemoverRegex.test(value)),
    companyCity:yup.string().trim().required('Company Address is not valid').test('emoji-not-valid', 'Emoji is not allowed', (value) => !emojiRemoverRegex.test(value)),
    companyState:yup.number().required('Company Address is not valid'),
    companyZipCode:yup.string().required('Company Address is not valid').min(5,'Company Address is not valid'),
})

export const deliveryDetailSchema = yup.object({
    
    deliverToAddress: yup.string().trim().required('Address is not valid'),
    deliverToCity: yup.string().trim().required('City is not valid'),
    deliverToState: yup.number().required('State is not valid'),
    deliverZipCode: yup.string().required('Zip is not valid').min(5,'Zip is not valid'),
    deliveryDate: yup.number().required('Delivery Date is Required'),
    sendInvoicesTo: yup.string().trim().required('Send Invoices to is not valid').test('valid-emails', 'Send Invoices to is not valid', value => {
      if (!value) return true; // empty field is allowed
      const emails = value.split(',');
      const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
      return isValid;
    }),
    shippingDocsTo: yup.string().trim().required('Enter valid email').test('valid-emails', 'Enter valid email', value => {
      if (!value) return true; // empty field is allowed
      const emails = value.split(',');
      const isValid = emails.every(email => email.trim() && isEmail(email.trim()));
      return isValid;
    }),
  
})

export const receivingHoursSchema = yup.object({
    dates: yup.array()
})

export const paymentSettingSchema = yup.object({

  bnplAvailable: yup.boolean(),
  net30CheckBox: yup.string(),
  einNumber: yup.string().trim().test("isRequired", "Ein Number is not valid", function (value) {
    const bnplAvailable = this.parent.bnplAvailable;
    if (bnplAvailable === true) return true;
    const net30CheckBox = this.parent.net30CheckBox;
    if (net30CheckBox === "false") return true;
    if (!/^x{5}\d{4}$|^\d{2}-\d{7}$/.test(value)) {
      return false
    }
    return !!value;
  }),
  dnBNumber: yup.string().test("isRequired", "D&B Number is not valid", function (value) {
    const bnplAvailable = this.parent.bnplAvailable;
    if (bnplAvailable === true) return true;
    const net30CheckBox = this.parent.net30CheckBox;
    if (net30CheckBox === "false") return true;
    if (!/^x{5}\d{4}$|^\d{9}$/.test(value)) {
      return false
    }
    return !!value;
  }),
  creditLine: yup.string().test("isRequired", "Credit Line is not valid", function (value) {
    const bnplAvailable = this.parent.bnplAvailable;
    if (bnplAvailable === true) return true;
    const net30CheckBox = this.parent.net30CheckBox;
    if (net30CheckBox === "false") return true;
    if (value) {
      return +removeCommaFromCurrency(value) > 0;
    } else {
      return false;
    }
  }),
  achCheckBox: yup.string(),
  bankName: yup.string(),
  routingNo: yup.string(),
  accountNo: yup.string(),
  requestCreditLine: yup.string(),
})

export const documentInfoSchema = yup.object({
  resaleCertificates: yup.array().of(
    yup.object().shape({
      cerificateFileName: yup.string().nullable(),
      cerificateS3Url: yup.string().default(null).nullable(),
      stateId: yup.number().nullable().when("cerificateS3Url", {
        is: (s3Url) => !!s3Url,
        then: (s) => s.required("Required")
      }),
      expirationDate: yup.string().nullable().when("cerificateS3Url", {
        is: (s3Url) => !!s3Url,
        then: (s) => s.required("Required")
      }),
      status: yup.string().nullable(),
      id: yup.string().nullable(),
      isDeletable: yup.string().nullable(),
    })
  ).test("atLeastOneValidField", "Please fill atleast one certificate", (values => values?.some(value => value.cerificateS3Url))),
})

