apply plugin: 'com.android.application'
apply plugin: 'com.google.firebase.crashlytics'

android {
    namespace "bryzos.extended.pricing.widget"
    compileSdk rootProject.ext.compileSdkVersion
    defaultConfig {
        applicationId "bryzos.extended.pricing.widget"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 15
        versionName "1.0.148"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        aaptOptions {
             // Files and dirs to omit from the packaged assets dir, modified to accommodate modern web apps.
             // Default: https://android.googlesource.com/platform/frameworks/base/+/282e181b58cf72b6ca770dc7ca5f91f135444502/tools/aapt/AaptAssets.cpp#61
            ignoreAssetsPattern '!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~'
        }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

repositories {
    flatDir{
        dirs '../capacitor-cordova-android-plugins/src/main/libs', 'libs'
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation "androidx.appcompat:appcompat:$androidxAppCompatVersion"
    implementation "androidx.coordinatorlayout:coordinatorlayout:$androidxCoordinatorLayoutVersion"
    implementation "androidx.core:core-splashscreen:$coreSplashScreenVersion"
    implementation project(':capacitor-android')
    testImplementation "junit:junit:$junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
    implementation project(':capacitor-cordova-android-plugins')

    implementation "com.google.firebase:firebase-iid:21.1.0"
    implementation 'com.google.firebase:firebase-messaging:24.0.0'
    implementation 'com.google.firebase:firebase-installations:18.0.0'
    implementation 'com.google.firebase:firebase-crashlytics:19.0.3'
    implementation 'com.google.firebase:firebase-analytics:22.0.2'
    implementation 'com.pusher:push-notifications-android:1.9.2'

    testImplementation 'junit:junit:4.12'
}

apply from: 'capacitor.build.gradle'
apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'

try {
    def servicesJSON = file('google-services.json')
    if (servicesJSON.text) {
        apply plugin: 'com.google.gms.google-services'
    }
} catch(Exception e) {
    logger.info("google-services.json not found, google-services plugin not applied. Push Notifications won't work")
}
