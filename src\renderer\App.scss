.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.widgetCanvas{
  position: relative;
  background: transparent;
  border-radius: 10px;
  width: 100%;
  overflow: hidden;
  max-width: 600px;
}
.webBackground{
  background-color: #000;
}
.webLoginHeight{
  height: 108px;
}

.loaderContent{
  border-radius: 0px 0px 10px 10px;
  // -webkit-backdrop-filter: blur(60px);
  // backdrop-filter: blur(60px);
  // background-color: rgba(0, 0, 0, 0.75);
  margin: 0px auto;
  max-width: 600px;
  position: absolute;
  inset: 0;
  z-index: 10;
}

.bgImg {
  background-color: transparent;
  height: auto;
  left: 0px;
  mix-blend-mode: normal;
  object-fit: cover;
  top: 0px;
  width: 100%;
  // background-image: url('https://prod-bryzos-assets.imgix.net/img/bg-app.png?w=700&h=900&auto=compress');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  max-width: 600px;
  margin: 0 auto;
  position: relative;
}

.loaderRunning{
  opacity: 0 !important;
}

.blurBg{
  border-radius: 0px;
  background-color: rgba(0, 0, 0, 0.5);
}