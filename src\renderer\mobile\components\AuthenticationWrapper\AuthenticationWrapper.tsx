import { getDeviceId, setUserAppData } from "../../library/helper";
import { loginPageConst, removeAxiosInterceptor, socketDisconnect, useGlobalStore, useSellerOrderStore , useAuthStore, changePasswordConst, decryptData, encryptData , commomKeys, useGetDeviceIdApproval} from "@bryzos/giss-ui-library";
import {App as NativeApp} from '@capacitor/app';
import { Device } from '@capacitor/device';
import packageData from "../../../../../package.json"
import useOurLogin from "../../library/hooks/useOurLogin";
import rg4js from "raygun4js";
import { Auth } from "aws-amplify";
import { unRegister } from "../../common/pushNoti";
import { useIonRouter } from "@ionic/react";
import useSnackbarStore from "../../library/component/Snackbar/snackbarStore";
import { Directory, Filesystem } from "@capacitor/filesystem";
import { useEffect, useState } from "react";
import useBuyerSettingStore from "../../pages/buyer/setting/BuyerSettingStore";
import useSellerSettingStore from "../../pages/seller/setting/SellerSettingStore";
import axios from 'axios';
import { mobileRoutes } from "../../library/common";
import useOnSubmitLogin from "../../library/hooks/useOnSubmitLogin";
import SplashScreen from "../SplashScreen/SplashScreen";
import { useSplashScreenStore } from "../../library/stores/splashScreenStore";

const AuthenticationWrapper  = ({children,cognitoUser,configureAxiosInterceptor,deviceId,getInitialData,resetStoreOnLogout, setResetStoreOnLogout, noInternet}) =>{
    const {userData, setUserData,setShowLoader, forceLogout, setForceLogout, resetCommonStore,  isUserLoggedIn, setIsUserLoggedIn,setBackdropOverlay, setHasLoginProcessCompleted, globalForceLogout, setGlobalForceLogout, autoLogin, setAutoLogin, passwordChanged, setPasswordChanged, setSignupUser, globalLogout, setGlobalLogout ,setCurrentUser, isAppStateChangeActive, userCredentials, setUserCredentials, decryptionEntity, securityHash} = useGlobalStore();
    const ourLogin = useOurLogin()
    const router = useIonRouter()
    const { resetSnackbarStore }: any = useSnackbarStore();
    const { resetOrderStore } = useSellerOrderStore();
    const { resetBuyerSetting } = useBuyerSettingStore();
    const { resetSellerSetting } = useSellerSettingStore();
    const {triggerLogout,setTriggerLogout,resetDefaultLogoutData,defaultLogoutData,resetAuthCommonStore, triggerLogin,resetLoginData , defaultLoginData, setLoginError,  triggerSubmitLogin, resetSubmitLogin, submitLoginData }= useAuthStore();
    const {showSplashScreen , setShowSplashScreen ,updatesFound, resetSplashScreenStore, setUpdatesFound} = useSplashScreenStore()
    const onSubmitLogin = useOnSubmitLogin(setLoginError)
    const checkDeviceIdApproval = useGetDeviceIdApproval()
    useEffect(()=>{
      setUserCredForAutoLogin();
      return ()=>{
          setLoginError();
      }
    },[])

    useEffect(() => {
      const setAppData = async () => {
        try {
          const deviceInfo = await Device.getInfo(); 
          const appInfo = (deviceInfo.platform === 'web') 
            ? { version: 'web' } 
            : await NativeApp.getInfo();
          if (!deviceId.current) {
            deviceId.current = await getDeviceId();
          } 
    
          if (deviceInfo && packageData?.version && appInfo) {
            setUserAppData({
              os_version: `${deviceInfo.platform} ${deviceInfo.osVersion} ${deviceInfo.model}`,
              last_login_app_version: appInfo.version,
              ui_version: packageData.version,
              device_id: deviceId.current
            });
          }
        } catch (error) {
          console.error("Error fetching device or app info:", error);
        }
      };
    
      setAppData();
    }, []); 
    
    useEffect(() => {
      if (cognitoUser && isUserLoggedIn) {
        // @ts-ignore
        initSession(cognitoUser.attributes.email);
      }
    }, [cognitoUser])

    useEffect(()=>{
      if(isAppStateChangeActive && !noInternet && location.pathname === mobileRoutes.loginPage ){
        setUserCredForAutoLogin();
      }
    }, [isAppStateChangeActive, noInternet])

    useEffect(()=>{
      if(!updatesFound){
        if(userCredentials && decryptionEntity && securityHash && deviceId.current){
          setShowSplashScreen(false)
          setShowLoader(true);
          router.push('/login', { animate: true, direction: 'forward' })
          if((userCredentials !== changePasswordConst.noUserCredentialFound) ){
            decryptUserCredential(userCredentials, decryptionEntity);
            setUserCredentials(null);
          }else{
            setShowLoader(false);
          }
        }
      }
    },[userCredentials, decryptionEntity, updatesFound, securityHash, deviceId])

    useEffect(()=>{
      if(updatesFound){
        setTimeout(() => {
          setUpdatesFound(false);
        }, 30000);
      }
    },[updatesFound])

    useEffect(()=>{
      if(resetStoreOnLogout){
        clearStore();
      }
    }, [resetStoreOnLogout])

    useEffect(()=>{
      if(passwordChanged){
        if(passwordChanged.device_id !== deviceId.current && passwordChanged.email_id === userData.data.email_id){
          handleLogout(true);
        }
        setPasswordChanged(null);
      }
    },[passwordChanged])

    useEffect(()=>{
      if(forceLogout){
        handleLogout(true, false, false);
        setForceLogout(false);
      }
    }, [forceLogout])

    useEffect(() => {
      (async() => {
          if(globalForceLogout){
              await handleLogout(true, true);
              router.push(globalForceLogout, {animate:true });
              setGlobalForceLogout(null);
          }
      })();
    },[globalForceLogout])
    
    useEffect(()=>{
      (async() => {
          if(globalLogout){
              await handleLogout(false, true);
              router.push(globalLogout, {animate:true });
              setGlobalLogout(null);
          }   
      })();
    }, [globalLogout])
  
    useEffect(()=>{
      if(autoLogin){
        refreshAndAutoLogin();
        setAutoLogin(false);
      }
    },[autoLogin])

    useEffect(()=>{
      if(triggerLogout){
        handleLogout(defaultLogoutData.isForceLogout,defaultLogoutData.stopNavigate,defaultLogoutData.removePassword);
        setTriggerLogout(false);
        resetDefaultLogoutData()
      }
    },[triggerLogout])

    useEffect(()=>{
      if(triggerLogin){
        initSession(defaultLoginData.emailData,defaultLoginData.isRefresh,defaultLoginData.axiosMethod)
        resetLoginData()
      }
    },[triggerLogin])

    useEffect(()=>{
      if(triggerSubmitLogin && submitLoginData){
        submit(submitLoginData)
        resetSubmitLogin()
    }
    },[triggerSubmitLogin])

    const decryptUserCredential = async (userCredentials, decryptionEntity) => {
      const data = JSON.parse(await decryptData(userCredentials, decryptionEntity.decryption_key, true));
      submit(data);
    }
  
    async function submit(data) {
      try{
        setShowLoader(true)
        const res = await checkDeviceIdApproval.mutateAsync(data.email);
        if(!res?.data?.data?.status || res?.data?.data === "Success"){
          setLoginError({message: res?.data?.data?.message ??  commomKeys.errorContent})
          setShowLoader(false);
          return;
        }
        onSubmitLogin.mutateAsync(data)
      }catch(err){
        setLoginError({message: commomKeys.errorContent})
        setShowLoader(false)
      }
    }

    const setUserCredForAutoLogin = async () => {
      try{
        const credential = await Filesystem.readFile({
          path: 'user-data.txt',
          directory: Directory.Cache,
        });
        if(credential){
          setUserCredentials(credential.data ?? changePasswordConst.noUserCredentialFound);
        }else{
          setUserCredentials(changePasswordConst.noUserCredentialFound);
        }
      }catch(err){
        setUserCredentials(changePasswordConst.noUserCredentialFound);
      }
    }

    const refreshAndAutoLogin = async() => {
      partialLogout();
      clearStore();
      setShowLoader(true);
      await setUserCredForAutoLogin();
      router.push(mobileRoutes.loginPage, { animate: true })
    }

    const handleLogout = async (isForceLogout, stopNavigate=false, removePasswordCred=true) => {
      if (userData) {
        setShowLoader(true);
        if (!isForceLogout) {
          let payload = {
            data: {
              'email_id': userData.data.email_id
            }
          }
          const deviceInfo = await Device.getInfo();
          const appInfo = (deviceInfo.platform === 'web') ? 'web' : await NativeApp.getInfo();
          if (deviceInfo && packageData?.version && appInfo) {
            payload.data.os_version = deviceInfo.platform + " " + deviceInfo.osVersion + " " + deviceInfo.model;
            payload.data.last_login_app_version = appInfo.version;
            payload.data.ui_version = packageData.version;
          }
          await axios.post(import.meta.env.VITE_API_SERVICE + '/user/logout', payload)
            .finally(async () => {
              await clearOnLogout(stopNavigate, removePasswordCred);
            })
        }
        else await clearOnLogout(stopNavigate, removePasswordCred);
      }
    }
  
    const clearStore = () => {
      resetOrderStore();
      resetCommonStore();
      resetSellerSetting();
      resetBuyerSetting();
      configureAxiosInterceptor(true);
      setResetStoreOnLogout(false);
      setIsUserLoggedIn(false);
      resetAuthCommonStore(),
      resetSplashScreenStore()
    }

    async function initSession(emailData: string, isRefresh, axiosMethod) {
        if(!isRefresh) await configureAxiosInterceptor();
        if (!deviceId.current) {
          deviceId.current = await getDeviceId();
        }
        let payload = {
          data: { "email_id": emailData, device_id: deviceId.current, }
        }
        setShowSplashScreen(false)
        setShowLoader(true);
        const deviceInfo = await Device.getInfo();
        const appInfo = (deviceInfo.platform === 'web') ? 'web' : await NativeApp.getInfo();
        if (deviceInfo && packageData?.version && appInfo) {
          payload.data.os_version = deviceInfo.platform + " " + deviceInfo.osVersion + " " + deviceInfo.model;
          payload.data.last_login_app_version = appInfo.version;
          payload.data.ui_version = packageData.version;
        }
        ourLogin.mutateAsync(payload).then(async ({ data: { data } }) => {
        setCurrentUser(data)
          if (!data) {
            return;
          }
          if(typeof data === 'string') {
            initSession(emailData)
            setShowSplashScreen(false)
            setShowLoader(false);
            return;
          }
          rg4js('setUser', {
            firstName: data.first_name,
            lastName: data.last_name,
            email: data.email_id,
          });
          await getInitialData(data, deviceInfo.platform, isRefresh, axiosMethod);
          setSignupUser(null);
          setHasLoginProcessCompleted(true);
        })
        .catch(e => {
          if(e?.response?.status === 400){
            setLoginError({message: loginPageConst?.loginApprovalError});
            clearOnLogout(false, true);
          }
          setSignupUser(null);
          setShowSplashScreen(false)
          setShowLoader(false);
        });
    }

    const removeAutoLogIn = async () => {
      try {
        await Filesystem.deleteFile({
          path: 'user-data.txt',
          directory: Directory.Cache  // Specify the directory
        });
      } catch (e) {
        console.error('Error deleting file', e);
      }
    }

    const clearOnLogout = async (stopNavigate, removePasswordCred = true) => {
        try {
          await Auth.signOut();
        } catch (error) {
          console.error('error signing out: ', error);
          setShowLoader(false);
        } finally {
          unRegister();
          document.cookie.split(";").forEach((c) => {
            document.cookie = c.replace(/^ +/, "").replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);
          });
          localStorage.removeItem('isUserLoggedIn');
          if(removePasswordCred) await removeAutoLogIn();
          if(!stopNavigate)
          router.push('/login', { animate: true});
          removeAxiosInterceptor();
          partialLogout();
          setResetStoreOnLogout(true);
          setShowLoader(false);
        }
    }

    const partialLogout = () => {
      setUserData({});
      resetSnackbarStore();
      setBackdropOverlay(false);
      socketDisconnect();
    }
    
    return (
      <> {showSplashScreen ? <SplashScreen ></SplashScreen> : <>{children}</>}</>
    )
}

export default AuthenticationWrapper;