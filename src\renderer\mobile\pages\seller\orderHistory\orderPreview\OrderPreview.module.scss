.yourOrderPreview {
  padding: 16px 0px;
  height: calc(100% - 75px);

  .orderBuyerPo {
    padding: 0px 16px;
  }

  .heading {
    border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
    padding-bottom: 11.5px;
    opacity: 0.8;
    font-family: Noto Sans Display;
    font-size: 16px;
    font-weight: 300;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    width: 100%;
    display: flex;

    span {
      margin: 0 auto;
    }
  }

  .poAndDateDetails {
    padding: 10px 16px;
    font-family: Noto Sans Display;
    font-size: 12px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #fff;

    div {
      padding-bottom: 4px;

      span {
        font-weight: 600;
        padding-left: 10px;
      }
    }
  }

  .addPoLineTable {
    table {

      thead,
      tbody {
        tr {

          td,
          th {
            &:first-child {
              width: 10%;
            }

            &:nth-child(2) {
              width: 42%;
            }

            &:nth-child(3) {
              width: 24%;
              text-align: center;
            }

            &:nth-child(4) {
              width: 24%;
              text-align: right;
              padding-right: 10px;
            }
          }
        }
      }
    }

    thead {
      width: 100%
    }

    tbody {
      display: block;
      overflow-y: auto;
       height: calc(100vh - 520px );
       min-height: 150px;

      tr {
        &:last-child {
          td {
            border-bottom: 0px;
          }
        }
      }
    }

    thead,
    tbody tr {
      display: table;
      width: 100%;
      table-layout: fixed;
    }

  }

  .tableGrid {
    width: 100%;
    display: inline-grid;
    grid-template-areas:
      "head-fixed"
      "body-scrollable"
      "foot-fixed";

    thead {
      grid-area: head-fixed;
    }

    tbody {
      grid-area: body-scrollable;

      tr {
        &:nth-child(even) {
          background-color: rgba(0, 0, 0, 0.15);

          td {

            &:nth-child(2),
            &:nth-child(3) {
              color: rgba(255, 255, 255, 0.67);
            }
          }
        }
      }
    }

    tr {
      th {
        height: 21px;
        padding: 2px 3px 1.7px 3px;
        background-color: rgba(0, 0, 0, 0.4);
        font-family: Noto Sans Display;
        font-size: 12px;
        font-weight: 500;
        line-height: 1.4;
        text-align: center;
        color: #fff;

        &:nth-child(2) {
          text-align: left;
        }

        &.lastTd {
          text-align: right;
          padding-right: 10px;
        }
      }
    }

    tr {
      td {
        font-family: Noto Sans Display;
        padding: 10px 3px;
        font-size: 12px;
        font-weight: 300;
        line-height: 1.2;
        text-align: left;
        color: #fff;
        vertical-align: top;
        border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);

        .tagLbl {
          margin-top: 4px;
          display: block;
        }

        .domesticMaterialLbl {
          color: #70ff00;
          margin-top: 2px;
          display: block;
          font-size: 10px;
        }

        &.qtyTd {
          text-align: center;
        }

        &:first-child {
          font-size: 16px;
          font-weight: 500;
          line-height: 1;
          text-align: center;
          color: rgba(255, 255, 255, 0.8);
          vertical-align: text-top;
        }

        &.lastTd {
          text-align: right;
          padding-right: 10px;
        }
      }
    }

  }

  .totalAmt {
    padding: 11px 10px;
    grid-area: foot-fixed;
    width: 100%;

    tr:is(:first-child) {
      td {
        padding: 2px 0px;

        &:first-child {
          text-align: right;

          span {
            padding-right: 0px;
          }
        }

        &:last-child {
          span {
            padding-right: 10px;
          }
        }


        &:nth-child(3) {
          text-align: center;
          width: 25px;

          span {
            padding-right: 0px;
          }
        }

        div {
          display: block;
          padding: 2px 0px;
          border-top: 0.5px solid rgba(255, 255, 255, 0.3);
          border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
          background-color: rgba(0, 0, 0, 0.6);
          height: 20px;
        }



        border-top:0.5px solid rgba(255, 255, 255, 0.3);
        border-bottom:0.5px solid rgba(255, 255, 255, 0.3);

        &.materialTotal {
          flex-grow: 0;
          font-family: Noto Sans Display;
          font-size: 12px;
          font-weight: 500;
          line-height: 1.2;
          text-align: right;
          color: rgba(255, 255, 255, 0.8);
        }

        &.materialTotal1 {
          white-space: nowrap;
          font-weight: 500;
        }

        &:nth-child(3) {
          text-align: center;
        }
      }
    }

    tr {
      &:last-child {
        td {
          padding-bottom: 0px;
        }
      }

      td {
        padding: 4px 0px 0px 0px;
        font-family: Noto Sans Display;
        font-size: 12px;
        font-weight: 300;
        line-height: 1.2;
        text-align: right;
        color: rgba(255, 255, 255, 0.8);

        &:last-child {
          width: 80px;
        }

        &:nth-child(3) {
          text-align: center;
        }
        .saleTax{
          font-weight: 500;
        }
        .txtSign {
          display: block;
          text-align: center;
          padding: 0px 10px;
          font-weight: 500;
        }

        .txtNumber {
          font-weight: 600;
          padding-right: 10px;
        }

        .totalPurchase,
        .totalPurchaseSign,
        .totalPurchaseNumber {
          font-size: 20px;
          font-weight: 600;

        }

        .totalPurchaseSign {
          font-weight: 600;
        }

        .totalPurchaseNumber {
          font-weight: 600;
          padding-right: 10px;
          width: 60px;
        }
      }
    }
  }
}

.acceptOrderHead {
  width: 100%;
  display: flex;
  gap: 8px;
  margin-top: 17px;
  padding: 0px 16px;

  button {
    &:disabled {
      color: rgb(255, 255, 255);
      border: solid 0.5px rgb(255, 255, 255);
      background-color: rgba(0, 0, 0, 0.5);
      opacity: 0.2;
      font-weight: 300;
      cursor: not-allowed;

      &:hover {
        border: solid 0.5px rgb(255, 255, 255);
      }
    }
  }

  .btnPreNextPo {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0;
    width: 56px;
    height: 48px;
    align-self: stretch;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 0 10px;
    border-radius: 2px;
    border: solid 0.8px rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.2);
    font-family: Noto Sans Display;
    font-size: 12px;
    font-weight: 300;
    line-height: 1.2;
    text-align: center;
    color: #fff;
  }

  .previewNextPreBtn {
    width: 100%;
    height: 44px;
    border-radius: 8px;
    border: 0;
  }
}
