.tncBox {
    width: 100%;
    padding: 30px 0px 0px 0px;
    // -webkit-backdrop-filter: blur(60px);
    // backdrop-filter: blur(60px);
    // background-color: rgba(0, 0, 0, 0.75);
    .onboardingLogo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 41px;
        min-height: 140px;
        pointer-events: none;
    }

    .tnCInnerContent {
        padding: 0px 20px;
    }

    .tnCPage {
        padding: 12px 4px 12px 12px;
        border-radius: 4px;
        border: solid 0.6px #fff;
        .tncScrollClass {
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            max-height: 224px;

            &::-webkit-scrollbar {
                width: 8px;
                height: 6px;
            }
            &::-webkit-scrollbar-thumb {
            background:
                #9da2b2;
            border-radius: 50px;
            }
            &::-webkit-scrollbar-track {
            background: transparent;
            }

            .TermsofUseV1 {
                font-family: Noto Sans;
                font-size: 24px;
                font-weight: bold;
                line-height: 1.4;
                text-align: left;
                color: #fff;
            }
            p {
                margin: 16px 1px 16px 0;
                font-family: Noto Sans;
                font-size: 14px;
                font-weight: 300;
                line-height: 1.4;
                text-align: left;
                color: #fff;
            }
        }
    }

    .checkingThisbox {
        margin: 24px 0 0 3px;
        font-family: Noto Sans;
        font-size: 12px;
        font-weight: 300;
        line-height: 1.4;
        color: #fff;
    }
    .btnSectionTnC{
        text-align: left;
        .onBoradingDownloadTnC{
            font-family: Noto Sans;
            font-size: 14px;
            line-height: 1.5;
            text-align: center;
            color: #fff;
            transition: all 0.1s;
            cursor: pointer;
            margin-top: 15px;
            margin-left: 51px;
            display: inline-block;
            &:hover{
              color: #70ff00;
        
            }
        }
    }

   

    .GetStartedbtn {
        width: 384px;
        height: 49px;
        margin: 80px 108px 40px;
        padding: 12px 25px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.1);
        font-family: Noto Sans;
        font-size: 18px;
        font-weight: 300;
        line-height: 1.4;
        text-align: center;
        color: rgba(255, 255, 255, 0.75);
        &[disabled]{
         cursor: not-allowed;
        }
    }
}

.onboardingTnCBody {

    .GetStartedbtn {
        background-color: #70ff00;
        font-family: Noto Sans Display;
        font-weight: 600;
        color: #000;
    }
}

.onboardingBlurClass {
    filter: blur(4px);
    -webkit-filter: blur(4px);
}

.ErrorDialog {
    .dialogContent {
        max-width: 300px;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 30px 34px 30px 34px;
        object-fit: contain;
        border-radius: 10px;
        -webkit-backdrop-filter: blur(24px);
        backdrop-filter: blur(24px);
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
        background-color: rgba(0, 0, 0, 0.72);
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;
  
        p {
            margin-bottom: 20px;
        }
  
  
        .submitBtn {
            height: 40px;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 10px 24px;
            border-radius: 4px;
            border: solid 0.5px #fff;
            background-color: transparent;
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: center;
            color: #fff;
            margin-top: 20px;
            transition: all 0.1s;
  
            &:hover {
                background-color: #70ff00;
                border: solid 0.5px #70ff00;
                color: #000;
            }
  
            &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
  
                &:hover {
                    border: solid 0.5px #fff;
                    background-color: transparent;
                    color: #fff;
                }
            }
        }
  
  
    }
  
  }