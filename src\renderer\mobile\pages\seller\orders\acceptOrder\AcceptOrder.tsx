// @ts-nocheck
import styles from "./AcceptOrder.module.scss";
import { IonContent, IonModal, IonPage, useIonRouter, useIonViewWillEnter, useIonViewWillLeave } from "@ionic/react";
import { memo, useEffect, useRef, useState } from "react";
import { purchaseOrder, referenceDataKeys } from "../../../../library/common";
import useSaveSellerViewedOrder from "../../../../library/hooks/useSaveSellerViewedOrder";
import { ReactComponent as WarningIcon } from '../../../../../assets/images/warning-seller-icon.svg';
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import { ReactComponent as CloseIcon } from '../../../../../assets/mobile-images/close_Popup.svg';
import { format4DigitAmount, formatCurrencyWithComma, formatToTwoDecimalPlaces } from "../../../../library/helper";
import clsx from "clsx";
import ReminderYouAreAlmostTherePopup from "../ReminderYouAreAlmostTherePopup/ReminderYouAreAlmostTherePopup";
import OrderConfirmation from "../../../../components/OrderConfirmation/OrderConfirmation";
import moment from 'moment';
import 'moment-timezone';
import SwipeButton from "../../../../components/SwipeButton/AcceptSwipeButton";
import { getSocketConnection, useSellerOrderStore, useGlobalStore } from '@bryzos/giss-ui-library';

const SubmitApplicationDialog = (props: any) => {

    const [hasAcceptedTnC, setHasAcceptedTnC] = useState(false);

    const swipeButtonRef = useRef<any>();

    const tncChangeHandler = ($event: any) => {
        $event.stopPropagation();
        setHasAcceptedTnC(state => !state);
    }

    return (
        <IonModal className={clsx('orderAcceptPopup', styles.orderAcceptDrawer)} isOpen={props.open ?? false} backdropDismiss={false}>
            <button className={styles.closePopupBtn} onClick={props.onClose}><CloseIcon /></button>
            <div className={styles.orderAcceptContent}>
                <div className={styles.orderInnerContent}>
                    <div className={styles.importantBtn}>
                        <span className={styles.leftIcon}><WarningIcon /></span>
                        IMPORTANT
                        <span className={styles.rightIcon}><WarningIcon /></span>
                    </div>
                    <ul>
                        <li>All delivered material must be new, prime, within the stated tolerance per the product specification and include mill test reports.</li>
                        <li>Material must be packaged & loaded for forklift or magnetic offloading.</li>
                        <li>Strip load only, no pyramid loading for pipe.</li>
                        <li>Material must be reasonably free of oxidation and pitting.</li>
                        <li>Maximum bundle weight is 5,000 pounds.</li>
                        <li>By “Accepting Order,” you agree to fulfill this order (including delivery, fuel surcharges and other fulfillment-related items) at the published price.  <br />
                            The only acceptable variance between the published price and your future invoices will be reasonable quantity reconciliations (ie. ordered vs shipped).</li>
                    </ul>


                    <label className='containerChk'>
                        <input type='checkbox' checked={hasAcceptedTnC} onChange={($event) => { tncChangeHandler($event) }} />
                        <span className='checkmark'></span>
                        <span className='lblChk'>
                            I understand and agree.
                        </span>
                    </label>
                </div>


                <div className={styles.flx}>
                    <SwipeButton
                        ref={swipeButtonRef}
                        classes=''
                        bollTitle='Slide to accept order'
                        isValid={hasAcceptedTnC}
                        errors={props.errors}
                        onSuccess={(e) => {
                            props.acceptOrder(e);
                        }}
                    />
                </div>

            </div>

        </IonModal>
    );
};


const AcceptOrder = () => {
    // const navigate = useNavigate();
    // const location = useLocation();
    const router = useIonRouter();
    const routeData = router.routeInfo.routeOptions;
    const {
        userData,
        sellerSettingsData,
        backNavigation,
        setBackNavigation,
        hideHeader,
        setHideHeader,
        setShowLoader,
        showLoader,
        pusherActionEvent,referenceData
    }: any = useGlobalStore();

    const {
        changePoToBeAccepted,
        filteredPoList,
        navigatePageTo,
        errorPopupDetail,
        setErrorPopupDetail,
        setNavigatePageTo,
    }: any= useSellerOrderStore()
    const orderDetail = useSellerOrderStore((state: any) => state.orderToBeShownInOrderAccept);
    const setOrderDetail = useSellerOrderStore((state: any) => state.setOrderToBeShownInOrderAccept);
    const setPOCart = useSellerOrderStore((state: any) => state.setPOCart);
    const ordersCart = useSellerOrderStore(state => state.ordersCart);
    const [showScrollDownToSee, setShowScrollDownToSee] = useState(true);
    let stateRef: any = [];
    if (referenceData) stateRef = referenceData.ref_states;
    const [poIndex, setPoIndex] = useState({index:routeData?.index});
    const [errorMessage, setErrorMessage] = useState('');
    const [openErrorPopUp, setOpenErrorPopUp] = useState(false);
    const [isReminderPopup, setIsReminderPopup] = useState(true);
    const [openReminderYouAreAlmostTherePopup, setOpenReminderYouAreAlmostTherePopup] = useState(false);
    const [openSubmitApp, setOpenSubmitApp] = useState(routeData?.showPopup && sellerSettingsData);
    const [openOrderConfirmation, setOpenOrderConfirmation] = useState(false);

    const saveSellerViewedOrder = useSaveSellerViewedOrder();

    useIonViewWillEnter(() => {
        setHideHeader(true);
    }, [])

    useIonViewWillEnter(() => {
        if(showLoader) setShowLoader(false);
    }, [showLoader])

    useIonViewWillLeave(() => {
        cleanUpBeforeSwitch()
    }, [])

    useEffect(() => {
          if(pusherActionEvent){
            cleanUpBeforeSwitch()
          }
      }, [pusherActionEvent])

    useEffect(() => {
        if (sellerSettingsData) {
            setOpenReminderYouAreAlmostTherePopup(false);
        } else {
            setOpenReminderYouAreAlmostTherePopup(true);
        }
    }, [sellerSettingsData]);

    useEffect(() => {
        if (!routeData?.isEditMode)
            setPoIndex({index:filteredPoList?.findIndex((order: any) => order.buyer_po_number === orderDetail?.buyer_po_number)});
    }, [filteredPoList]);

    useEffect(() => {
        console.log(poIndex);
        const order = filteredPoList[poIndex.index] ? filteredPoList[poIndex.index] : orderDetail;
        order?.items?.sort((a: any, b: any) => {
            return a.po_line - b.po_line;
        });
        saveSellerViewedOrder.mutateAsync({ data: { id: order.id } }).then(async () => {
            ordersCart[index].is_order_view = true;
            setPOCart([ ...ordersCart ])
        }).catch(e => {
            console.log(e)
        });
        setOrderDetail(order);
    }, [poIndex]);

    useEffect(() => {
        if (navigatePageTo && !openOrderConfirmation) {
            console.log("---------" + navigatePageTo);
            changePoToBeAccepted(null);
            setNavigatePageTo('');
            setOpenOrderConfirmation(true)
        }
    }, [navigatePageTo]);

    useEffect(() => {
        if (errorPopupDetail && !openErrorPopUp) {
            setOpenSubmitApp(false);
            setErrorMessage(errorPopupDetail);
            setBackNavigation(-3)
            setOpenErrorPopUp(true);
        }
    }, [errorPopupDetail]);

    const cleanUpBeforeSwitch = ()=>{
        setHideHeader(false);
        setOpenOrderConfirmation(false);
        setOpenReminderYouAreAlmostTherePopup(false)
        setOpenSubmitApp(false);
        setOpenErrorPopUp(false);
    }

    const state = stateRef?.find((stateDetail: any) => stateDetail.id == orderDetail?.state_id)?.code;
    const deliveryDate = moment.utc(orderDetail?.delivery_date).tz('America/Chicago').format('MMM DD, YYYY');
    // const deliveryDate = dayjs.utc(orderDetail?.delivery_date).format('MMM DD, YYYY');

    const availableAfter = referenceData?.ref_general_settings?.find((setting: any) => setting.name === referenceDataKeys.sellerAvailInMinKey).value;
    const domesticMaterialText = referenceData?.ref_general_settings?.find((setting: any) => setting.name === referenceDataKeys.domesticMaterialTextKey).value;
    const createdDate = orderDetail?.payment_method === purchaseOrder?.paymentMethodBNPL ? orderDetail?.created_date : orderDetail?.ach_po_approved_date;
    const availableTime = moment.utc(createdDate).add((+availableAfter) + 1, 'minute').local().format('h:mm a');
    const materialValue = +orderDetail?.seller_po_price;
    const salesTaxValue = +orderDetail?.seller_sales_tax;
    const totalOrderValue = (materialValue + salesTaxValue).toFixed(2);
    const startIndex = 0;
    const lastIndex = filteredPoList?.length - 1;
    const productsContainerRef: any = useRef();

    const handleScroll = () => {
        if (productsContainerRef.current.scrollTop !== 0)
            setShowScrollDownToSee(false);
    };

    const handleClickOpen = ($event: any) => {
        $event.stopPropagation();
        console.log('seller ', sellerSettingsData)
        if (sellerSettingsData) {
            setOpenReminderYouAreAlmostTherePopup(false);
            setOpenSubmitApp(true);
        } else {
            setIsReminderPopup(false);
            setOpenReminderYouAreAlmostTherePopup(true);
        }
    };

    const handleErrorClose = ($event: any) => {
        $event.stopPropagation();
        setOpenErrorPopUp(false);
        setErrorPopupDetail(null);
        router.push("/order-listing",{animate:true,direction:'forward'})
        // navigate(routes.orderPage);
    };
    const handleSubmitClose = ($event: any) => {
        $event.stopPropagation();
        setOpenSubmitApp(false);
    };  
    const previousPage = () => {
        if (poIndex.index > startIndex) {
            scrollToTopOfOrdersList();
            setShowScrollDownToSee(true);
            setPoIndex((state: any) => {return {index:state.index - 1}});

        }
    }
    const nextPage = () => {
        console.log(poIndex + "=" + filteredPoList.length);
        if (poIndex.index < lastIndex) {
            console.log(poIndex, lastIndex);
            scrollToTopOfOrdersList();
            setShowScrollDownToSee(true);
            setPoIndex((state: any) => {return {index:state.index + 1}});

        }
    }

    const acceptOrder = ($event: any) => {
        const socket = getSocketConnection();
        console.log("Accepting Order..." + orderDetail?.buyer_po_number);
        socket.emit('acceptPo', { buyer_po_number: orderDetail?.buyer_po_number });
        setOpenSubmitApp(false);
        changePoToBeAccepted(orderDetail?.buyer_po_number);
        // navigate(routes.orderConfirmationPageSeller);
    }

    const ErrorDialog = memo((props: any) => {
        const [disable, setDisable] = useState(false);

        const closeDialog = ($event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
            setDisable(true);
            props.onClose($event);
        }
        
        return (
            <IonModal className={'shareAppPopup'} isOpen={props.open ?? false} backdropDismiss={false}>

                {/* <p>{props.errorMessage}</p> */}
                <button className={styles.closePopupBtn1} onClick={props.onClose}><CloseIcon /></button>
                <div className={styles.missPopupMain}>
                    <p className={styles.youJustMissetext} >YOU JUST MISSED IT!</p>
                    <p className={styles.missPopupText}>This order was just claimed by another Seller.</p>
                    <p className={styles.missPopupText}>Continue to review more orders & click “Accept Order” as soon as you know you can fulfill the order. Good luck!</p>
                    <p className={styles.missPopupText}>The only acceptable variation in invoice amount is based on shipped quantity.</p>
                    {/* <button className={styles.submitBtn} onClick={($event) => props.onClose($event)}>Ok</button> */}
                        <button onClick={($event) => {closeDialog($event)}} type="submit" className={styles.doneBtn} disabled={disable}>
                        Claim Another Order
                        </button>
                </div>
            </IonModal>
        )
    })

    function display(data: string) {
        const lines = data.split('\n');
        const firstLine = lines[0];
        const restLines = lines.slice(1);

        return (
            <div>
                <p className="liHead">{firstLine}</p>
                {restLines.map((line, index) => (
                    <p key={index}>{line}</p>
                ))}
            </div>
        );
    }

    const productListing = orderDetail?.items?.map((product: any, index: any) => {
        return (<tr key={index}>
            <td>{index + 1}</td>
            <td >
                <span className={styles.description}>
                    {display(product.description)}
                </span>
                {/* <div className={styles.stripLoad}>
                    Strip Load / Include MTRs
                </div> */}
                {(product.domestic_material_only > 0) && <div className={styles.domesticMaterialLbl}>
                    {domesticMaterialText}
                </div>}
            </td>
            <td className={styles.qtyTd}>
                <span>{formatCurrencyWithComma(product.qty)} {product.qty_unit} <br/>
                    {product.price_unit === "Lb" ? format4DigitAmount(product.seller_price_per_unit) : formatToTwoDecimalPlaces(product.seller_price_per_unit)}</span> / <span>{product.price_unit}</span>
            </td>
            <td className={styles.lastTd}><div className={styles.div1}>$ {formatToTwoDecimalPlaces(product.seller_line_total)}</div></td>
        </tr>);
    })

    const disablePreviousButton = startIndex === poIndex.index || filteredPoList.length <= 1;
    const disableNextButton = lastIndex === poIndex.index || filteredPoList.length <= 1;

    const scrollToBottomOfOrdersList = () => {
        setShowScrollDownToSee(false);
        productsContainerRef.current.scrollTop = productsContainerRef.current.scrollHeight;
    }
    const scrollToTopOfOrdersList = () => {
        productsContainerRef.current.scrollTop = 0;
    }
    return (
        <IonPage>
            <IonContent>
                {!openOrderConfirmation ?
                    <>


                        {routeData?.isEditMode &&
                            <div className={styles.disputeHeader}>
                                PO# {orderDetail?.po_number}
                            </div>
                        }
                        <div className={styles.acceptOrderContent}>
                            <ErrorDialog
                                open={openErrorPopUp}
                                onClose={($event: any) => handleErrorClose($event)}
                                errorMessage={errorMessage} />
                            <div className={styles.orderDetailContent}>

                                <div className={styles.headerTop}>
                                    <h2 className={styles.heading}><BackBtnArrowIcon onClick={() => router.push("/order-listing",{animate:true,direction:'forward'})} /><span>{orderDetail?.claimed_by === purchaseOrder.pending ? '' : 'Available' } Orders </span></h2>
                                    {orderDetail?.claimed_by === purchaseOrder.pending ?
                                        <button className={styles.orderPreviewBtn}>
                                            <span className={styles.leftIcon}><WarningIcon /></span>
                                            <span className={styles.rightIcon}><WarningIcon /></span>
                                            <span>Order Preview</span>
                                            <span className={styles.acceptReview}>Available to Accept at {availableTime}</span>
                                        </button>
                                        
                                        :
                                        ""
                                    }
                                    <div className={styles.orderDetailsTiltle}>Order Details</div>
                                </div>
                                
                                <div className={styles.acceptOrderInformation} >
                                    <div className={styles.acceptOrderInformationCol1}>
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td className={styles.padBottom8}>
                                                        <div className={styles.gridOderDetailsTop}>
                                                            <div className={styles.gridlbl}>Total Order Value:</div>
                                                            <div className={styles.gridNum}>$ {formatToTwoDecimalPlaces(totalOrderValue)}</div>
                                                        </div>
                                                    </td>
                                                    <td className={styles.padBottom8}>
                                                        <div className={styles.gridOderDetailsTop}>
                                                            <div className={styles.gridlbl}>Total Weight:</div>
                                                            <div className={styles.gridNum}>{formatToTwoDecimalPlaces(orderDetail?.total_weight)} LBS</div>
                                                        </div>
                                                    </td>
                                                    <td className={styles.padBottom8}>
                                                        <div className={styles.gridOderDetailsTop}>
                                                            <div className={styles.gridlbl}>Due Date:</div>
                                                            <div className={styles.gridNum}>{deliveryDate}</div>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <div className={styles.gridOderDetailsTop}>
                                                            <div className={styles.gridlbl}>Delivery Destination:</div>
                                                            <div className={styles.gridNum}>{orderDetail?.city}, {state} {orderDetail?.zip}</div>
                                                        </div>
                                                    </td>
                                                    <td colSpan={2}>
                                                        <div className={styles.gridOderDetailsTop}>
                                                            <div className={styles.gridlbl}>Freight Term:</div>
                                                            <div className={styles.gridNum}>{orderDetail?.freight_term} (FOB Destination)</div>
                                                        </div>
                                                    </td>
                                                </tr>   
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div className={clsx(styles.addPoLineTable, orderDetail?.claimed_by === purchaseOrder.pending ? styles.pendingTable : '')}>
                                    <table className={styles.tableGrid}>
                                        <thead>
                                            <tr>
                                                <th><span>LN</span></th>
                                                <th><span>DESCRIPTION</span></th>
                                                <th><span>QTY @ $/UM</span></th>
                                                <th colSpan={2} className={styles.lastTd}><span>TOTAL</span></th>
                                            </tr>
                                        </thead>
                                        <tbody ref={productsContainerRef} onScroll={handleScroll}>
                                            {productListing}
                                        </tbody>
                                    </table>
                               
                                    <table className={styles.totalAmt}>
                                        <tr>
                                            <td><div><span></span></div></td>
                                            <td className={styles.materialTotal}><div><span>Material Total</span></div></td>
                                            <td className={styles.materialTotal}><div><span>$</span></div></td>
                                            <td className={styles.materialTotal1}><div><span> {formatToTwoDecimalPlaces(orderDetail?.seller_po_price)}</span></div></td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td><span className={styles.saleTax}>Sales Tax</span></td>
                                            <td><span className={styles.txtSign}>$</span></td>
                                            <td><span className={styles.txtNumber}>{formatToTwoDecimalPlaces(orderDetail?.seller_sales_tax)}</span></td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td><span className={styles.totalPurchase}>Total Purchase</span></td>
                                            <td><span className={styles.totalPurchaseSign}>$</span></td>
                                            <td><span className={styles.totalPurchaseNumber}>{formatToTwoDecimalPlaces(totalOrderValue)}</span></td>
                                        </tr>

                                    </table>

                                    </div>
                                
                            </div>
                            <div>
                                {!routeData?.isEditMode && <div className={styles.acceptOrderHead} >
                                    {disablePreviousButton ? <>
                                        <button className={clsx(orderDetail?.claimed_by === purchaseOrder.pending ? styles.previewNextPreBtn : "" , styles.btnPreNextPo, disablePreviousButton && styles.nextprevdisabledBtn)} disabled={disablePreviousButton} onClick={previousPage}><span>Prev PO</span></button>
                                    </> :
                                        <>
                                            <button className={clsx(orderDetail?.claimed_by === purchaseOrder.pending ? styles.previewNextPreBtn : "" , styles.btnPreNextPo, disablePreviousButton && styles.nextprevdisabledBtn)} disabled={disablePreviousButton} onClick={previousPage}><span>Prev PO</span></button>

                                        </>
                                    }

                                    {orderDetail?.claimed_by === purchaseOrder.pending ?
                                        ""
                                        :
                                        <button className={styles.acceptOrderBtn} onClick={(event) => handleClickOpen(event)}>
                                            <SubmitApplicationDialog open={openSubmitApp} onClose={($event: any) => handleSubmitClose($event)} acceptOrder={acceptOrder} />
                                            <span>ACCEPT ORDER</span>
                                        </button>
                                    }
                                    {disableNextButton ? <>
                                        <button className={clsx(orderDetail?.claimed_by === purchaseOrder.pending ? styles.previewNextPreBtn : "" , styles.btnPreNextPo, disableNextButton && styles.nextprevdisabledBtn)} onClick={nextPage} disabled={disableNextButton}><span>Next PO</span></button>
                                    </> : <>
                                        <button className={clsx(orderDetail?.claimed_by === purchaseOrder.pending ? styles.previewNextPreBtn : "" , styles.btnPreNextPo, disableNextButton && styles.nextprevdisabledBtn)} onClick={nextPage} disabled={disableNextButton}><span>Next PO</span></button>
                                    </>}

                                </div>
                                }
                            </div>

                        </div>

                        {openReminderYouAreAlmostTherePopup && <ReminderYouAreAlmostTherePopup open={openReminderYouAreAlmostTherePopup} close={() => setOpenReminderYouAreAlmostTherePopup(false)} isReminderPopup={isReminderPopup} />}
                    </>
                    :
                    <OrderConfirmation poNumber={orderDetail?.buyer_po_number} setOpenOrderConfirmation={setOpenOrderConfirmation} />
                }
            </IonContent>
        </IonPage>
    )

}
export default AcceptOrder;