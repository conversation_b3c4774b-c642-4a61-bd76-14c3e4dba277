.buyerorderConfirmationContent {
    padding: 12px 16px;
    border-radius: 0px 0px 10px 10px;
    // -webkit-backdrop-filter: blur(60px);
    // backdrop-filter: blur(60px);
    // background-color: rgba(0, 0, 0, 0.75);
    margin: 0px auto;
    max-width: 600px;
    width: 100%;
    text-align: center;

    .orderConfirmation {
        font-family: Noto Sans;
        font-size: 28px;
        font-weight: bold;
        line-height: 1.6;
        text-align: center;
        margin-bottom: 12px;
        font-weight: 600;
        color: #70ff00;
    }

    .emailHeder {
        .orderEmail {
            font-family: Noto Sans;
            font-size: 16px;
            font-weight: 300;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.6;
            letter-spacing: normal;
            text-align: center;
            color: #fff;
            
         


        }
        .emailIdInvoice {

        font-weight: 600;
        }
        
    }

    .poBoxOne {
        padding: 20px 0px 8px 0px;

        .poNumberCreator {
            font-family: Noto Sans;
            font-size: 18px;
            font-weight: bold;
            line-height: 1.6;
            letter-spacing: 1.8px;
            color: #fff;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        
        }
        .poNumber {
            font-size: 14px;
            font-weight: 300;
            line-height: 1.6;
            padding-left: 8px;
        }
    }

    .poNumber {
        font-family: Noto Sans;
        font-size: 16px;
        font-weight: 300;
        line-height: 1.6;
        text-align: center;
        color: #fff;
    }

    .uploadYourPo1 {
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: Noto Sans;
        color: #fff;
        margin: 12px auto;
        position: relative;

        .uploadYourPoDiv {
            width: 360px;
            height: 64px;
            padding: 8px 0;
            border-radius: 8px;
            background-color: rgba(0, 0, 0, 0.25);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            &:hover {
                border: 1px solid #42ff00;;
            }

        }




        .disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .svgDiv {
            position: absolute;
            right: 65px;
            transition: all 0.1s;

            .questionIcon2 {
                display: none;
                width: 24px;
                height: 24px;
            }

            &:hover {
                .questionIcon1 {
                    display: none;
                }

                .questionIcon2 {
                    display: inline-block;
                }
            }
        }

        input {
            display: none;
        }
    }

    .emailIdInter {
        font-family: Noto Sans;
        font-size: 14px;
        line-height: 1.4;
        text-align: center;
        color: #fff;
        font-weight: 300;
    }

    .uploadYourPo {
        width: 360px;
        height: 64px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-family: Noto Sans;
        padding: 8px 0;
        border-radius: 8px;
        background-color: rgba(0, 0, 0, 0.25);
        color: #fff;
        margin: 12px auto;

        &:hover {
            border: 1px solid #42ff00;;
        }
    }

    .disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .returnInstantPricing {
        font-family: Noto Sans;
        font-size: 16px;
        line-height: 1.6;
        color: #fff;
        margin: 24px auto;
        display: flex;
        justify-content: center;
        text-align: center;
        align-items: center;
        gap: 4px;
        cursor: pointer;

        &:hover {
            color: #70ff00;
        }
    }

    .pointer {
        cursor: pointer;
    }
}

.ConfirmationDialog {
    .dialogContent {
        max-width: 300px;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 30px 34px 30px 34px;
        object-fit: contain;
        border-radius: 10px;
        -webkit-backdrop-filter: blur(24px);
        backdrop-filter: blur(24px);
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
        background-color: rgba(0, 0, 0, 0.72);
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;

        p {
            margin-bottom: 20px;
        }


        .submitBtn {
            transition: all 0.1s;
            font-family: Noto Sans;
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
            color: #70ff00;
            background-color: transparent;
            border: 0;
            opacity: 0.7;

            &:disabled {
                cursor: not-allowed;
                color: #fff !important;
                opacity: 0.5 !important;
            }

            &:hover {
                color: #70ff00;
                opacity: unset;
            }
        }

        .cancelBtn {
            opacity: 0.7;
            font-family: Noto Sans;
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
            color: #fff;
            transition: all 0.1s;
            background-color: transparent;
            border: none;

            &:hover {
                opacity: unset;
            }
        }

        p {
            padding: 20px;
        }
    }

    .actionsTab {
        display: flex;
        width: 100%;

        button {
            width: 50%;
        }
    }
}

.setRatingBox {
    width: 360px;
    height: 64px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-family: Noto Sans;
    padding: 8px 0;
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.25);
    color: #fff;
    margin: 12px auto;
    &:hover {
        border: 1px solid #42ff00;;
    }
}