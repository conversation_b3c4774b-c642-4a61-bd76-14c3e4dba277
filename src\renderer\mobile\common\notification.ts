// @ts-2nocheck 
import { Channel } from 'pusher-js';
import { notificationSchedule } from './notificationHandler';
 
let userId: string | null = null; 
  
export function setNotificationConstants(_userId: string | null){
    userId = _userId; 
   
}

 
function decodeHtmlEntities(html: string) {
    return html.replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");
}

export function markAsReadNotification(newNotification:any){
    let notificationArray = [];
    if(newNotification)
    notificationArray = newNotification;
    
    // if(notificationArray.length > 0)
    // mainWindow?.webContents.send(channelWindow.markAsReadNotification, JSON.stringify(notificationArray));
}

 

export function showNotification(notificationSchema: any) {
     console.log('notificationSchema', notificationSchema)
     const title = notificationSchema.title;
     const message = notificationSchema.body;
      
     notificationSchedule(title,message)
}

const createActionUrl = (navigationSchema: any, notificatonId: string) => {
    let actionUrl = `bryzos://${userId}/${notificatonId}/`;
    if(navigationSchema){
        if(navigationSchema.routePath)actionUrl+=`${navigationSchema.routePath}`;
        const state = navigationSchema.state;
        if(state){
            let stateUrl = '/';
            for(const key in state){
                stateUrl+=`${key}=${state[key]},`;
            }
            actionUrl+=`${stateUrl.substring(0,stateUrl.length-1)}`;
        }
    }
    return actionUrl;
}

 

export const notificationHandler = (notification: any) => {
    showNotification(notification);
    // mainWindow?.webContents.send(channelWindow.markAsReadNotification, JSON.stringify([notification]));
}

export const notificationEventsHandler = (channel: Channel, channelEvents: string[]) => {
    channelEvents.forEach(($event: string) => {
        channel.bind( $event, (data: any) => {
        // console.log('Received notification:', data);
            notificationHandler(data.notification);
        });
    });
}