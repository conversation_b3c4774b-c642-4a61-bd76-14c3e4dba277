import { create } from 'zustand';

export interface Profile { 
    first_name: string, 
    last_name: string, 
    email_id: string, 
    phone: string,
} 

export interface CompanyDetails { 
    company_name: string, 
    client_company: string | null, 
    company_address_line1: string, 
    company_address_city: string,
    company_address_state_id: number,
    company_address_zip: string
}

export interface DeliveryDetails { 
    delivery_address_line1: string, 
    delivery_address_city: string, 
    delivery_address_state_id: number, 
    delivery_address_zip: string,
    delivery_days_add_value: string, 
    send_invoices_to: string, 
    shipping_docs_to: string
}

export interface Payment { 
    bnpl: BNPLModel | null, 
    ach: ACHCredit | null, 
    default_payment_method: string | null;
}

export interface ReceivingHours {
    id: string,
    user_id: number,
    day: string,
    from: number,
    to: number,
    is_active: number,
    display_name: string,
    is_user_available: number,
    created_date: string | null | undefined,
    time_stamp: string | null | undefined,
}

export interface SalesCertificate {
    id: string | undefined,
    user_id: string | undefined,
    state_id: number,
    expiration_date: string,
    cerificate_url_s3: string,
    status: string | undefined,
    file_name: string,
    is_active: number | undefined,
    is_nearing_expiration_email_sent: number | undefined,
    created_date: string | null | undefined,
    time_stamp: string | null | undefined,
    is_deletable: number | undefined,
    company_id: string,
    added_by: string,
}

export interface ACHCredit{
    id: string,
    user_id: string,
    bank_name: string,
    routing_number: string,
    account_number: string,
    truvault_document_id: string | number |null,  //need to confirm type with backend
    is_approved: number,
    is_active: number,
    created_date: string,
    time_stamp: string
}

export interface BNPLModel{
    id: string,
    user_id: number,
    duns: string,
    ein_number: string,
    is_approved: string | number |null,
    requested_credit_limit: string,
    net_terms_days: string | number |null,
    agreed_terms: 0,
    auth_amount_percentage: string | number |null,
    charges_date: string | number |null,
    balance_qualification_link: string | number |null,
    reference_document_id: string,
    payment_info_id: string,
    is_active: number,
    created_date: string,
    time_stamp: string,
    requested_increase_credit: string | number |null,
    is_requested_increase_credit_approved: string | number |null,
    credit_status: string | number |null,
    user_request_increase_credit_id: string | number |null,
    balance_credit_limit: number,
    balance_available_credit_limit: number,
    balance_outstanding_credit_limit: number | null,
    bryzos_credit_limit: number,
    bryzos_available_credit_limit: number,
    bryzos_outstanding_credit_limit: number | null, 
    outstanding_amount: number
}

export interface BuyerSetting{
    id: string,
    user_id: string,
    company_name: string,
    company_id: string|number|null,
    first_name: string,
    last_name: string,
    email_id: string,
    phone: string,
    delivery_address_line1: string,
    delivery_address_line2: string|number|null,
    delivery_address_city: string,
    delivery_address_state_id: 35,
    delivery_address_zip: string,
    delivery_days_add_value: 4,
    send_invoices_to: string,
    shipping_docs_to: string,
    is_active: 1,
    created_date: string,
    time_stamp: string,
    company_address_line1: string,
    company_address_line2: string|number|null,
    company_address_city: string,
    company_address_state_id: 27,
    company_address_zip: string,
    default_payment_method: string|null,
    client_company: string,
    resale_certificate: SalesCertificate[],
    user_delivery_receiving_availability_details: ReceivingHours[],
    ach_credit: ACHCredit | undefined,
    bnpl_settings: BNPLModel | undefined
}

interface BuyerSettingState {
    profileInfo: Profile | null;
    companyInfo: CompanyDetails | null;
    deliveryInfo: DeliveryDetails | null;
    reportsInfo: any | null; // Use the appropriate type for reportsInfo
    paymentInfo: Payment | null;
    documentLibInfo: SalesCertificate[] | null;
    receivingHoursInfo: ReceivingHours[] | null;
    buyerSetting: BuyerSetting | null;
}

const buyerSettingStoreInit: BuyerSettingState = {
    profileInfo: null,
    companyInfo: null,
    deliveryInfo: null,
    reportsInfo: null,
    paymentInfo: null,
    documentLibInfo: null,
    receivingHoursInfo: null,
    buyerSetting: null
}

const useBuyerSettingStore = create<BuyerSettingState>((set) => ({
    ...buyerSettingStoreInit,
    setProfileInfo: (profileInfo: Profile) => set({ profileInfo }),
    setCompanyInfo: (companyInfo: CompanyDetails) => set({ companyInfo }),
    setDeliveryInfo: (deliveryInfo: DeliveryDetails) => set({ deliveryInfo }),
    setPaymentInfo: (paymentInfo: Payment) => set({ paymentInfo }),
    setDocumentLibInfo: (documentLibInfo: SalesCertificate[]) => set({ documentLibInfo }),
    setReceivingHoursInfo: (receivingHoursInfo: ReceivingHours[]) => set({ receivingHoursInfo }),
    setBuyerSettingInfo: (buyerSetting: BuyerSetting) => set({ buyerSetting }),
    resetBuyerSetting: () => set(() => ({
        ...buyerSettingStoreInit
    }))
}));

export default useBuyerSettingStore;