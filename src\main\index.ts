// @ts-nocheck
import { shell, app, BrowserWindow, session, ipcMain, Tray, Menu, nativeImage, screen } from 'electron';
import path from 'path';
import { routes } from "../common";
import electronLocalshortcut from 'electron-localshortcut';
import { isDev } from './helper';
import config from './config';
import Store from 'electron-store';
import pusherInit from './pusher';
import os from 'os';
import axios from 'axios';

const { webpage, internetCheckURL } = config

// import { appProtocol } from '../utils/constant';

let badgeCount = 0;
let zoom = 1;
let setVibrancy;
if (process.platform === 'win32') {
  setVibrancy = require("electron-acrylic-window").setVibrancy;
  }


declare const MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY: string;

const store = new Store();
const storeKeysToClear = ['isOnboardDisable'];   //Add the keys that you want to clear after (CTRL/CMD)+Shift+Backspace
const landingDomain = webpage;
export const channelWindow = {
  setChannelWindows: 'setChannelWindows',
  close: 'windowClose',
  closeNewUpdateWindow: 'new-update-widnow-close',
  doUpdate: 'doUpdate',
  minimize: 'windowMinimize',
  sticky: 'windowSticky',
  updateHeight: 'windowHeight',
  electronVersion: 'electronVersion',
  ready: "updateWindowReady",
  windowStore:'windowStore',
  systemVersion: 'systemVersion',
  zoom: 'zoom',
  pusher: 'pusherNotifier',
  logout: 'logout',
  showNotification: 'showNotification',
  markAsReadNotification: 'markAsReadNotification',
  resetAppCacheAndStorage: 'resetAppCacheAndStorage',
  reloadWindow: 'reload-window',
  badgeCountHandler: 'badge-count',
  isMarkAsReadNotification: 'marked-as-read-notification',
  forceLogout: 'force-logout',
  handleURI: 'handle-uri',
  refreshApp: 'refresh-app',
  openNotificationSetting:'openNotificationSetting',
  productReferenceChanged: 'product-ref-data-changed',
  getAccessToken: 'get-refreshed-token',
  refreshPrivateChannel: 'refresh-private-pusher'
}
// const landingDomain = 'https://d3a6n0zk8sh2q.cloudfront.net'; //staging
// const landingDomain = 'https://d2mzyi51e69tib.cloudfront.net'; //demo
// const landingDomain = 'https://d1vwiimyrqoc4m.cloudfront.net'; //production
// const landingDomain = 'https://d2mzyi51e69tib.cloudfront.net'; //demo

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) app.quit();

// Temporary fix to ensure that we don't face RequestHeaderSectionTooLarge  problem.
// This typically occurs when the user does a force reload.
app.commandLine.appendSwitch('js-flags', '--max-old-space-size=8192');

app.whenReady().then(() => {
  // Increase the maximum size allowed for request headers to 16384 bytes
  // app.session.defaultSession.webRequest.onBeforeSendHeaders(
  //   (details, callback) => {
  //     details.requestHeaders['Content-Security-Policy'] =
  //       'script-src \'self\' \'unsafe-eval\'';
  //     callback({ cancel: false, requestHeaders: details.requestHeaders });
  //   }
  // );


});

let tray;
let iconPath;
export let browserWindow;
export let newUpdateWindow;

if (process.defaultApp) {
  if (process.argv.length >= 2) {
    app.setAsDefaultProtocolClient('bryzos', process.execPath, [path.resolve(process.argv[1])]);
  }
} else {
  app.setAsDefaultProtocolClient('bryzos')
}

const webPreferences = {
  ...(isDev && { webSecurity: false }),
  backgroundThrottling: false,
  nodeIntegration: false, // is default value after Electron v5
  contextIsolation: true, // protect against prototype pollution
  enableRemoteModule: true,
  preload: MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY,
  devTools: isDev
};

let browserWindowUrl = landingDomain;

const createWindow = () => {
   const iconPath = path.join(__dirname, '../../public','favicon.ico');
   let browserWindowHeight;
   let isOnBoardPageDisable = store.get('isOnboardDisable');
   if(!isOnBoardPageDisable){
    browserWindowHeight = 800;
    browserWindowUrl+=routes.onboardingWelcome;
   }else {
    browserWindowHeight = 108;
   }
  // tray = new Tray(iconPath);
  // tray.setToolTip('bryzos');
  createTray();

  function isVibrancySupported() {
    // Windows 10 or greater
    return (
      process.platform === 'win32' &&
      parseInt(os.release().split('.')[0]) >= 10
    )
  }

	let vibrancy = 'dark'

	if (isVibrancySupported()) {
		vibrancy = {
			theme: '#12345678',
			effect: 'acrylic',
			useCustomWindowRefreshMethod: false,
			disableOnBlur: true,
			debug: false,
		}
	}
  const screenWidth = screen.getPrimaryDisplay().workAreaSize.width;
  browserWindow = new BrowserWindow({
    icon: iconPath,
    width: 600,
    height: browserWindowHeight,
    frame: false,
    transparent: false,
    resizable: false,
    vibrancy: vibrancy,
    x: (screenWidth - 600)/2,
    y:50,
    webPreferences
  });
  pusherInit(browserWindow, channelWindow, store);

  if (setVibrancy) {
  setVibrancy(browserWindow, vibrancy);
  }


  electronLocalshortcut.register(browserWindow, process.platform === 'darwin' ? 'Cmd+Shift+Backspace' : 'Ctrl+Shift+Backspace', clearData);
  electronLocalshortcut.register(browserWindow, process.platform === 'darwin' ? 'Cmd+R' : 'Ctrl+R', checkInternetAccessAndLoadPage);
  browserWindow.webContents.on('before-input-event', (event, input) => {
    // Check if the F11 key was pressed
    if (input.key === 'F11') {
      // Prevent the default behavior (i.e., entering full-screen mode)
      event.preventDefault();
    }
    else if (input.control && input.shift && input.code === 'Equal') { //prevent default app zoom-in for ctrl+shift+"="
      event.preventDefault();
    }
    else if(((process.platform === 'win32' && input.control) || (process.platform === 'darwin' && input.meta)) && (input.key === '-' || input.key === '=' || input.key === '0' || input.key === '+')){
      let isResetZoom = false;
      if(input.key === '0')
      isResetZoom = true;
      appZoom(input, event, isResetZoom);
    }
  });

  const appZoom = (input, event, isResetZoom)=>{
    const { width, height } = browserWindow.getBounds();
    let newZoom = 1;
    if(!isResetZoom && input)
    newZoom = input.key === '-' ? (zoom - .1) :  (zoom + .1);
    const newWidth = Math.round(width/zoom * newZoom);
    const newHeight = Math.round(height/zoom * newZoom);
    if(newZoom >= 0.5 && newZoom <= 1){
      zoom = +newZoom;
      browserWindow?.setContentSize(newWidth,newHeight);
      browserWindow?.webContents.send(channelWindow.zoom, zoom);
    }
    event?.preventDefault();
  }

  checkInternetAccessAndLoadPage();


 if (isDev) browserWindow.webContents.openDevTools({ mode: 'detach' });
 
  browserWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  if (!tray) {
    createTray();
  }

  browserWindow.on("close", ev => {
    ev.sender.hide();
    ev.preventDefault();
  });

  let isDragging = false; // Track if the window is being dragged

  browserWindow.on('resize', (event) => {
    if (isDragging) {
      event.preventDefault(); // Prevent resizing when dragging
    }
  });

  browserWindow.on('will-resize', () => {
    isDragging = true; // Set isDragging to true when dragging starts
  });

  browserWindow.on('move', () => {
    if (!isDragging) {
      return; // Ignore move event if not dragging
    }
    browserWindow.once('mouseup', () => {
      isDragging = false; // Reset isDragging on mouseup event
    });
    browserWindow.once('mouseleave', () => {
      isDragging = false; // Reset isDragging on mouseleave event
    });
  });

  app.on('before-quit', ev => {
    browserWindow.removeAllListeners("close");
    browserWindow = null;
  });

  browserWindow.on('will-move', () => {
    
    if (setVibrancy && isVibrancySupported()) {
      setVibrancy(browserWindow, null);
      browserWindow.setBackgroundColor('rgba(0, 0, 0, 0.75)');
    }
  });

  browserWindow.on('moved', () => {
    if (setVibrancy) {
      setVibrancy(browserWindow, vibrancy);
    }
  });  

  browserWindow.webContents.on('did-finish-load', () => {
    // console.log(path.join(__dirname, '../../public', 'badgeCount.png'));
    // browserWindow.setOverlayIcon(path.join(__dirname, '../../public', 'badgeCount.png'), 'Description of the overlay');
    setTimeout(() => {
      if(zoom === 1)
      appZoom(null, null, true);
    }, 500);
  });
};

const checkInternetAccessAndLoadPage = async () => {
  if(browserWindow){
    try {
      browserWindowUrl = landingDomain;
      await axios.get(internetCheckURL);
      if(!setLoadingPage('check')){
        browserWindowUrl+=routes.onboardingWelcome;
      }
      browserWindow.loadURL(encodeURI(browserWindowUrl+'?v='+new Date().getTime()));
    } catch (error) {
        animateResize(browserWindow,600,800,200)
        browserWindow.loadFile(path.join(__dirname, 'public/asset', 'no-internet.html'));
    }
  }
}

const createTray = () => {
  iconPath = nativeImage.createFromPath(path.join(__dirname, 'public/asset', 'favicon.ico'));

  
  tray = new Tray(iconPath);
  tray.setToolTip('bryzos');

  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Open Bryzos',
      click: () => {
        browserWindow.show();
        showWindowsBadge(badgeCount);
      }
    },
    {
      role: "quit"
    }
  ]);

  tray.setContextMenu(contextMenu);
}
export const createNewUpdateWindow = () => {
  browserWindow.webContents.executeJavaScript('window.screenX').then((offsetX)=>{
    browserWindow.webContents.executeJavaScript('window.screenY').then((offsetY)=>{
      newUpdateWindow = new BrowserWindow({
        width: 400 * zoom,
        height: 355 * zoom,
        frame: false,
        transparent: true,
        resizable: false,
        modal: true,
        show: false,
        parent: browserWindow, 
        webPreferences: webPreferences,
        x: (offsetX + 100 * zoom),
        y: (offsetY),
      });
      newUpdateWindow.loadURL(landingDomain + routes.newUpdate);
      newUpdateWindow.once("ready-to-show", () => {
        newUpdateWindow.show();
      });  
      if (isDev) newUpdateWindow.webContents.openDevTools({ mode: 'detach' });
      
      newUpdateWindow.on('closed', () => {
        // @ts-ignore
        newUpdateWindow = null;
      });
      newUpdateWindow.webContents.on('did-finish-load', () => {
        setTimeout(() => {
          newUpdateWindow?.webContents.send(channelWindow.zoom, zoom);
        }, 500);
      });
    })
  })
}
 

function toggleWindow() {
  if (!browserWindow) return;
  if (browserWindow.isVisible()) browserWindow.hide();
  else {
    browserWindow.show();
  }
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => { 
  if(browserWindow)
  electronLocalshortcut.unregisterAll(browserWindow);
  if (process.platform !== 'darwin') {
    app.dock.hide();
  }
});

// url-handler
// https://www.electronjs.org/docs/latest/tutorial/launch-app-from-url-in-another-app
// if (process.defaultApp) {
//   if (process.argv.length >= 2) {
//     app.setAsDefaultProtocolClient(appProtocol, process.execPath, [
//       path.resolve(process.argv[1]),
//     ]);
//   }
// } else {
//   app.setAsDefaultProtocolClient(appProtocol);
// }

const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', (_event, argv) => {
    console.log("OPEING SECOND INSTANCE",argv);
    // Someone tried to run a second instance, we should focus our window.
    if (browserWindow) {
      const lastArg = argv[argv.length - 1];
      if (lastArg) {
        browserWindow.webContents.send(channelWindow.handleURI, lastArg);
      }
      browserWindow.setAlwaysOnTop(true);
      console.log('mini',browserWindow.isMinimized())
      if (browserWindow.isMinimized()) browserWindow.restore();
      console.log('isvisible',browserWindow.isVisible())
      if(!browserWindow.isVisible()){
        browserWindow.show();
      }
      if(badgeCount>0)
      showWindowsBadge(badgeCount);
      setTimeout(()=>{
        browserWindow.setAlwaysOnTop(false);
      },1000)
    }
  });

  // Create mainrWindow, load the rest of the app, etc...
  app.whenReady().then(() => {
    // Detect and use proxy settings
    const defaultSession = session.defaultSession;
    defaultSession.resolveProxy(landingDomain, (proxy) => {
      if (proxy === 'DIRECT') {
        console.log('No proxy detected');
      } else {
        const [type, host, port] = proxy.split(':');

        // Configure the session to use the proxy
        defaultSession.setProxy({ proxyRules: proxy }, () => {
          console.log('Proxy settings updated');
        });
      }
    });

    createWindow();
    if(!isDev){
      const exeName = path.basename(process.execPath);
      app.setLoginItemSettings({
      openAtLogin: true,
      path: process.execPath,
      args: [
      '--processStart', `${exeName}`
      ]
      });
    }
    app.on('activate', () => {
      if (browserWindow === null) createWindow();
    });

    // Get the default session object
    // const defaultSession = session.defaultSession;

    // Intercept all requests
    defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
      // Add the "Origin" header to the request
      details.requestHeaders['Origin'] = landingDomain;

      // Call the callback with the updated headers
      callback({ cancel: false, requestHeaders: details.requestHeaders });
    });
  
  });

  app.on('open-url', (event, url) => {
    event.preventDefault();
    // toggleWindow();
    console.log("Open Url Event", url);
    browserWindow?.webContents.send(channelWindow.handleURI, url);
    if (browserWindow?.isMinimized()) browserWindow.restore();
    if(!browserWindow?.isVisible()){
        browserWindow?.show();
    }
    // let data = url.split('=')[1];
    // data = decodeURIComponent(data);

    // browserWindow?.webContents.send('auth-data', data);
  });
  
}

ipcMain.on(channelWindow.openNotificationSetting, (event, data) => {
  if(data){
    if (os.platform() === "win32") {
      shell.openExternal('ms-settings:notifications');
    } else if (os.platform() === "darwin") {
      shell.openExternal('x-apple.systempreferences:com.apple.preference.notifications');
    }
    store.set('isNotificationEnabled',true)
  }else{
    event.returnValue = store.get('isNotificationEnabled') ?? false;
  }
});

ipcMain.on(channelWindow.setChannelWindows, (event, data) => {
  event.returnValue = channelWindow;
});

ipcMain.on(channelWindow.close, () => {
  browserWindow.close()
})

ipcMain.on(channelWindow.closeNewUpdateWindow, () => {
  newUpdateWindow.close();
});

ipcMain.on(channelWindow.minimize, () => {
    browserWindow.minimize();
});

ipcMain.on(channelWindow.reloadWindow, (event, data) => {
  if(browserWindow)
  checkInternetAccessAndLoadPage();
});

ipcMain.on(channelWindow.badgeCountHandler, (event, data) => {
  if(data.type === 'reset'){
    badgeCount=0;
    showWindowsBadge(0)
  }
  else if(data.type === 'set'){
    badgeCount = data.count;
    showWindowsBadge(badgeCount);
  }
  else{
    badgeCount++;
    showWindowsBadge(badgeCount);
  }
});

ipcMain.on(channelWindow.sticky, (event, data) => {
  if(data===null){
    try {
      const currentAlwaysOnTopValue = browserWindow.isAlwaysOnTop();
      let toggleAlwaysOnTopValue = !currentAlwaysOnTopValue;
  
      browserWindow.setAlwaysOnTop(toggleAlwaysOnTopValue);
  
      event.returnValue = toggleAlwaysOnTopValue;
    } catch (error) {
      console.error(error);
      event.returnValue = false; // set the return value to false if an error occurred
    }
  }
  else {
    event.returnValue = "Not 1st app release";
  }
});

ipcMain.on(channelWindow.windowStore, (event, data) => {
  event.returnValue =  setLoadingPage(data) ;
});

ipcMain.on(channelWindow.refreshApp, (event, data) => {
  console.log("Refresh app");
  if(browserWindow)browserWindow.reload();
});

const setLoadingPage = (data) => {
  let isOnBoardPageDisable = store.get('isOnboardDisable');
  isOnBoardPageDisable = store.get('isOnboardDisable');
  if(data === 'check'){
    return isOnBoardPageDisable ? true : false ;
  }else if(data === 'set'){
    store.set('isOnboardDisable',true)
  }
}

ipcMain.on(channelWindow.systemVersion, (event, data) => {
  event.returnValue = getSystemVersion();
});

const getSystemVersion = ()=> {
  if(os.platform() === "win32"){
    if(os.release() < '10.0.22000'){
      return 'Windows 10';
    }else{
      return 'Windows 11';
    }
  }else if(os.platform() === "darwin"){
    if(os.arch() === 'x64'){
      return 'Mac Intel';
    }else{
      return 'Mac ARM';
    } 
  }
}

ipcMain.on(channelWindow.electronVersion, (event) => {
  event.returnValue = app.getVersion();
});

ipcMain.on(channelWindow.updateHeight, (event,  height) => {
    animateResize(browserWindow,600, Math.round(height),200);
});

let intervalId = null
function animateResize(win, width, height, duration = 200) {
  if(getSystemVersion() === 'Windows 10')
  win.setContentSize(Math.round(width*zoom),Math.round(height*zoom));
  // console.log("ANIMATE>>",width,height,zoom);
  else {
    if (intervalId !== null) {
      clearInterval(intervalId);
    }
    const startSize = win.getSize();
    const targetSize = [width, height];
    const sizeDiff = [
      targetSize[0] - startSize[0],
      targetSize[1] - startSize[1],
    ];
    const interval = 10;
    const steps = duration / interval;
    let stepCount = 0;
    intervalId = setInterval(() => {
      stepCount++;
      const progress = stepCount / steps;
      const newSize = [
        startSize[0] + sizeDiff[0] * progress,
        startSize[1] + sizeDiff[1] * progress,
      ];
      win.setContentSize(Math.round(width*zoom),Math.round(newSize[1]*zoom));
      browserWindow?.webContents.send(channelWindow.zoom , zoom);
      if (stepCount >= steps) {
        if (intervalId !== null){
          clearInterval(intervalId);
          intervalId = null;
        }
      }
    }, interval);
  }
}

const showWindowsBadge = (count)=>{
  if(os.platform() === "darwin")
    app.setBadgeCount(count);
  else
    app.setBadgeCount(count);
}

const clearData = async () => {
  // resetAppNotification();
  // Clear cookies
  const cookies = session.defaultSession.cookies;
  
  await cookies.flushStore();
  session.defaultSession.clearStorageData({
    storages: ['cookies']
  }, () => {
    console.log('All cookies cleared');
  });
  for(const key of storeKeysToClear){
    store.delete(key);
  }
  console.log("Trying to clear data");
  // Clear local storage and session storage
  browserWindow.webContents.executeJavaScript(`
    localStorage.clear();
    sessionStorage.clear();
  `);
  app.relaunch();
  app.quit();
}
 
import updater from './update';
updater()