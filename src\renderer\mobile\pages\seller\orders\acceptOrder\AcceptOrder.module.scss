.orderAcceptDrawer {
  .orderAcceptContent {
    padding: 16px 20px 44px 20px;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .orderInnerContent {
    padding-top: 40px;
    height: 100%;
  }

  .closePopupBtn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 9;
  }

  .importantBtn {
    width: 100%;
    height: 56px;
    align-self: stretch;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-radius: 8px;
    background-color: #d9d9d9;
    font-family: Noto Sans Display;
    font-size: 20px;
    font-weight: normal;
    line-height: 1.6;
    text-align: center;
    color: #000;
  }

  ul {
    padding-left: 24px;
    margin-top: 20px;
    margin-bottom: 30px;

    li {
      font-family: Noto Sans Display;
      font-size: 14px;
      font-weight: 300;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      margin-bottom: 12px;
    }
  }

  p {
    margin-bottom: 15px;
    margin-left: 20px;
  }

  .closeIcon {
    position: absolute;
    top: 10px;
    right: 12px;
    cursor: pointer;

    svg {
      height: 25px;
      width: 25px;
      color: white;

      path {
        fill: #fff
      }
    }
  }


  .flx {
    display: flex;
    justify-content: center;
    margin-top: auto;

    .submitBtn {
      width: 100%;
      height: 48px;
      border-radius: 100px;
      transition: all 0.1s;
      font-family: Noto Sans;
      font-size: 16px;
      line-height: 1.6;
      text-align: center;
      color: #000;
      background-color: #70ff00;
      border: 0;
      border: 0.5px solid transparent;
      padding: 0px 5px 0px 5px;

      &:disabled {
        cursor: not-allowed;
        opacity: 0.7;
      }
    }

  }

}



.acceptOrderContent {
  padding: 0px 0px 0px 0px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .orderDetailContent {
    height: 100%;
  }

  .acceptOrderHead {
    margin-top: auto;
    padding: 0px 16px;
  }

}

.headerTop {
  padding: 0px 16px;
}

.heading {
  font-family: Noto Sans Display;
  font-size: 16px;
  font-weight: normal;
  line-height: 1.4;
  text-align: center;
  color: #fff;
  padding: 12px 0px;
  margin-top: 0px;
  border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;

  span {
    margin: 0 auto;
  }
}


.orderDetailsTiltle {
  font-family: Noto Sans Display;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.4;
  text-align: left;
  color: #fff;
  padding-bottom: 12px;
  border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
}

.acceptOrderInformation {
  padding: 8px 16px;

  table {
    width: 100%;
  }
}

.acceptOrderInformationCol1 {

  .gridOderDetailsTop {

    font-family: Noto Sans Display;
    text-align: left;
    color: #fff;

    .gridlbl {
      opacity: 0.7;
      font-size: 10px;
      font-weight: 300;
      line-height: 1.4;
      margin-bottom: 4px;
    }

    .gridNum {
      font-size: 12px;
      font-weight: normal;
    }
  }
}

.tableGrid {
  width: 100%;
  display: inline-grid;
  grid-template-areas:
  "head-fixed"
  "body-scrollable"
  "foot-fixed";

thead {
  grid-area: head-fixed;
}

  tbody {
    grid-area: body-scrollable;
    tr {
      &:nth-child(even) {
        background-color: rgba(0, 0, 0, 0.15);
        td {

          &:nth-child(2),
          &:nth-child(3) {
              color: rgba(255, 255, 255, 0.67);
          }
      }
      }
    }
  }

  tr {
    th {
      height: 21px;
      padding: 2px 3px 1.7px 3px;
      background-color: rgba(0, 0, 0, 0.4);
      font-family: Noto Sans Display;
      font-size: 12px;
      font-weight: 500;
      line-height: 1.4;
      text-align: center;
      color: #fff;

      &:nth-child(2) {
        text-align: left;
      }

      &.lastTd {
        text-align: right;
        padding-right: 10px;
      }
    }
  }

  tr {
    td {
      font-family: Noto Sans Display;
      padding: 10px 3px;
      font-size: 12px;
      font-weight: 300;
      line-height: 1.2;
      text-align: left;
      color: #fff;
      vertical-align: top;
      border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);

      .tagLbl {
        margin-top: 4px;
        display: block;
      }

      .domesticMaterialLbl {
        color: #70ff00;
        margin-top: 2px;
        display: block;
        font-size: 10px;
      }

      &.qtyTd {
        text-align: center;
      }

      &:first-child {
        font-size: 16px;
        font-weight: 500;
        line-height: 1;
        text-align: center;
        color: rgba(255, 255, 255, 0.8);
        vertical-align: text-top;
      }

      &.lastTd {
        text-align: right;
        padding-right: 10px;
      }
    }
  }

}


.addPoLineTable {

  
  table {

    thead,
    tbody {
      tr {

        td,
        th {
          &:first-child {
            width: 10%;
          }

          &:nth-child(2) {
            width: 42%;
          }

          &:nth-child(3) {
            width: 24%;
            text-align: center;
          }

          &:nth-child(4) {
            width: 24%;
            text-align: right;
            padding-right: 10px;
          }
        }
      }
    }
  }

  thead {
    width: 100%
  }

  tbody {
    display: block;
    overflow-y: auto;
    height: calc(100vh - 435px);
    min-height: 150px;

    tr {
      &:last-child {
        td {
          border-bottom: 0px;
        }
      }
    }
  }

  thead,
  tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
  }

}


.totalAmt {
  padding: 11px 10px;
  grid-area: foot-fixed;
  width: 100%;

  tr:is(:first-child) {
      td {
          padding: 2px 0px;
          &:first-child {
              text-align: right;
              span {
                  padding-right: 0px;
              }
          }

          &:last-child {
              span {
                  padding-right: 10px;
              }
          }


          &:nth-child(3) {
              text-align: center;
              width: 25px;
              span {
                  padding-right: 0px;
              }
          }

          div {
              display: block;
              padding: 2px 0px;
              border-top: 0.5px solid rgba(255, 255, 255, 0.3);
              border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
              background-color: rgba(0, 0, 0, 0.6);
              height: 20px;
          }

        

          border-top:0.5px solid rgba(255, 255, 255, 0.3);
          border-bottom:0.5px solid rgba(255, 255, 255, 0.3);

          &.materialTotal {
              flex-grow: 0;
              font-family: Noto Sans Display;
              font-size: 12px;
              font-weight: 300;
              line-height: 1.2;
              text-align: right;
              color: rgba(255, 255, 255, 0.8);
          }

          &.materialTotal1 {
              white-space: nowrap;
              font-weight: 500;
          }

          &:nth-child(3) {
              text-align: center;
          }
      }
  }

  tr {
      &:last-child {
          td {
              padding-bottom: 0px;
          }
      }

      td {
          padding: 4px 0px 0px 0px;
          font-family: Noto Sans Display;
          font-size: 12px;
          font-weight: 300;
          line-height: 1.2;
          text-align: right;
          color: rgba(255, 255, 255, 0.8);

          &:last-child{
            width: 80px;
          }
          
          &:nth-child(3){
              text-align: center;
          }
          .saleTax{
            font-weight: 500;
          }
          .txtSign {
              display: block;
              text-align: center;
              padding: 0px 10px;
              font-weight: 500;
          }

          .txtNumber {
              font-weight: 600;
              padding-right: 10px;
          }

          .totalPurchase,
          .totalPurchaseSign,
          .totalPurchaseNumber {
              font-size: 20px;
              font-weight: 600;

          }

          .totalPurchaseSign {
              font-weight: 600;
          }

          .totalPurchaseNumber {
              font-weight: 600;
              padding-right: 10px;
              width: 60px;
          }
      }
  }
}


.acceptOrderHead {
  width: 100%;
  display: flex;
  gap: 8px;

  button {
    &:disabled {
      color: rgb(255, 255, 255);
      border: solid 0.5px rgb(255, 255, 255);
      background-color: rgba(0, 0, 0, 0.5);
      opacity: 0.2;
      font-weight: 300;
      cursor: not-allowed;

      &:hover {
        border: solid 0.5px rgb(255, 255, 255);
      }
    }
  }

  .btnPreNextPo {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 0;
    width: 56px;
    height: 48px;
    align-self: stretch;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 0 10px;
    border-radius: 2px;
    border: solid 0.8px rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.2);
    font-family: Noto Sans Display;
    font-size: 12px;
    font-weight: 300;
    line-height: 1.2;
    text-align: center;
    color: #fff;
  }

  .acceptOrderBtn {
    height: 48px;
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 2px 10px;
    border-radius: 2px;
    background-color: #70ff00;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border: 0;
    font-family: Noto Sans Display;
    font-size: 16px;
    font-weight: normal;
    line-height: 1.2;
    letter-spacing: 1.6px;
    text-align: center;
    color: #000;

  }

  .previewNextPreBtn {
    width: 100%;
    height: 44px;
    border-radius: 8px;
    border: 0;
  }
}

.missPopupText {
  font-family: Noto Sans Display;
  font-size: 14px;
  font-weight: 300;
  line-height: 1.4;
  text-align: left;
  color: #fff;
  margin-top: 20px;
}

.closePopupBtn1 {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 9;
}

.missPopupMain {
  padding: 40px 20px 24px 20px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .doneBtn {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    padding: 10px 24px;
    border-radius: 8px;
    border: #70ff00;
    background-color: #70ff00;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    text-align: center;
    color: #000;
    margin-top: auto;
  }
}

.youJustMissetext {
  font-family: Noto Sans Display;
  font-size: 20px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #fff;
}

.orderPreviewBtn {
  padding: 8px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.2);
  font-family: Noto Sans Display;
  font-size: 16px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 1.6px;
  text-align: center;
  color: #fff;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
  margin-bottom: 12px;

  .leftIcon {
    position: absolute;
    left: 8px;
  }

  .rightIcon {
    position: absolute;
    right: 8px;
  }

  svg {
    width: 24px;
    height: 24px;
  }

  .acceptReview {
    font-size: 10px;
    letter-spacing: normal;
  }
}

.padBottom8 {
  padding-bottom: 8px;
}

.pendingTable{
  table{
    tbody {
      height: calc(100vh - 490px);
    }
  }
}