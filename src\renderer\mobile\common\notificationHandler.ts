import { LocalNotifications } from '@capacitor/local-notifications';


export const notificationSchedule = (title:string, body:string, extra: any) => {
    const randomId = Math.floor(Math.random() * 10000) + 1;

    LocalNotifications.schedule({
      notifications: [
        {
          smallIcon: 'res://drawable/widget_icon',
          title ,
          body ,
          id: randomId,
          extra,
          schedule: {
            at: new Date(Date.now() + 1000)
            // repeats: true,
            // every: "minute"
          }
        }
      ]
    });
  }