import { encryptData, useGlobalStore } from '@bryzos/giss-ui-library';
import { Directory, Filesystem } from '@capacitor/filesystem';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Auth } from 'aws-amplify';
import { reactQueryKeys } from '../common';
import { unRegister } from '../../common/pushNoti';
import { getUserAppData, removeAutoCred, stringifyError } from '../helper';
import useSaveLoginActivityAnalytics from './useSaveLoginActivityAnalytics';
import { useIonRouter } from '@ionic/react';

const useOnSubmitLogin = (setError) => {
  const store = useGlobalStore.getState();
  const query = useQueryClient();
  const saveLoginActivityAnalytics = useSaveLoginActivityAnalytics()
  const router = useIonRouter();

  return useMutation(
    async (payload) => {
      try {
        const { email, password} = payload
        const user = await Auth.signIn(email, password);
        if (user.challengeName === 'NEW_PASSWORD_REQUIRED') {
          const completeUser = await Auth.completeNewPassword(user, password);
        }
        const cred = await encryptData(JSON.stringify({ email, password }),store.decryptionEntity.decryption_key);
        await Filesystem.writeFile({
          path: 'user-data.txt',
          data: cred,
          directory: Directory.Cache,
        });
        store.setCurrentAccessToken(user.signInUserSession.accessToken.jwtToken);
        store.setIsUserLoggedIn(true);
        query.invalidateQueries([reactQueryKeys.cognitoUser]);
      } catch (error) {
        const data = {
          ...getUserAppData(),
          reason:{error:stringifyError(error), 
          isManualLogin:store.isManualLogin}, 
          email:payload.email
        }
        saveLoginActivityAnalytics.mutate(data)
        store.setSignupUser(null);
        unRegister();
        store.setShowLoader(false);
        if (error.name === 'NotAuthorizedException'){
          if(error?.message === "Password attempts exceeded"){
            removeAutoCred()
            setError({message: "Password attempts exceeded"})
          }else{
            setError({message: 'Wrong username or password'})
          }
        }
        router.push('/login', { animate: true, direction: 'forward' })
      }
    },
    {
      retry: 4,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      staleTime: 0,
    }
  );
};


export default useOnSubmitLogin;