import { CircularProgress } from '@mui/material';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import styles from './Loader.module.scss';

const Loader = (props:any) => {
    const {showLoader} = useGlobalStore();
    return (
        <>
          {
          (showLoader || props.showLoader === true) &&
            <>
              <div className={styles.backDrop}></div>
              <div className={styles.LoaderMain}>
                <div className={styles.dotPulse}></div>
              </div>
            </>
            
          }
        </>
    );
};

export default Loader;