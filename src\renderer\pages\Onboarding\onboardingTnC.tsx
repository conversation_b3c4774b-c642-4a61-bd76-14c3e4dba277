
// @ts-nocheck
import { useLocation, useNavigate } from 'react-router';
import { useHeightListener } from '../../hooks/useHeightListener';
import styles from './onboardingTnC.module.scss'
import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { fileType, routes } from '../../../common';
import OnboardingFooter from './onboardingFooter';
import { useStore } from '../../helper/store';
import { downloadFiles } from '../../helper';
import { Dialog } from '@mui/material';


function OnboardingTnc() {
    const ref = useHeightListener();
    const navigate = useNavigate();
    const location = useLocation();
    const [tncCheckbox, setTncCheckBox] = useState(false);
    const [tncCheckboxDisable, setTncCheckBoxDisable] = useState(false);
    const [tncData, setTncData] = useState({});
    const [openErrorDialog, setOpenErrorDialog] = useState(false);
    const setShowLoader = useStore(state => state.setShowLoader);
    const data = location.state;

    useEffect(() => {
        const handleTncData = async () =>{
            try{
                const requestOptions = {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                };
                const response = await fetch(import.meta.env.VITE_API_SERVICE + '/reference-data/homepage', requestOptions);
                const responseData = await response.json();
                responseData.ref_bryzos_terms_conditions.forEach(termsAndCondition => {
                    if(data.userType === termsAndCondition.type){
                        setTncData(termsAndCondition)
                    }
                })
            }catch (error) {
                console.error('onboarding tnc error',error)
            }
        }
        setShowLoader(true)
        handleTncData();
    },[])
    const handleOnboardingSubmit = async () => {
        try{
            setTncCheckBoxDisable(false)
            if(data){
                const payload = {
                    data: {
                        user_type: data.userType,
                        company_name: data.companyName,
                        first_name: data.firstName,
                        last_name: data.lastName,
                        email_id: data.emailAddress,
                        bryzos_terms_condtion_id: tncData.id,
                        accepted_terms_and_condition: tncData.terms_conditions_version,
                        zip_code: data.zipCode,
                        client_company: data.companyEntity,
                    }
                }
                const requestOptions = {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                };
                const response = await fetch(import.meta.env.VITE_API_SERVICE + '/user/onBoard', requestOptions);
                const responseData = await response.json();
                if(responseData.data.error_message){
                    navigate(routes.onboardingDetails, { state: {data, errorMessage: responseData.data.error_message, tncCheckbox, tncData}})
                }else{
                    navigate(routes.onboardingThankYou);
                }
            }else{
                navigate(routes.onboardingDetails);
            }
        }catch (error) {
            console.error('onBoarding error',error)
            setTncCheckBoxDisable(true)
        }

    }
    const [tnc, setTnc] = useState('');
    const FetchHtml = async () => {
        const response = await fetch(tncData.cloudfront_url);
        return await response.text(); // Returns it as Promise
    };
    const SetHtml = async () => {
        await FetchHtml().then((text) => {
            setTnc(text);
            setShowLoader(false)
        });
    };
    useEffect(() => {
        if(Object.keys(tncData).length !== 0){
            SetHtml(true)
        }
    },[tncData]);

    const downloadReports = (fileUrl, fileName, fileType) => {
        const showError = downloadFiles (fileUrl, fileName, fileType)
        showError.then(res => {
            if (res) {
                setOpenErrorDialog(false);
            } else {
                setOpenErrorDialog(true);
            }
        })

    }
    return (
        <div className={clsx(styles.tncBox, 'bgBlurContent')} ref={ref}>
            <div className={styles.tnCInnerContent}>
            <div className={clsx(tncCheckbox && styles.onboardingBlurClass, styles.onboardingLogo)}>
                <img src='/onboardingLogo.png' />
            </div>
            <div className={clsx(styles.trial, tncCheckbox && styles.onboardingTnCBody)}>
                <div className={styles.tnCPage}>
                    <div className={styles.tncScrollClass}>
                        <h2 className={styles.TermsofUseV1}>Bryzos Instant Pricing Desktop Widget <br />
                            Terms of Use ({tncData?.terms_conditions_version?.toUpperCase()})</h2>
                        {/* <p>Please read these Bryzos Instant Pricing Desktop Widget Terms of Use (these "Terms") carefully as they govern your use of (which includes access to) Bryzos’ Instant Pricing Desktop Widget (the “Widget”).</p> */}
                        {/* <p>Use of the Widget may be subject to additional terms and conditions presented by Bryzos, which are hereby incorporated by this reference into these Terms.</p> */}
                        <div className='tncOnboarding'>
                            <div dangerouslySetInnerHTML={{ __html: tnc }}></div>
                        </div>
                        
                    </div>
                </div>

                <div className={clsx(styles.checkingThisbox, 'onboardingChk')}>
                    <label className='containerChk'>
                        <input type='checkbox' onChange={(e) => {setTncCheckBox(e.target.checked); setTncCheckBoxDisable(e.target.checked)}} checked={tncCheckbox} />
                        <span className='checkmark' />
                        <span className='lblChk'>
                            By checking this box I am confirming on behalf of myself, and the company which I represent that I have read, understand and agree to the above terms and conditions.
                        </span>
                    </label>
                </div>
            </div>

            </div>
            <div className={styles.btnSectionTnC}>
            <div className={styles.onBoradingDownloadTnC} onClick={() => { downloadReports(import.meta.env.VITE_FILE_URL_DOWNLOAD_TNC_PDF, 'Tnc', fileType.pdf) }}>
                    Click here to download T&C
                </div>
            </div>
            
            <div className={clsx(tncCheckbox && styles.onboardingTnCBody)}><button className={clsx(styles.getstarttext,styles.GetStartedbtn)} disabled={!tncCheckboxDisable}  onClick={handleOnboardingSubmit}>Get Started</button></div>
            <OnboardingFooter />
            <>
                <Dialog
                    open={openErrorDialog}
                    onClose={(event) => setOpenErrorDialog(false)}
                    transitionDuration={200}
                    hideBackdrop
                    classes={{
                        root: styles.ErrorDialog,
                        paper: styles.dialogContent
                    }}

                >
                    <p>No data found. Please try again in sometime</p>
                    <button className={styles.submitBtn} onClick={(event) => setOpenErrorDialog(false)}>Ok</button>
                </Dialog>
            </>
        </div>
    );
}
export default OnboardingTnc;
