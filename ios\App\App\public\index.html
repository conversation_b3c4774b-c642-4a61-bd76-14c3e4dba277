<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/Widget_Icon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, viewport-fit=cover" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="/Widget_Icon.png" />
    <link href="https://fonts.googleapis.com/css?family=Noto+Sans:300,400,500,600,700,900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Syncopate:300,400,500,600,700,900&display=swap" rel="stylesheet">
    <link href='https://fonts.googleapis.com/css?family=Noto+Sans+Display:300,400,500,600,700&display=swap' rel='stylesheet' />
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Syncopate:300,400,500,600,700,900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Bryzos - Pricing Widget</title>
    <script type="module" crossorigin src="/assets/index-********.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-96deee04.js">
    <link rel="stylesheet" href="/assets/index-e7b72095.css">
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    
    <script>
      window.global = window;
      var exports = {};
    </script>
  </body>
</html>
