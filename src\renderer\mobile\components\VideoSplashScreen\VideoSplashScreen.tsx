// import React, { useEffect, useRef } from 'react';
// import IntroVideo from "../../../assets/mobile-images/welcomepage.mp4";
// import flashLogo from "../../../assets/mobile-images/BG_APP.svg"

// interface VideoSplashScreenProps {
//   onVideoEnd: () => void;
// }

// const VideoSplashScreen: React.FC<VideoSplashScreenProps> = ({ onVideoEnd }) => {
//   const videoRef = useRef<HTMLVideoElement>(null);

//   useEffect(() => {
//     const video = videoRef.current;
//     if (!video) return;

//     const handleVideoEnd = () => {
//       onVideoEnd();
//     };

//     const skipVideo = () => {
//       handleVideoEnd();
//     };

//     video.addEventListener('ended', handleVideoEnd);
//     video.addEventListener('click', skipVideo);

//     // Clean up
//     return () => {
//       video.removeEventListener('ended', handleVideoEnd);
//       video.removeEventListener('click', skipVideo);
//     };
//   }, [onVideoEnd]);

//   return (
//     <video 
//       ref={videoRef} 
//       autoPlay 
//       muted
//       preload="auto"
//       playsInline
//       poster={flashLogo}
//       style={{ width: '100%', height: '100%', objectFit: 'cover' }}
//     >
//       {/* <source src="/asset/welcomepage.mp4" type="video/mp4" /> */}
//       <source src={IntroVideo} type="video/mp4" />
//       Your browser does not support the video tag.
//     </video>
//   );
// };

// export default VideoSplashScreen;
