// @ts-nocheck
import { useEffect, useState, useRef } from 'react';
import styles from './VideoLibrary.module.scss';
import { useGetAllVideoLibraryTag, useGetVideoByTag, useGlobalStore, useImgixOrImageKit, USE_IMGIX, USE_IMAGEKIT, mobileDiaglogConst, useSaveVideoLibraryViews, formatCurrencyWithComma, snackbarSeverityType, videoLibraryConst, commomKeys} from '@bryzos/giss-ui-library';
import VideoPlayer from '../../components/VideoPlayer/VideoPlayer';
import { ReactComponent as VideoPlayIcon } from '../../../assets/mobile-images/VideoPlay-Icon.svg';
import { ReactComponent as ViewsIcon } from '../../../assets/mobile-images/Views.svg';
import { ReactComponent as ShareVideo } from '../../../assets/mobile-images/ShareVideo.svg';
import { IonContent, IonPage } from '@ionic/react';
import useDialogStore from '../../components/Dialog/DialogStore';
import clsx from 'clsx';
import { Device } from '@capacitor/device';
import {App as NativeApp} from '@capacitor/app';
import useSnackbarStore from '../../library/component/Snackbar/snackbarStore';
import ShareVideoUrl from '../../components/ShareVideoUrl/ShareVideoUrl';

const VideoLibrary = () => {
  const getVideoByTag = useGetVideoByTag();
  const getAllVideoLibraryTag = useGetAllVideoLibraryTag();
  const imgixOrImageKit = useImgixOrImageKit();
  const [videoLibraryTags, setVideoLibraryTags] = useState([]);
  const [targetedVideoToPlay, setTargetedVideoToPlay] = useState();
  const [targetedTag, setTargetedTag] = useState();
  const [targetedIndex, setTargetedIndex] = useState();
  const [playVideoUrl, setPlayVideoUrl] = useState();
  const [captionUrl, setCaptionUrl] = useState();
  const [useImgixUrl, setUseImgixUrl] = useState(false);
  const [useImageKitUrl, setUseImageKitUrl] = useState(false);
  const [videoMapping, setVideoMapping] = useState({});
  const {setShowLoader, userData, setVideoLibraryMapping, videoLibraryMapping, setVideoLibraryViewCount, videoLibraryViewCount, videoLibraryTags : tagsFromLibrary, setVideoLibraryTags: setTagsFromLibrary, isAppStateChangeActive } = useGlobalStore();
  const { showCommonDialog, resetDialogStore } = useDialogStore();
  const saveVideoViewCount = useSaveVideoLibraryViews();
  const [isRunningOnIphone, setIsRunningOnIphone] = useState(false);
  const [isRunningOnIpad, setIsRunningOnIpad] = useState(false);
  const [videoNoLongerAvailable, setVideoNoLongerAvailable] = useState(false);
  const [imgUrl, setImgUrl] = useState();
  const [isPiP, setIsPiP] = useState(false);
  const [disableNextVideoBtn, setDisableNextVideoBtn] = useState(true);
  const {showToastSnackbar, resetSnackbarStore}: any = useSnackbarStore();
  const [isExpanded, setIsExpanded] = useState(false);
  const videoRef = useRef(null);

  useEffect(() => {
    isIPhoneOrIpad();
    getAllVideos();
  }, [])

  
  useEffect(()=>{
    if(videoLibraryMapping){
      setVideoMapping(videoLibraryMapping);
      const index = videoLibraryMapping[targetedTag]?.findIndex(x => targetedVideoToPlay.video_id === x.video_id)
      if(targetedIndex >= 0) {
        if(index >= 0){
          setTargetedIndex(index);
          setDisableNextVideoBtn(videoLibraryMapping[targetedTag].length <= 1);
        }
        else{
          setVideoNoLongerAvailable(true);
          setTargetedIndex();
          setTargetedTag();
        }
      }
      setVideoLibraryMapping(null);
    }
  },[videoLibraryMapping])

  useEffect(()=>{
    if(tagsFromLibrary){
      const tagMapping = tagsFromLibrary.reduce((acc, current) => {
        const param = current.query_param;
        acc[current.query_param] = current;
        return acc;
      },{})
      const replaceVideoTagging = videoLibraryTags.map((tag)=> {
        const tagObj = tagMapping[tag.query_param];
        tagMapping[tag.query_param] = undefined;
        return  tagObj ?? tag;
      }) ?? [];
      Object.values(tagMapping).forEach((tag)=>{
        if(tag)
        replaceVideoTagging.push(tag)
      })
      setVideoLibraryTags(replaceVideoTagging);
      setTagsFromLibrary(null);
      if(targetedTag){
        const currentPlayingVideoTag = replaceVideoTagging.find((tag)=> tag.query_param === targetedTag )
        setVideoNoLongerAvailable(!currentPlayingVideoTag?.show_on_app ?? true);
      }
    }
  },[tagsFromLibrary])

  useEffect(()=>{
    if(videoLibraryViewCount){
      const {tag, view_count, video_id} = videoLibraryViewCount;
      if(videoMapping[tag])
      videoMapping[tag] = videoMapping[tag].map((video) => {
        if(video.video_id === video_id)return {...video, view_count};
        return video;
      });
      setVideoMapping({...videoMapping});
      setVideoLibraryViewCount(null);
    }
  },[videoLibraryViewCount])

  useEffect(()=>{
      if (isAppStateChangeActive) {
        getAllVideos();
      }
  },[isAppStateChangeActive])

  const getAllVideos = async () => {
    setShowLoader(true);
    try{
      const response = (await imgixOrImageKit.mutateAsync())?.data?.data;
      if(response){
        const imgixObj = response.find(res => res.config_key === USE_IMGIX);
        if (imgixObj) {
          setUseImgixUrl(imgixObj.config_value);
        }
        const imgeKitObj = response.find(res => res.config_key === USE_IMAGEKIT);
        if (imgeKitObj) {
          setUseImageKitUrl(imgeKitObj.config_value);
        }
      }
      const libraryTagsList = (await getAllVideoLibraryTag.mutateAsync()).data?.data;
      if(libraryTagsList){
        const tagList = libraryTagsList.reduce((tags ,curr_tag) => {
          if(curr_tag.show_on_app)  tags.push(curr_tag.query_param);
          return tags;
        },[]);
        getVideoByTag.mutateAsync(tagList).then((data)=>{
          setShowLoader(false);
          setVideoLibraryTags(libraryTagsList);
          if(data?.data?.data)
          setVideoMapping(data.data.data);
        })
      }
    }
    catch(err){
      showCommonDialog(commomKeys.errorContent,[{name: mobileDiaglogConst.ok, action: resetDialogStore}]);
      setShowLoader(false);
    }
  }

  const getPlayVideoUrl = (s3VideoPath, tag, video) => {
    let url = useImageKitUrl
      ? import.meta.env.VITE_IMAGEKIT_PREFIX +
        s3VideoPath 
      : import.meta.env.VITE_CLOUDFRONT_PREFIX + s3VideoPath ;
    url += '?tag=' + tag;
    if (video.is_large_file && useImageKitUrl) {
      url += '&tr=orig';
    }
    return url;
  };

  const playVideo = async (tag: string, video: any, index: number) => {
    if(!(tag && videoMapping[tag]?.length > 1)){
      setDisableNextVideoBtn(true);
    }else {
      setDisableNextVideoBtn(false)
    }
    if(isPiP){
      showToastSnackbar(videoLibraryConst.videoWarningForPiPMode, snackbarSeverityType.alert, null, resetSnackbarStore, '', 5000);
      return;
    }
    if(video?.video_s3_url){
      const payload = {
        "data":{
            "user_id": userData.data.id.toString(),
            "video_id": video.video_id
        }
      }
      saveVideoViewCount.mutateAsync(payload).then(()=>{
        video.view_count+=1;
        setVideoMapping({...videoMapping});
      });
      const s3VideoPath = video.video_s3_url.split(".com")[1];
      const _url = getPlayVideoUrl(s3VideoPath,tag,video) ;
      const img = isRunningOnIpad ? video.thumbnail_s3_url_map?.intro_tablet : video.thumbnail_s3_url_map?.intro_mobile;
      const s3ImgPath = img.split(".com")[1];
      const imgUrl = useImgixUrl ? import.meta.env.VITE_IMGIX_PREFIX + s3ImgPath + import.meta.env.VITE_IMGIX_SUFFIX : import.meta.env.VITE_CLOUDFRONT_PREFIX + s3ImgPath;

      setPlayVideoUrl();
      setCaptionUrl();
      setImgUrl();

      if(isRunningOnIpad || isRunningOnIphone){
        setTimeout(()=>{
          setPlayVideoUrl(_url);
          setCaptionUrl(video.subtitle_s3_url);
          setImgUrl(imgUrl);
        },0);
      }
      else {
        setTimeout(()=>{
          setPlayVideoUrl(_url);
          setCaptionUrl(video.subtitle_s3_url);
          setImgUrl(imgUrl);
        },0);
      }
    }else{
      setPlayVideoUrl();
      setCaptionUrl();
    }
    if(video?.thumbnail_s3_url_map){
      const img = isRunningOnIpad ? video.thumbnail_s3_url_map?.intro_tablet : video.thumbnail_s3_url_map?.intro_mobile;
      const s3ImgPath = img.split(".com")[1];
      const imgUrl = useImgixUrl ? import.meta.env.VITE_IMGIX_PREFIX + s3ImgPath + import.meta.env.VITE_IMGIX_SUFFIX : import.meta.env.VITE_CLOUDFRONT_PREFIX + s3ImgPath;
      setImgUrl(imgUrl)
      setTimeout(()=>{videoRef?.current?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });},10);
    }
    setTargetedIndex(index);
    setTargetedTag(tag);
    setTargetedVideoToPlay(video);
    setVideoNoLongerAvailable(false);
    setIsExpanded(false)
  }

  async function isIPhoneOrIpad() {
    const info = await Device.getInfo();
    setIsRunningOnIphone(info.platform === 'ios' && info.model.toLowerCase().includes('iphone'));
    setIsRunningOnIpad(info.platform === 'ios' && info.model.includes('iPad'));
  }

  const playNextVideo = () => {
    if(videoMapping[targetedTag]?.length > 1){
      const index = (targetedIndex+1) % (videoMapping[targetedTag].length);
      playVideo(targetedTag, videoMapping[targetedTag][index], index);
    }
  }

  const showPiPInProcess = (_isPiP: boolean) => {
    setIsPiP(_isPiP);
  }

  const shortTextLimit = 130;
  const fullTextLimit = 180;
  const isLongText = videoMapping[targetedTag]?.[targetedIndex]?.description?.length > fullTextLimit;
  const truncatedText = videoMapping[targetedTag]?.[targetedIndex]?.description?.length > shortTextLimit ? videoMapping[targetedTag]?.[targetedIndex]?.description.slice(0, shortTextLimit) + '...' : videoMapping[targetedTag]?.[targetedIndex]?.description;
  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  const [openShare, setOpenShare] = useState(false);

  const openShareVideoPopup = () => {
    setOpenShare(true);
  };

  const shareVideoPopupClose = () => {
    setOpenShare(false);
  };

  return (
    <IonPage>
      <IonContent>
       <div className={clsx(styles.videoPlayerMain, openShare && 'shareBackdrop' )}>
          <div ref = {videoRef}>
            <div className={styles.videoPlayerTitleMain}>
              <div className={styles.videoPlayerTitle}>BRYZOS = TRUST</div>
              <p>Here is your all-access pass to get to know the Bryzos team. Check out this behind-the-scenes view into the discussions and decisions made to optimize
                this product designed with you specifically in mind. Let us show you why this app will forever change the way you trade steel.</p>
            </div> 
          </div>
          <div className={clsx(styles.videoOuterContainer, videoNoLongerAvailable && styles.videoPlayerMinHeight)}>
          {
            (imgUrl && (!videoNoLongerAvailable || isPiP)) &&
            <div className={styles.videoContainer}>
              {playVideoUrl ? 
                <VideoPlayer
                  url={playVideoUrl}
                  width={"100%"}
                  height={"300px"}
                  autoPlay={true}
                  isIpad={isRunningOnIpad}
                  isIphone={isRunningOnIphone}
                  playNextVideo={playNextVideo}
                  poster={'../../../assets/mobile-images/black.png'}
                  imgUrl={imgUrl}
                  showPiPInProcess={showPiPInProcess}
                  isPiP={isPiP}
                  disableNextVideoBtn={disableNextVideoBtn}
                  captionUrl={captionUrl}
                /> 
              :
                <img src={imgUrl} height={"100%"} width={"100%"} alt={targetedVideoToPlay.caption}></img>
              }
              {
                (videoMapping && targetedTag && targetedIndex>=0) &&
                <>
                  <div className={styles.targetedTag}>
                    <p>{videoMapping[targetedTag][targetedIndex]?.title}</p>                
                  </div>
                  {videoMapping[targetedTag]?.[targetedIndex]?.description && <div className={styles.descriptionVideo}>
                    <span>
                        {isExpanded || !isLongText ? videoMapping[targetedTag]?.[targetedIndex]?.description : truncatedText}
                  </span>
                    {isLongText && (
                      <button onClick={handleToggle}>
                        {isExpanded ? 'Show Less' : 'Show More'}
                      </button>
                    )}
                    
                  </div>
                  }

                  {playVideoUrl &&
                    <div className={styles.ViewSection}>
                      {videoMapping[targetedTag][targetedIndex]?.view_count > 0 && <div className={styles.rowAlignTitle}><ViewsIcon /> {formatCurrencyWithComma(`${videoMapping[targetedTag][targetedIndex]?.view_count}`)} views</div>}
                      {videoMapping[targetedTag]?.[targetedIndex]?.share_video_url && <span className={styles.shareVideoIcon} onClick={openShareVideoPopup}><ShareVideo /> Share</span>}
                    </div>
                  }
                 
                </>
              }
            </div>
          }
          {
            (isPiP || videoNoLongerAvailable) && <div className={clsx(styles.videoNoLongerAvailable, isPiP && styles.pipWindow)}>
              <span>{videoNoLongerAvailable ? videoLibraryConst.videoNotAvailable : videoLibraryConst.videoPlayingInPiPMode}</span>
            </div>
          }
          </div>
          <div className={styles.episodesThumbnailSection}>
            {videoLibraryTags.map((tag) => {
              const tagParam = tag.query_param;
              if(videoMapping[tagParam]?.length > 0 && tag.show_on_app)
              return <div key={tag.display_title}>
                <div className={styles.episodesTitle}><span>{tag.display_title}</span><span className={styles.tagSubtitle}>{tag.display_subtitle}</span></div>
                <div className={styles.videoPlayerThumbFlex}>
                  {videoMapping[tagParam].map((videoSection: any, index: number) => {
                    const s3Path = videoSection.thumbnail_s3_url_map?.thumbnail_app?.split(".com")[1];
                    const imgUrl = useImgixUrl ? import.meta.env.VITE_IMGIX_PREFIX + s3Path + import.meta.env.VITE_IMGIX_SUFFIX+"&w=350&h=300" : import.meta.env.VITE_CLOUDFRONT_PREFIX + s3Path;
                    return (
                      <div key={videoSection.id}>
                        {
                          <div className={styles.videoThumb}>
                            <div className={clsx(styles.videoThumbBox, (targetedVideoToPlay?.video_id === videoSection.video_id && targetedTag === tag.query_param) && styles.selectedVideo)}>
                              <div className={styles.videoThumbBoxInnerContent}>
                                <img src={imgUrl} alt={videoSection.caption}></img>
                                <div className={styles.overlay} onClick={() => playVideo(tag.query_param, videoSection, index)}>
                                  {videoSection?.video_s3_url && <>
                                    {(targetedVideoToPlay?.video_id === videoSection.video_id && targetedTag === tag.query_param) && <div className={styles.nowPlatingtxt}>Now Playing</div>}
                                    {!(targetedVideoToPlay?.video_id === videoSection.video_id && targetedTag === tag.query_param) && <span className={styles.VideoPlayIcon}><VideoPlayIcon /></span>}
                                  </>}
                                </div>
                              </div>
                            </div>
                            <p>{videoSection.title}</p>
                            {(videoSection.video_s3_url && videoSection.view_count > 0) && (<div className={styles.rowAlign}><ViewsIcon /> {formatCurrencyWithComma(`${videoSection.view_count}`)} views</div>)}
                          </div>
                        }
                      </div>
                    )
                  })}
                </div>
              </div>
            })}
          </div>
        </div>
        <ShareVideoUrl openShare={openShare} shareVideoPopupClose={shareVideoPopupClose} videoData={targetedVideoToPlay} isIOSDevice={isRunningOnIphone || isRunningOnIpad}/>
      </IonContent>
    </IonPage>
  );
};

export default VideoLibrary;