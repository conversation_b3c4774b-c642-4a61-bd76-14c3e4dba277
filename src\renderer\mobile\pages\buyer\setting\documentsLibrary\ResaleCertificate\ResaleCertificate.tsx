import clsx from 'clsx';
import { CustomMenu } from '../../../../../components/CustomMenu';
import { useRef, useState } from 'react';
import {
    buyerSettingConst,
    commomKeys,
    fileType,
    prefixUrl,
} from '../../../../../library/common';
import {
    downloadFiles,
    uploadBuyerDocumentInfoToS3,
} from '../../../../../library/helper';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import { ReactComponent as ApprovedCheckIcon } from '../../../../../../assets/mobile-images/check_approved.svg';
import { ReactComponent as UploadIcon } from '../../../../../../assets/mobile-images/icon_Upload.svg';
import { ReactComponent as DeleteIcon } from '../../../../../../assets/mobile-images/delete.svg';
import useDialogStore from '../../../../../components/Dialog/DialogStore';
import styles from '../DocumentInfo.module.scss';
import axios from 'axios';
import { useIonRouter } from '@ionic/react';

const ExpirationMenuPropsTop = {
    classes: {
        paper: clsx(styles.Dropdownpaper),
    },
};

type Props = {
    control: any; trigger: any;
    register: any;
    states: any;
    errors: any;
    ResaleExpiration: any;
    setValue: any;
    watch: any;
    index: any;
    remove: any;
    isDirty: any;
    setOpenDocumentInfo: any;
    handleCloseDocumentPopup: any;
    documentInfo: any;
};
const ResaleCertificate: React.FC<Props> = ({
    control, trigger,
    register,
    states,
    errors,
    ResaleExpiration,
    setValue,
    watch,
    index,
    remove,
    setOpenDocumentInfo = null,
    handleCloseDocumentPopup,
    documentInfo
}) => {
    const [uploadCertProgress, setUploadCertProgress] = useState<boolean | null>(null);
    const { showCommonDialog, resetDialogStore }: any = useDialogStore();
    const { userData, setShowLoader }: any = useGlobalStore();

    const router: any = useIonRouter();
    const resaleCertFileRef = useRef<any>();

    console.log('watch', watch());
    const certId = watch(`resaleCertificates.${index}.id`);
    const status = watch(`resaleCertificates.${index}.status`);
    const signedUrl = watch(`resaleCertificates.${index}.cerificateS3Url`);
    const cerificateFileName = watch(`resaleCertificates.${index}.cerificateFileName`);
    const isDeletable = watch(`resaleCertificates.${index}.isDeletable`);

    const resaleCertEditHandler = () => {
        resaleCertFileRef.current?.click();
    };

    const deletePopup = () => {
        showCommonDialog('Delete?', [{ name: 'Yes', action: deleteCertificate }, { name: 'No', action: resetDialogStore }]);
    };

    const deleteCertificate = () => {
        resetDialogStore();
        setShowLoader(true);
        const payload = { data: { cert_id: certId } };
        axios.post(import.meta.env.VITE_API_SERVICE + `/user/deleteResaleCert`, payload).then((response) => {
            if (response.data.data.error_message) {
                setShowLoader(false);
                showCommonDialog(response.data.data.error_message, [
                    { name: 'Ok', action: resetDialogStore },
                ]);
            } else {
                console.log('File deleted successfully:', response.data);
                remove(index);
                setShowLoader(false);
                if (setOpenDocumentInfo) {
                    handleCloseDocumentPopup();
                } else {
                    router.push('/setting', { animate: true, direction: 'forward' });
                }
            }
        }).catch((error) => {
            console.error('Error deleting file:', error);
            setShowLoader(false);
        });
    };

    const viewCert = async () => {
        setShowLoader(true);
        try {
            const fileName = signedUrl.substring(signedUrl.lastIndexOf('/') + 1, signedUrl.length);
            await downloadFiles(signedUrl, fileName, fileType.unknown);
            setShowLoader(false);
        } catch (e) {
            setShowLoader(false);
        }
    };

    const uploadCertFile = async (event: any) => {
        const file = event.target.files[0];
        let url = null;

        if (event.target.files.length !== 0) {
            setShowLoader(true);
            setUploadCertProgress(true);

            const bucketName = import.meta.env.VITE_S3_UPLOAD_SETTINGS_RESALE_CERT_BUCKET_NAME;
            try {
                url = await uploadBuyerDocumentInfoToS3(file, userData, prefixUrl.resaleCertPrefix, bucketName);
                setUploadCertProgress(false);
                setValue(`resaleCertificates.${index}.cerificateFileName`, file.name);
                setValue(`resaleCertificates.${index}.cerificateS3Url`, url);
                trigger(`resaleCertificates.${index}`);
                showCommonDialog(buyerSettingConst.uploadCertDialogContent, [{ name: commomKeys.successBtnTitle, action: resetDialogStore }]);
                setShowLoader(false);
            } catch (error) {
                console.error(error);
                url ? setUploadCertProgress(false) : setUploadCertProgress(null);
                showCommonDialog(commomKeys.errorContent, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                setShowLoader(false);
            }
        }
    };

    return (
        <>
            <div className={styles.documentInfoMain}>
                <div className={styles.stateGrid}>
                    <label className={styles.stateNo}>
                        <span>
                            State {index + 1}{' '}
                        </span>
                        {index > 1 && !signedUrl && (
                            <span>
                                <button className={styles.delRow} onClick={() => remove(index)}>Delete</button>
                            </span>
                        )}
                    </label>
                    {status === 'Approved' ? <div className={styles.approvedStatus}>
                        <ApprovedCheckIcon />
                        {status}
                    </div> : <div className={styles.pendingStatus}>
                        {status}
                    </div>
                    }
                </div>
            </div>
            <div className={styles.documentInfoMain}>
                <label className={styles.viewCertlbl}>
                    Upload Cert{' '}
                    {signedUrl && (
                        <button onClick={viewCert} className={styles.viewCertBtn}>
                            View Cert.
                        </button>
                    )}
                </label>
                <div className={clsx(!!!isDeletable && styles.disabledCert)}>
                {uploadCertProgress === true ? (
                    <label>
                        <span className={styles.uploadText}>Uploading</span>
                    </label>
                ) : cerificateFileName ? (
                    <>
                        <div className={styles.uploadFileStatus}>
                            <span className={styles.uploadedFileName} onClick={resaleCertEditHandler}>
                                {cerificateFileName}
                            </span>
                            {certId && !!isDeletable && (
                                <button onClick={deletePopup}><DeleteIcon /></button>
                            )}
                        </div>
                    </>
                ) : (
                    <label className={styles.uploadFileBtn}>
                        <UploadIcon />
                        <button className={styles.uploadText} onClick={resaleCertEditHandler} >Upload</button>
                    </label>
                )}
                <input
                    type='file'
                    onChange={(e: any) => {
                        uploadCertFile(e);
                    }}
                    ref={(e) => {
                        resaleCertFileRef.current = e;
                    }}
                />
            
            <div className={clsx(styles.stateDropdown, styles.documentInfoMain)}>
                <div className={styles.grid1}>
                    <label>State</label>
                    <CustomMenu
                        control={control}
                        key={watch(`resaleCertificates.${index}.stateId`)}
                        name={`resaleCertificates.${index}.stateId`}
                        placeholder={'Select'}
                        MenuProps={{ classes: { paper: styles.Dropdownpaper, }, }}
                        items={states?.map((x: any) => ({ title: x.code, value: x.id })) ?? []}
                        className={clsx('selectDropdown', errors.resaleCertState && styles.borderOfError)}
                    />
                </div>
                <div className={styles.grid1}>
                    <label>Expiration </label>
                    <CustomMenu
                        control={control}
                        key={watch(`resaleCertificates.${index}.expirationDate`)}
                        name={`resaleCertificates.${index}.expirationDate`}
                        placeholder={'Select'}
                        MenuProps={{ ...ExpirationMenuPropsTop}}
                        items={ResaleExpiration}
                        className={clsx('selectDropdown', errors.resaleCertExpire && styles.borderOfError)}
                    />
                </div>
            </div>
            </div>
            </div>
            <div className={styles.addLineSeprator}></div>
        </>
    );
};

export default ResaleCertificate;
