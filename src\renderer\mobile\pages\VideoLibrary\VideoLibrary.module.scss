.videoPlayerMain {
    padding: 24px;
    margin-bottom: 80px;

    .videoNoLongerAvailable{
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #000;
        color: #fff;
        font-family: Inter;
        font-size: 14px;
        line-height: normal;
        width: 100%;
        height: 300px;
        margin-bottom: 20px;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 100;
        padding:12px;
        span{
            text-align: center;
        }
    }

    .videoContainer {
        margin-bottom: 24px;
        img{
            @media (max-width:1100px) {
                height:430px;
            }
            @media (max-width:480px) {
                height:300px;
            }
        }

        video{
            @media (max-width:1100px) {
                height:430px;
            }
            @media (max-width:480px) {
                height:300px;
            }
        }
        

        .targetedTag {
            font-family: Syncopate;
            font-size: 18.5px;
            font-weight: bold;
            line-height: normal;
            text-align: left;
            color: #fff;
            text-transform: uppercase;
            margin:16px 0px 8px 0px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: normal;
        }
    }

    .descriptionVideo{
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        line-height: normal;
        text-align: left;
        color: #fff;
        margin-bottom: 8px;
        button{
            font-family: Inter;
            font-size: 14px;
            color: #16b9ff;
            margin-left: 3px;
        }
    }

    .videoPlayerTitleMain {
        .videoPlayerTitle {
            font-family: Syncopate;
            font-size: 17.5px;
            font-weight: bold;
            line-height: 1.16;
            letter-spacing: normal;
            text-align: left;
            color: #fff;
            margin-bottom: 4px;
        }

        p {
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            line-height: 1.4;
            text-align: left;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 16px;
        }
    }

    .episodesThumbnailSection {
        .episodesTitle {

            span {
                font-family: Syncopate;
                font-size: 16.5px;
                font-weight: bold;
                line-height: 1.4;
                color: #fff;
                margin-right: 4px;
                display: inline-block;
                text-transform: uppercase;
            }

            .tagSubtitle{
                margin-left: 5px;
                font-size: 12px;
                font-weight: normal;
                text-transform: none;
                font-family: 'Inter';
            }
        }

        .videoPlayerThumbFlex {
            display: flex;
            column-gap: 12px;
            overflow-x: auto;
            padding-bottom: 6px;
            margin-bottom: 10px;

            &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
            }

            &::-webkit-scrollbar-thumb {
                background-color: rgba(217, 217, 217, 0.37);
                border-radius: 50px;
            }

            &::-webkit-scrollbar-track {
                background: transparent;
            }
        }

        .videoThumb {
            width: 140px;
            border-radius: 4px;
            
            .videoThumbBox {
                width: 140px;
                height: 120px;
                flex-grow: 0;
                border-radius: 4px;
                background-color: rgba(0, 0, 0, 0.3);
                margin: 12px 0px;
                position: relative;
                transition: all 0.1s;
                border: solid 1px transparent;
                &.selectedVideo{
                    border: solid 1px rgba(255, 255, 255, 0.7);
                    img{
                        opacity: 0.4;
                    }
                    .overlay{
                        background-color:rgba(255, 255, 255, 0.4);
                        pointer-events: none;
                        .nowPlatingtxt{
                            font-family: Syncopate;
                            font-size: 12px;
                            font-weight: 600;
                            color: #fff;
                            text-transform: uppercase;
                        }
                    }
                    &:hover{
                        border: solid 1px rgba(255, 255, 255, 0.7);
                    }
                }

                .videoThumbBoxInnerContent {
                    width: 100%;
                    height: 100%;
                }

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: fill;
                    border-radius: 4px;
                }

                .overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.3);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }

                &:hover {
                    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.25);
                    border: solid 1px #16b9ff;
                }

                .VideoPlayIcon {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    cursor: pointer;
                    z-index: 99;
                     svg{
                        border-radius: 50%;
                        box-shadow: inset -0.4px 0.4px 0.4px -0.9px rgba(255, 255, 255, 0.35);
                        border-style: solid;
                        border-width: 0.6px;
                        border-image-source: linear-gradient(202deg, #fff 24%, rgba(255, 255, 255, 0) 15%);
                        background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.25), rgba(0, 0, 0, 0.25)), linear-gradient(202deg, rgb(255 255 255 / 44%) 92%, rgba(255, 255, 255, 0) 15%);
                        background-origin: border-box;
                        background-clip: content-box, border-box;
                        g{
                          display: none;
                        }
                      }
                }
            }

            p {
                font-family: Inter;
                font-size: 12px;
                font-weight: 300;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: left;
                color: #fff;
                margin: 0px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 3;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;
            }
        }

    }
}

.ViewSection{
    display: flex;
    align-items: center;
    margin-top: 8px;
    .rowAlignTitle{
        display: flex;
        align-items: center;
        font-family: Inter;
        font-weight: 300;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        gap: 5px;
        text-transform: none;
        svg{
            width: 24px;
            height: 24px;
        }
    }
    .shareVideoIcon{
        font-family: Inter;
        font-size: 12px;
        font-weight: 300;
        line-height: normal;
        text-align: left;
        color: rgba(255, 255, 255, 0.7);
        display: flex;
        align-items: center;
        margin-left: 16px;
        cursor: pointer;
        svg{
            margin-right: 4px;
        }
    }

}

.rowAlign{
    display: flex;
    align-items: center;
    font-family: Inter;
    font-weight: 300;
    margin-top: 8px;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.7);
    gap: 5px;
    text-transform: none;
}

.videoOuterContainer {
    position: relative;
    .pipWindow {
        background-color: rgba(255, 255, 255, 0.4);
    }
}

.videoPlayerMinHeight {
    min-height: 300px;
    margin-bottom: 20px;
}