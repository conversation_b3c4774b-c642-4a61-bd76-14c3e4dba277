export const channelWindowList = {
  close: 'windowClose',
  closeNewUpdateWindow: 'new-update-widnow-close',
  doUpdate: 'doUpdate',
  minimize: 'windowMinimize',
  sticky: 'windowSticky',
  updateHeight: 'windowHeight',
  electronVersion: 'electronVersion',
  ready:"updateWindowReady",
  windowStore:'windowStore'
};
export const toggleStickyBtnId = 'toggle-sticky-btn';
export const localStorageStickyItemKey = 'isSticky';
export const SHOW_NOTIFICATION = "show-notification";

export const userRole = {
  buyerUser : 'BUYER',
  sellerUser : 'SELLER',
  neutralUser : 'NEUTRAL'
}

export const appEnvironment = {
  dev:'dev',
  staging:'staging',
  demo: 'demo',
  prod: 'prod'
}

export const EmptyString = '';
export const MinSearchDataLen = 2;
export const keyUpCode = 38;
export const keyDownCode = 40;
export const keyEnterCode = 13;

export const commomKeys = {
  countryCode: '+1',
  uploadSuccessful: 'Upload Successful',
  error: 'Error',
  errorContent: 'Something went wrong. Please try again in sometime',
  errorBtnTitle: 'Ok',
  successBtnTitle: 'Got it',
  actionStatus: {
    success: 'success',
    error: 'error'
  },
  electronWindowsStoreAvailableVersion: '1.0.3',
  minorElectronVersion: '1.0.4',
  info: 'Info',
  refresh: 'Refresh',
  tryAgain: 'Try Again',
  updateText: 'Update'
}

export const snackbarSeverityType = {
  alert: 'alert',
  warning: 'warning',
  success: 'success'
}

export const snackbarMessageContent = {
  socketAuthExpiryMessage: "<p>Session expired. Please refresh the app or click on 'Try Again'</p>",
  socketRefreshMessage: "<p>We seem to have disconnected. Please click on 'Try Again'</p>",
  productReferenceDataChanged: "<p>Please refresh the app to get updated Products & Pricing</p>"
}

export const mobileSnackbarMessageContent = {
  productReferenceDataChanged: "<p>There has been an update in Products & Pricing</p>",
}

export const referenceDataKeys = {
  domesticMaterialTextKey: "DOMESTIC_MATERIAL_ONLY_TEXT",
  sellerAvailInMinKey: "SELLER_AVAIL_IN_MINUTES"
}

export const routes = {
  loginPage : '/',
  homePage : '/home',
  forgotPassword : '/forgot-password',
  tempPage : '/share-widget',
  TnCPage : '/tnc',
  successPage : '/success',
  buyerSettingPage : '/buyer-setting',
  sellerSettingPage : '/seller-setting',
  createPoPage : '/create-po',
  orderConfirmationPage : '/order-confirmation',
  orderConfirmationPageSeller : '/order-confirmation-seller',
  acceptOrderPage : '/accept-order',
  disputePage : '/dispute',
  orderPage : '/order',
  newUpdate : '/new-update',
  onboardingWelcome:'/onboarding-welcome',
  onboardingTnc:'/onboarding-tnc',
  onboardingDetails:'/onboarding-details',
  onboardingThankYou:'/onboarding-thank-you'
}

export const mobileRoutes = {
  loginPage : '/login',
  instantPurchasing : '/instantpurchasing',
  search : '/search',
  TnCPage : '/tnc',
  successPage : '/success',
  buyerSettingPage : '/setting',
  sellerSettingPage : '/seller-setting',
  onboardingWelcome:'/onboarding-welcome',
  onboardingTnc:'/onboarding-tnc',
  onboardingDetails:'/onboarding-detail',
  onboardingThankYou:'/onboarding-thank-you',
  profile: '/profile',
  companyInformation: "/company-information",
  deliveryDetail: "/delivery-detail",
  receivingHours: '/receiving-hours',
  documentInfo: '/document-info',
  myReports: '/my-reports',
  payment: '/payment',
  sellerProfile: '/seller-profile',
  sellerCompanyInformation: "/seller-company-information",
  stockingLocation: '/stocking-location',
  fundingSetting: '/funding-setting',
  sellerDocumentInfo: '/seller-document-info',
  yourProducts: '/your-products',
  sellerMyReports: '/seller-my-reports',
  shareApp: '/shareapp',
  orderListing: '/order-listing',
  acceptOrder: '/accept-order',
  yourOrderListing: '/your-order-listing',
  yourOrderPreview: '/your-order-preview',
  error: '/error',
  changePassword: '/change-password',
  forgotPassword : '/forgot-password'
}

export const settingBasedRoutes = {
  "BUYER" : new Set([mobileRoutes.profile,mobileRoutes.companyInformation,mobileRoutes.deliveryDetail, mobileRoutes.receivingHours, mobileRoutes.documentInfo, mobileRoutes.myReports, mobileRoutes.payment,  mobileRoutes.buyerSettingPage]),
  "SELLER": new Set([mobileRoutes.sellerProfile, mobileRoutes.sellerCompanyInformation, mobileRoutes.sellerDocumentInfo, mobileRoutes.sellerMyReports, mobileRoutes.sellerSettingPage, mobileRoutes.fundingSetting, mobileRoutes.stockingLocation, mobileRoutes.yourProducts]),
} 

export const roleBasedRoutes = {
  "BUYER" : new Set([mobileRoutes.search,mobileRoutes.profile,mobileRoutes.companyInformation,mobileRoutes.deliveryDetail, mobileRoutes.receivingHours, mobileRoutes.documentInfo, mobileRoutes.myReports, mobileRoutes.payment, mobileRoutes.instantPurchasing,  mobileRoutes.buyerSettingPage]),
  "SELLER": new Set([mobileRoutes.search,mobileRoutes.yourOrderListing, mobileRoutes.yourOrderPreview, mobileRoutes.sellerProfile, mobileRoutes.sellerCompanyInformation, mobileRoutes.sellerDocumentInfo, mobileRoutes.sellerMyReports, mobileRoutes.sellerSettingPage, mobileRoutes.fundingSetting, mobileRoutes.stockingLocation, mobileRoutes.yourProducts, mobileRoutes.orderListing, mobileRoutes.acceptOrder]),
}

export const DeliveryDates = [
    { title: 'Order Date + 2 Days', value: 2 },
    { title: 'Order Date + 3 Days', value: 3 },
    { title: 'Order Date + 4 Days', value: 4 },
    { title: 'Order Date + 5 Days', value: 5 },
    { title: 'Order Date + 6 Days', value: 6 },
    { title: 'Order Date + 7 Days', value: 7 },
    { title: 'Order Date + 8 Days', value: 8 },
    { title: 'Order Date + 9 Days', value: 9 },
    { title: 'Order Date + 10 Days', value: 10 },
    { title: 'Order Date + 11 Days', value: 11 },
    { title: 'Order Date + 12 Days', value: 12 },
    { title: 'Order Date + 13 Days', value: 13 },
    { title: 'Order Date + 14 Days', value: 14 },
    { title: 'Order Date + 15 Days', value: 15 },
    { title: 'Order Date + 16 Days', value: 16 },
  ];

  export const States = [
    { title: 'AK', value: 27 },
    { title: 'WD', value: 28 },
    { title: 'NY', value: 29 },
    { title: 'NE', value: 30 },
  ];
  
  
  export const reactQueryKeys = {
    cognitoUser: 'cognitoUser',
    getUserPartData: 'getUserPartData',
    getCassData: "getCassData",
    createCassSupplier: "createCassSupplier",
    getForbiddenTooltips: "getForbiddenTooltips",
    getCompanyLists: "getCompanyLists",
    getCustomNotification: "getCustomNotification",
    getUnreadNotifications: 'getUnreadNotifications',
    getSecurityData: 'getSecurityData'
  }

  export const receivingHoursWeeksDefault = [
    { name: 'Mon', value: 'monday', defaultFrom: '11', defaultTo: '15' },
    { name: 'Tue', value: 'tuesday', defaultFrom: '11', defaultTo: '15' },
    { name: 'Wed', value: 'wednesday', defaultFrom: '11', defaultTo: '15' },
    { name: 'Thu', value: 'thusday', defaultFrom: '11', defaultTo: '15' },
    { name: 'Fri', value: 'friday', defaultFrom: '11', defaultTo: '15' },
    { name: 'Sat', value: 'saturday', defaultFrom: '11', defaultTo: '15' },
    { name: 'Sun', value: 'sunday', defaultFrom: '11', defaultTo: '15' }
]

  export const RecevingHoursFrom = [
    {title:'3am', value: 3, disabled: false},
    {title:'4am', value: 4, disabled: false},
    {title:'5am', value: 5, disabled: false},
    {title:'6am', value: 6, disabled: false},
    {title:'7am', value: 7, disabled: false},
    {title:'8am', value: 8, disabled: false},
    {title:'9am', value: 9, disabled: false},
    {title:'10am', value: 10, disabled: false},
    {title:'11am', value: 11, disabled: false},
    {title:'12pm', value: 12, disabled: false},
    {title:'1pm', value: 13, disabled: false},
    {title:'Closed', value: 'closed', disabled: false},
  ];

  export const RecevingHoursTo = [
    {title:'10am', value: 10, disabled: false},
    {title:'11am', value: 11, disabled: false},
    {title:'12pm', value: 12, disabled: false},
    {title:'1pm', value: 13, disabled: false},
    {title:'2pm', value: 14, disabled: false},
    {title:'3pm', value: 15, disabled: false},
    {title:'4pm', value: 16, disabled: false},
    {title:'5pm', value: 17, disabled: false},
    {title:'6pm', value: 18, disabled: false},
    {title:'Closed', value: 'closed', disabled: false},
  ]

  export const ExpirationDate1 = [
    {title:'6 months', value: 12 },
    {title:'1 year', value: 13 },
    {title:'2 year', value: 14 },
    {title:'3 year', value: 15 },
    {title:'4 year', value: 16 },
    {title:'5 year', value: 17 },
    {title:'Never Expires', value: 18 }
  ]

  export const ExpirationDate2 = [
    {title:'6 months', value: 12 },
    {title:'1 year', value: 13 },
    {title:'2 year', value: 14 },
    {title:'3 year', value: 15 },
    {title:'4 year', value: 16 },
    {title:'5 year', value: 17 },
    {title:'Never Expires', value: 18 }
  ]


 export const prefixUrl = {
    resaleCertPrefix : 'resalecert',
    irsW9Prefix : 'irsw9',
    lineCard : 'linecard',
    buyerPo : 'BuyerPO',
    sellerSo : 'SellerSO'
  }

  export const fileType = {
    excelSheet : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    pdf: 'application/pdf',
    zip: 'application/zip',
    unknown: 'unknown',
  }

export const buyerSettingConst = {
  buyerCreditLineLimit : 200000,
  creditLimitErrorMessage: 'This Beta currently supports credit lines up to $200,000.',
  uploadCertDialogContent: 'Your Resale Certificate has been uploaded successfully',
  apiRoutesForSave:{
    profile:'/user/mobile/buyer/saveProfile',
    companyInfo:'/user/mobile/buyer/saveCompanyInfo',
    receivingHours:'/user/mobile/buyer/saveReceivingHrsInfo',
    documentInfo:'/user/mobile/buyer/saveDocument',
    deliveryInfo:'/user/mobile/buyer/saveDeliveryInfo',
    saveBnpl:'/user/mobile/buyer/saveBnplRequest',
    saveDefaultPaymentMethod:'/user/mobile/buyer/saveDefaultPaymentMethod',
  }
}

export const sellerSettingConst = {
  apiRoutesForSave:{
    profile:'/user/mobile/seller/saveProfile',
    companyInfo:'/user/mobile/seller/saveCompany',
    stockInfo:'/user/mobile/seller/saveStockInformation',
    documentInfo:'/user/mobile/seller/saveDocument',
    fundingInfo:'/user/mobile/seller/saveFundingSettings',
  }
}
export const purchaseOrder = {
  readyToClaim: "READY_TO_CLAIM",
  pending: "PENDING",
  paymentMethodACH: "ach_credit",
  paymentMethodBNPL: "bryzos_pay"
}

export const disputeConst = {
  searchPlaceholder: "Search Orders to Create Dispute",
}
export const orderConfirmationConst = {
  buyerCancel: "BUYER_ORDER_CANCEL",
  sellerCancel: "SELLER_ORDER_CANCEL",
  uploadPoDialogContent: 'Your Purchase Order has been uploaded successfully',
  uploadSoDialogContent: 'Your Sales Order has been uploaded successfully'
}

export const orderPageConst = {
  orderNotAvaialableMsg: "The order is no longer available.",

}

export const userTypes = [
  { name: 'Buyer', value: 'BUYER'},
  { name: 'Seller', value: 'SELLER'},
];

export const supplier = "Supplier";
export const cassErrorMessage = "Cass - ";

export const certificateList = [
  {title: "Select One", value: 0, disabled: true},
  {title: "State 1", value: 1},
  {title: "State 2", value: 2},
]

export const mobileDiaglogConst = {
  unsavedChangesMsg: "You have unsaved changes",
  goBackToSetting: "Go Back to Settings",
  stay: "Stay",
  continue:"Continue",
  ok:"OK",
  receivingHrsEmpty: 'Receiving Hours Missing',
  goToSettings: "Go to settings",
  yes: "Yes",
  no: "No",
  refresh: "Refresh",
  discountPricingChangesMsg: "App improvements have been made that require an update."
}

export const commonAppEventsofPusher={
  privateEvents: {
    "userForceLogout":"USER_FORCE_LOGOUT",
    "userDiscountPriceChange": "USER_DISCOUNT_PRICE_CHANGE"
  },
  publicEvents: {referencePriceChanges: "NOTIFICATION_PRICE_CHANGES",referenceProductChanges: "NOTIFICATION_PRODUCT_CHANGES", referencePriceAndProductChanges:"NOTIFICATION_PRICE_PRODUCT_CHANGES", customNotification: "CUSTOM_NOTIFICATION"},
  buyerEvents: {},
  sellerEvents: {},
  mobileEvents: {capgoUpdate: "MOBILE_CAPGO_UPDATE"}
}

export const checkoutBnplErrorData = {
  creditLimitExceed : {
    tag: 'AVAILABLE_CREDIT_LIMIT_EXCEED',
    title: 'You do not have enough credit available for this purchase',
    body: `<span id="routeToPayment">Click here</span> to view your available credit or request a credit line increase or select a different Method of Payment`
  },
  notSetup : {
    tag: 'BNPL_NOT_SET_UP',
    title: 'You do not have enough credit available for this purchase',
    body: `Your BNPL request is still not approved. Please try again once approved`
  }
}

export const customNotification = {
  priorty: {
    low: "LOW",
    medium: "MEDIUM",
    high: "HIGH",
  },
  action: {
    refresh: "REFRESH",
    close: "CLOSE",
  }
}

export const raygunKeys = {
  socketInvalidToken: {
    tag: "socket-invalid-token",
    errorMsg : "Error in socket connection with invalid token."
  },
  capgoUpdate: {
    tag: "capgo"
  }
}

export const httpsOrigin = 'https://';
export const refererSuffix = '/';

export const capgoUpdateErrorMessages = {
  noUpdateNeeded: 'No update needed'
}

export const videoLibraryShare = {
  facebookWebUrl: 'https://www.facebook.com/sharer/sharer.php?u=',
  facebookAppUrl: 'fb://share/?link='
}

export const downloadFilesSuccessErrorMessages = {
  success: {
    downloadFolderText: 'File successfully downloaded in Downloads folder',
    documentFolderText: 'File successfully downloaded in Documents folder'
  },
  error: {
    permissionDenied: 'Please Accept Read Write permission, and then try again',
    unableToDownload: 'Sorry unable to download file'
  },
  conditionText: {
    errorIncludes: 'One of RECEIVER_EXPORTED or RECEIVER_NOT_EXPORTED should be specified'
  }

}

export const splashScreenConst = {
  starting:"Starting...",
  checkingForUpdates:"Checking for updates...",
  noUpdatesFound:"No updates found.",
  downloadingUpdates:"Downloading updates..."
}

export const cognitoCookiePrefix = 'CognitoIdentityServiceProvider';

export const BNPL_STATUS = {
  enabled: "ENABLED",
  onHold: "ON HOLD",
  restricted: "RESTRICTED"
}

export const android15SdkVersion = 35;