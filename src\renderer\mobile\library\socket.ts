// @ts-nocheck
import io from 'socket.io-client';
// import { snackbarMessageContent } from './common';
import { useStore } from './stores/store';

export interface SocketHeader {
  gissToken: string
  email: string
  accessToken: string
};

let socketInstance = null;
const MAX_RETRIES = 100;
const RETRY_INTERVAL = 3000;
let heartbeat = null;

const cleanUpEvent = () => {
  if(socketInstance){
    socketInstance.off("connect_error");
    socketInstance.off('poAccepted');
    socketInstance.off('authenticationUserStatus');
    socketInstance.off('newPoAdded');
    socketInstance.off('error');
    socketInstance.off('newPoToClaim');
    socketInstance.off('forceLogout');
    socketInstance.off('reconnect');
    socketInstance.off("connect");
    socketInstance.off("disconnect");
    socketInstance.off("updatePurchaseOrderInfo");
  }
}

export const resetConnection = () => socketInstance = null;

export const getSocketConnection = () => socketInstance;

export const socketDisconnect = () => {
  socketInstance?.disconnect();
  resetConnection();
}

export const createSocket = (url, extraHeaders:SocketHeader, userData, setReconnectToSocket) => {
  if (!socketInstance) {
    socketInstance = io(url, {
      extraHeaders,
      autoConnect: false,
    });

    let retryCount = 0;

    const state = useStore.getState();

    const connectSocket = () => {
      console.log("Trying to connect to socket ....");
      cleanUpEvent();
      socketInstance.connect();
     
      socketInstance.on('connect', () => {
        console.log('Socket connected');
        retryCount = 0;
        console.log("HeartBEAT",heartbeat)
        if(heartbeat)
        clearInterval(heartbeat)
        heartbeat = setInterval(() => {
          const date = new Date();
          console.log('---Heartbeat('+date.getHours()+':'+date.getMinutes()+':'+date.getSeconds()+')---');
          socketInstance.emit('heartbeat', {
            "socketId": socketInstance.id,
            ...extraHeaders
          });
        }, 45 * 1000);
        // removeSocketDisconnectToaster();
      });

      socketInstance.on('disconnect', (reason) => {
        console.log(`Socket disconnected: ${reason}`);
        console.log("HeartBEAT",heartbeat)
        console.log(retryCount +" < " + MAX_RETRIES +" = "+(retryCount < MAX_RETRIES));
        state.setIsOrderReadyForNavigate(false);
        if(reason === 'io client disconnect'){
          console.log('IO CLIENT DISCONNECT');
          clearInterval(heartbeat);
          cleanUpEvent();
          resetConnection();
        }else {
          // If disconnection was not initiated by client,
          // retry connection after some delay
            if (retryCount < MAX_RETRIES) {
                setTimeout(() => {
                console.log(`Retrying connection (${retryCount + 1}/${MAX_RETRIES})...`);
                connectSocket();
                retryCount++;
                }, RETRY_INTERVAL);
            }   
            else{
                console.log(`Max retries (${MAX_RETRIES}) exceeded, giving up...`);
            }
        }
      });

      socketInstance.on('connect_error', (error) => {
        console.log(retryCount + " < " + MAX_RETRIES + " = " + (retryCount < MAX_RETRIES));
        state.setIsOrderReadyForNavigate(false);
        if (retryCount < MAX_RETRIES) {
          // if (retryCount === 0)
          //   socketConnectionErrorHandler(snackbarMessageContent.socketRefreshMessage);
          setTimeout(() => {
            console.log(`Retrying connection (${retryCount + 1}/${MAX_RETRIES})...`);
            cleanUpEvent();
            clearInterval(heartbeat);
            connectSocket();
            retryCount++;
          }, RETRY_INTERVAL);
        } else {
          console.log(`Max retries (${MAX_RETRIES}) exceeded, giving up...`);
        }
        // handle connection error
      });

      socketInstance.on('poAccepted', (data) => {
        console.log('---poAccepted---');
        console.log(data.email_id);
        let isOrderConfirmed = data.email_id === userData.email_id;
        console.log(userData.email_id);
        state.removeFromPOCart(data.buyer_po_number, isOrderConfirmed);
        // removeOrderFromBadgeCount(data.buyer_po_number);
      });
      socketInstance.on('authenticationUserStatus', (data) => {
        console.log('---authenticationUserStatus---');
        console.log(data.po_data);
        state.setPOCart(data.po_data);
        state.setIsOrderReadyForNavigate(true);
      });
      socketInstance.on('newPoAdded', (data) => {
        console.log('---newPoAdded---');
        console.log(data);
        state.addPipeLinePO(data);
      });
      socketInstance.on('error', (data) => {
        console.log('---error---');
        console.log(data);
        state.setErrorPopupDetail(data)
      });
      socketInstance.on('newPoToClaim', (data) => {
        console.log('---newPoToClaim---');
        console.log(data);
        state.addNewPoToClaimToCart(data);
        // addOrderToListForBadgeCount(data.buyer_po_number);
      });
      socketInstance.on('updatePurchaseOrderInfo', (data) => {
        console.log('---updatePurchaseOrderInfo---');
        state.refreshPoFromCart(data);
        // removeOrderFromBadgeCount(data.buyer_po_number);
      });
      socketInstance.on('accessTokenInvalid', (data) => {
        console.log('---accessTokenInvalid---');
        // socketConnectionErrorHandler(snackbarMessageContent.socketAuthExpiryMessage);
        // setForceLogout(true);
        socketDisconnect();
        setReconnectToSocket(true);
      });  
      socketInstance.on('reconnect', () => {
        console.log("----reconnect-----");
        socketDisconnect();
        setReconnectToSocket(true);
        // getSocketConnection().connect();
      });
    };
    connectSocket();
  }

  return socketInstance;
};