import { registerPlugin, Plugin } from "@capacitor/core";
import { Capacitor } from '@capacitor/core';

interface AndroidSdkPluginInterface extends Plugin {
  getTargetSdkVersion(): Promise<{ targetSdkVersion: number }>;
}

// Create a safe version of the plugin that works on all platforms
const AndroidSdkPlugin = {
  getTargetSdkVersion: async (): Promise<{ targetSdkVersion: number }> => {
    // Only try to use the plugin on Android
    if (Capacitor.getPlatform() === 'android') {
      try {
        // Register and use the plugin only on Android
        const plugin = registerPlugin<AndroidSdkPluginInterface>("AndroidSdkPlugin");
        return await plugin.getTargetSdkVersion();
      } catch (error) {
        console.error('Error getting Android SDK version:', error);
        return { targetSdkVersion: 0 };
      }
    }
    // Return a default value for non-Android platforms
    return { targetSdkVersion: 0 };
  }
};

export { AndroidSdkPlugin };