// @ts-nocheck
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { IonCard, IonCardContent, createAnimation, createGesture } from '@ionic/react';
import type { Animation, Gesture, GestureDetail } from '@ionic/react';
import styles from './SwipeButton.module.scss';
import clsx from 'clsx';

type Props = {
  onSuccess: () => void;
  topTextMessage?: string;
  bollTitle?: string;
  classes?: string;
  swipeBallClass?: string;
  isValid?: boolean;
  maxWidth:number;
}

type Ref = HTMLDivElement;

const SwipeButton = forwardRef<Ref, Props>(({ onSuccess, isValid, topTextMessage, bollTitle, classes, swipeBallClass, maxWidth }, ref) => {
  useImperativeHandle(ref, () => {
    return {
      reset: reset,
    };
  });

  const cardEl = useRef<HTMLIonCardElement | null>(null);
  const animation = useRef<Animation | null>(null);
  const gesture = useRef<Gesture | null>(null);
  const initialStep = useRef<number>(0);
  const started = useRef<boolean>(false);
  const stateRef = useRef<boolean>(false);
  const container = useRef<any>(null);
  const [trackToHideWidth, setTrackToHideWidth] = useState(0);
  console.log("max-widht", maxWidth.current.offsetWidth)
  stateRef.current = isValid;

  useEffect(() => {
    if (animation.current === null) {
      /**
       * The track is 344px wide.
       * The card is 100px wide.
       * We want 16px of margin on each end of the track.
       */
      const MAX_TRANSLATE = maxWidth.current.offsetWidth - 107;

      animation.current = createAnimation()
        .addElement(cardEl.current!)
        .duration(300)
        .fromTo('transform', 'translateX(0)', `translateX(${MAX_TRANSLATE}px)`);

      gesture.current = createGesture({
        el: cardEl.current!,
        threshold: 0,
        gestureName: 'card-drag',
        onMove: (ev) => onMove(ev),
        onEnd: (ev) => onEnd(ev),
      });

      gesture.current.enable(true);

      const onMove = (ev: GestureDetail) => {
        if (stateRef.current === false) {
          started.current = false;
          return;
        }

        if (!started.current) {
          animation.current!.progressStart();
          started.current = true;
        }
        setTrackToHideWidth(getStep(ev)*100);
        animation.current!.progressStep(getStep(ev));
      };

      const onEnd = (ev: GestureDetail) => {
        console.log('test' )
        setTrackToHideWidth(0)
        if (!started.current) {
          return;
        }

        gesture.current!.enable(false);

        const step = getStep(ev);
        const shouldComplete = step > 0.5;

        if (shouldComplete && onSuccess) {
          onSuccess();
        }

        animation.current!.progressEnd(shouldComplete ? 1 : 0, step).onFinish(() => {
          gesture.current!.enable(true);
        });

        initialStep.current = shouldComplete ? MAX_TRANSLATE : 0;
        started.current = false;
      };

      const clamp = (min: number, n: number, max: number) => {
        return Math.max(min, Math.min(n, max));
      };

      const getStep = (ev: GestureDetail) => {
        const delta = initialStep.current + ev.deltaX;
        return clamp(0, delta / MAX_TRANSLATE, 1);
      };
    }

    return () => {
      gesture.current!.destroy();
      animation.current!.destroy();
    }
  }, [cardEl]);

  const reset = () => {
    animation.current!.progressEnd(0, 1).onFinish(() => {
      gesture.current!.enable(true);
    });
    initialStep.current = 0;
    started.current = false;
  };

  return (
    <>
      <div className={clsx(styles.claimSwipeButton, !stateRef.current && styles.disabled, classes) } style={{width:`${maxWidth.current.offsetWidth - 32}px`}} >
        <div className={styles.track} style={{width:`${maxWidth.current.offsetWidth - 32}px`}}>
          <IonCard id="card" button={true} ref={cardEl}>
            <IonCardContent className='swipeCardContent'></IonCardContent>
          </IonCard>
          <span className={styles.slideToAccept}></span>
          <div className={styles.trackToHide} style={{width:`${trackToHideWidth}%`}}></div>
        </div>
      </div>
    </>
  );
});
export default SwipeButton;