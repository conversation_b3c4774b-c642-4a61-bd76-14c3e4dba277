import{bK as w}from"./vendor-96deee04.js";class y extends w{async getId(){return{identifier:this.getUid()}}async getInfo(){if(typeof navigator>"u"||!navigator.userAgent)throw this.unavailable("Device API not available in this browser");const e=navigator.userAgent,i=this.parseUa(e);return{model:i.model,platform:"web",operatingSystem:i.operatingSystem,osVersion:i.osVersion,manufacturer:navigator.vendor,isVirtual:!1,webViewVersion:i.browserVersion}}async getBatteryInfo(){if(typeof navigator>"u"||!navigator.getBattery)throw this.unavailable("Device API not available in this browser");let e={};try{e=await navigator.getBattery()}catch{}return{batteryLevel:e.level,isCharging:e.charging}}async getLanguageCode(){return{value:navigator.language.split("-")[0].toLowerCase()}}async getLanguageTag(){return{value:navigator.language}}parseUa(e){const i={},r=e.indexOf("(")+1;let a=e.indexOf(") AppleWebKit");e.indexOf(") Gecko")!==-1&&(a=e.indexOf(") Gecko"));const s=e.substring(r,a);if(e.indexOf("Android")!==-1){const t=s.replace("; wv","").split("; ").pop();t&&(i.model=t.split(" Build")[0]),i.osVersion=s.split("; ")[1]}else if(i.model=s.split("; ")[0],typeof navigator<"u"&&navigator.oscpu)i.osVersion=navigator.oscpu;else if(e.indexOf("Windows")!==-1)i.osVersion=s;else{const t=s.split("; ").pop();if(t){const n=t.replace(" like Mac OS X","").split(" ");i.osVersion=n[n.length-1].replace(/_/g,".")}}/android/i.test(e)?i.operatingSystem="android":/iPad|iPhone|iPod/.test(e)&&!window.MSStream?i.operatingSystem="ios":/Win/.test(e)?i.operatingSystem="windows":/Mac/i.test(e)?i.operatingSystem="mac":i.operatingSystem="unknown";const l=!!window.ApplePaySession,x=!!window.chrome,p=/Firefox/.test(e),d=/Edg/.test(e),g=/FxiOS/.test(e),c=/CriOS/.test(e),f=/EdgiOS/.test(e);if(l||x&&!d||g||c||f){let t;g?t="FxiOS":c?t="CriOS":f?t="EdgiOS":l?t="Version":t="Chrome";const n=e.split(" ");for(const o of n)if(o.includes(t)){const v=o.split("/")[1];i.browserVersion=v}}else if(p||d){const o=e.split("").reverse().join("").split("/")[0].split("").reverse().join("");i.browserVersion=o}return i}getUid(){if(typeof window<"u"&&window.localStorage){let e=window.localStorage.getItem("_capuid");return e||(e=this.uuid4(),window.localStorage.setItem("_capuid",e),e)}return this.uuid4()}uuid4(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const i=Math.random()*16|0;return(e==="x"?i:i&3|8).toString(16)})}}export{y as DeviceWeb};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
