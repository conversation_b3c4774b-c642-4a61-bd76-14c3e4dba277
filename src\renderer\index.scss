

:root{
  --headerHeight:175px;
  --headerHeight1:75px;
}


/* reset default browser styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Change autocomplete styles in WebKit */

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus input:-webkit-autofill,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  box-shadow: none;
  -webkit-box-shadow: none;
  transition: background-color 5000s ease-in-out 0s;
}

input:-webkit-autofill {
  box-shadow: inset 0 0 0px 9999px white;
  -webkit-box-shadow: inset 0 0 0px 9999px white !important;
}


input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=search]::-webkit-search-cancel-button {
  display: none;
}

// html datepicker style

input[type="date"]::-webkit-datetime-edit,
input[type="date"]::-webkit-datetime-edit-month-field,
input[type="date"]::-webkit-datetime-edit-year-field {
  font-family: Noto Sans;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.6;
  text-align: left;
  color: #fff;
}

input::-webkit-datetime-edit-day-field:focus {
  background-color: inherit;
  color: #fff;
  outline: none;
}

::-webkit-calendar-picker-indicator {
  filter: invert(1);
}

.cp {
  cursor: pointer;
}

html,
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: 'Noto Sans', sans-serif;
  max-width: 600px;
  scroll-behavior: smooth;

  &::-webkit-scrollbar {
    display: none;
  }
}

/* Keyboard handling styles */
body {
  --keyboard-height: 0px;
}

/* Prevent viewport zoom on input focus */
input, textarea, select {
  font-size: 16px !important;
  -webkit-user-select: text;
  user-select: text;
}

/* Handle keyboard overlay issues */
.keyboard-is-open {
  padding-bottom: var(--keyboard-height);
}

/* Ensure proper input visibility when keyboard is open */
input:focus, textarea:focus {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* Fix for Android keyboard overlay */
@media screen and (max-height: 500px) {
  .ion-page {
    position: relative;
    overflow-y: auto;
  }
}

/* Additional keyboard fixes for Android */
.capacitor-android {
  input, textarea {
    /* Prevent zoom on focus */
    font-size: 16px !important;
    /* Improve input visibility */
    background-color: transparent;
    border-radius: 4px;
  }

  /* Ensure content is not hidden behind keyboard */
  .ion-content {
    --keyboard-offset: var(--keyboard-height, 0px);
    padding-bottom: var(--keyboard-offset);
  }

  /* Fix for modal/popup positioning when keyboard is open */
  ion-modal.keyboard-is-open,
  ion-popover.keyboard-is-open {
    --height: calc(100vh - var(--keyboard-height, 0px));
  }
}

/* Specific fixes for input focus states */
input:focus,
textarea:focus,
.ion-focused {
  /* Ensure focused inputs are visible */
  z-index: 1000;
  position: relative;
}

/* Prevent content jumping when keyboard appears */
.ion-page {
  transition: none !important;
}

/* Fix for sticky elements when keyboard is open */
.keyboard-is-open {
  .ion-header,
  .ion-footer,
  .ion-toolbar {
    position: relative !important;
  }
}

.MuiTouchRipple-root {
  display: none;
}

button {
  cursor: pointer;
  background-color: transparent;
  border: none;
  &:focus{
    outline: none;
  }
}

.cp {
  cursor: pointer;
}

.w100 {
  width: 100%;
}

.dflex {
  display: flex;
}

.flexColunm{
  flex-direction: column;
}

.onboardingChk{
  .containerChk {
    .lblChk {
      font-weight: 300;
    }
  }

}

.containerChk {
  display: inline-block;
  position: relative;
  cursor: pointer;
  padding-left: 28px;
  text-align: left;
  &:focus-within {
    .checkmark{
      border: solid 1px #70ff00;
    }
  }

  .lblChk {
    font-family: Noto Sans;
    font-size: 12px;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    text-align: left;
  }

  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }

  .checkmark {
    position: absolute;
    top: 2px;
    left: 0;
    z-index: 1;
    width: 18px;
    height: 18px;
    border-radius: 1.7px;
    border: solid 0.7px #3b4665;
    background-color: #ebedf0;
  }

  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }

  input:checked~.checkmark {
    background-color: #70ff00;
    border: solid 0.7px #70ff00;
  }

  input:checked~.checkmark:after {
    display: block;
  }

  .checkmark:after {
    left: 6px;
    top: 3px;
    width: 3px;
    height: 7px;
    border: solid #000;
    border-width: 0 1.5px 1.5px 0;
    transform: rotate(45deg);
  }
}

.bryzos {
  position: absolute;
  top: 0px;
  left: 0px;
  padding: 5px 12px 0px 16px;
  z-index: 11;
  min-height: 42px;
}
.resetPosition{
  position: relative;
  // background-color: #000;
}
.loginBody {
  margin: auto;
  width: 600px;
  min-height: 108px;
  border-radius: 10px;
  position: relative;
  // backdrop-filter: blur(24px);
  // background-color: rgba(0, 0, 0, 0.6);

  table {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
    border-spacing: 0px;

    .inputBody {
      height: 108px;

      .enterEmail {
        .errorText {
          font-family: Noto Sans;
          font-size: 14px;
          line-height: 1.6;
          text-align: left;
          color: #f00;
        }

        input {
          width: 100%;
          font-family: Noto Sans;
          font-size: 20px;
          line-height: 1.2;
          text-align: left;
          color: #fff;
          background-color: transparent;
          border: none;
          outline: none;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }
        }

      }

      #inputPlaceholder {
        width: 100%;
        font-family: Noto Sans;
        font-size: 20px;
        line-height: 1.2;
        text-align: left;
        color: rgba(255, 255, 255, 0.5);
        background-color: transparent;
        border: none;
        outline: none;
      }


      .passwordBox {
        display: flex;
        gap: 8px;

        input {
          border-bottom: 2px solid rgba(255, 255, 255, 0.5);
          font-family: Noto Sans;
          font-size: 20px;
          line-height: 1.2;
          text-align: center;
          color: #fff;
          width: 22px !important;
        }
      }

    }

    tr {
      td {
        height: 100%;
        padding: 0px 16px;
        padding-top: 46px;
        vertical-align: baseline;

        &:nth-child(1) {
          width: 65%;
          // background-color: rgba(0, 0, 0, 0.4);
          border-radius: 10px 0px 0px 10px;
        }

        &:nth-child(2) {
          width: 35%;
          // background-color: rgba(0, 0, 0, 0.75);
          border-radius: 0px 10px 10px 0px;
        }
      }
    }
  }

  .pressBtn {
    font-family: Noto Sans;
    font-size: 16px;
    line-height: 1.6;
    text-align: left;
    color: #2ecc71;
    background: transparent;
    border: none;
    cursor: pointer;
  }

}

.passwordBox{
  display: flex;
  gap: 12px;
  input{
    text-align: center
  }

}

.drag {
  -webkit-app-region: drag;
}

.dragMain {
  display: flex;
  width: 100%;
  height: 25px;
  flex: 1;

  .stagingEnv {
    padding: 2px 16px;
    border-radius: 5000px;
    margin-left: 11px;
    background-image: linear-gradient(to right, #00c6ff, #0072ff);
    font-family: Noto Sans;
    font-size: 14px;
    color: #fff;
    line-height: 1.6;
  }

  .demoEnv {
    padding: 2px 16px;
    border-radius: 5000px;
    margin-left: 11px;
    background-image: linear-gradient(to right, #ec008c, #fc6767);
    font-family: Noto Sans;
    font-size: 14px;
    color: #fff;
    line-height: 1.6;
  }

}

.signUpbtn {
  padding: 2px 8px;
  margin-right: 20px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.2);
  font-family: Noto Sans;
  font-size: 14px;
  line-height: 1.6;
  color: #fff;
  &:hover{
    background-color: rgba(255, 255, 255, 0.4);
    cursor: pointer;
  }
}

.bryzos {
  width: 100%;
  padding-bottom: 5px;
  display: flex;
  align-items: center;

  .headerTitle {
    display: flex;
    align-items: center;
    width: 100%;
    position: relative;
    a{
      text-decoration: none;
    }

    .bryzosTitle {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: 600;
      line-height: 1.4;
      text-align: left;
      color: #fff;
    }
    .addCursorPointer{
      cursor: pointer;
    }

    .bedgeLogout {
      width: 11px;
      height: 11px;
      background-color: #f00;
      border-radius: 50%;
      display: inline-flex;
      margin-left: 4px;
      position: relative;
      z-index: 1;
      top: 1.4px;
    }

    .bedgeLogin {
      width: 11px;
      height: 11px;
      background-color: #42ff00;
      border-radius: 50%;
      display: inline-flex;
      margin-left: 4px;
      position: relative;
      z-index: 1;
      cursor: pointer;
      top: 1.4px;
    }

  }

  #toggle-sticky-btn{
    .activePIn {
      display: none;
      margin-right: 12px;
    }
    &.pinFocus {
    

      .pinIcon {
        display: none;
        // background-color: rgba(255, 255, 255, 0.2);
        // border-radius: 2px;
      }

      .activePIn {
        display: block;
        margin-right: 12px;
        // background-color: rgba(255, 255, 255, 0.2);
        // border-radius: 2px;
      }
    }
  }


  .minimizeAppIcon {
    margin-right: 12px;
  }
  .shareAppIcon {
    margin-right: 12px;
    width: 100%;
    height: 100%;
  }
  .activeShareAppIcon{
    margin-right: 12px;
    width: 100%;
    height: 100%;
    svg {
      path{
        fill: #70ff00;
      }
    }
  }


  .headerText {
    font-family: Noto Sans;
    font-size: 14px;
    line-height: 1.2;
    text-align: left;
    color: #fff;
    margin-top: 5px;
    margin-right: 12px;
    cursor: pointer;
  }

  .showAlertForNewPo{
    background-color: #70ff00;
    color: rgb(0, 0, 0, 0.9);
    margin-top: 0;
    padding: 5px 8px;
    border-radius: 3px;
  }

  .headerTextActive {
    font-family: Noto Sans;
    font-size: 14px;
    line-height: 1.2;
    text-align: left;
    color: #70ff00;
    margin-top: 5px;
    margin-right: 12px;
    cursor: pointer;
  }

  .headerDisabled {
    font-family: Noto Sans;
    font-size: 14px;
    line-height: 1.2;
    text-align: left;
    color: #fff;
    opacity: 0.5;
    margin-top: 5px;
    margin-right: 12px;
    cursor: not-allowed;
  }

  .buttoncut {
    display: flex;
    width: auto;
    text-align: right;
    margin-left: auto;
    white-space: nowrap;

    svg {
      transition: all 0.1s;
      cursor: pointer;
      width: 24px;
      height: 24px;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
      }
    }

    .minimizeAppIconDisabled {
      margin-right: 12px;
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
      }
    }
  }
}


.headerPanel {
  margin: auto;
  width: 600px;
  position: relative;
  opacity:1;
}

.searchPanel {
  margin: auto;
  width: 600px;
  position: relative;

  .homeBody {
    border-radius: 10px;
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
    padding: 16px;

    &.bdrRadiusNone {
      border-radius: 0px;
    }


    input {
      font-family: Noto Sans;
      font-size: 20px;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      background-color: transparent;
      border: none;
      outline: none;
      width: 100%;

      &::placeholder {
        color: #fff;
        opacity: 0.5;
      }
    }
  }


}

.listBody {
  z-index: 99;
  width: 600px;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: unset !important;
  padding: 12px 16px 12px 16px;
  border-radius: 0px 0px 10px 10px;

  .ulBody {
    height: 100%;
    padding: 0 8px 0 0;
    overflow: auto;
    max-height: 425px;
    display: flex;
    flex-direction: column;
    grid-gap: 8px;
    scroll-behavior: smooth;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background:
        #9da2b2;
      border-radius: 50px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    .liBody {
      font-family: Noto Sans;
      font-size: 16px;
      line-height: 1.4;
      color: #fff;
      padding: 6px 8px;
      border-radius: 4px;
      // height: 112px;
      border: 1px solid transparent;
      display: flex;
      flex-direction: column;
      justify-content: center;
      transition: all 0.1s;
      cursor: pointer;
      font-weight: 300;

      &:hover {
        border: 1px solid #42ff00;
      }

      &.selectedLiBody {
        border: 1px solid #42ff00;
      }

      .liHead {
        font-weight: 600;
      }
    }
  }

  .selectProduct {
    height: 100%;
    .headingSelect {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 0px 0px 0px 0px;

      &.footerSection{
        padding: 0px;
      }

      .firstDiv {
        flex: 1;
        display: flex;
        gap: 12px;

        .resetIcon {
          border-radius: 8px;
          background-color: rgba(0, 0, 0, 0.25);
          height: 40px;
          transition: all 0.1s;
          .img1 {
              svg {
                rect {
                  fill: transparent;
                }
              }
            }

          svg {
            width: 40px;
            height: 40px;
            
          }

          .img2 {
            display: none;
          }

          .img3 {
            position: absolute;
            left: 68px;
          }

          &:hover {
            .img1 {
              display: none;
            }

            .img2 {
              display: block;
            }

            .img3 {
              display: none;
            }
          }
        }

        .joinBryzos {
          font-family: Noto Sans;
          font-size: 14px;
          font-weight: 500;
          line-height: 1.4;
          text-align: left;
          color: #ccc;
          opacity: 0.5;
          transition: all 0.1s;
        }
      }

      .secondDiv {
        display: flex;
        gap: 18px;
        align-items: center;

        .comingSoon {
          font-family: Roboto;
          font-size: 17px;
          text-align: left;
          color: #42ff00;
        }

        .buyNowBtn {
          width: 104px;
          height: 40px;
          border-radius: 4px;
          border: solid 1px #fff;
          font-family: Roboto;
          font-size: 14px;
          line-height: 1.2;
          text-align: center;
          color: #fff;
          padding: 3px;
          cursor: pointer;
        }

        .buyNowBtnInactive {
          cursor: not-allowed;
        }

        .showNewPoCliam {
          background-color: #70ff00;
          border-color: #70ff00;
          color: #000;
          padding: 3px 15px;
        }

        a {
          text-decoration: none;
        }

        .shareWidget {
          font-family: Noto sans;
          font-size: 14px;
          font-weight: 500;
          line-height: 1.4;
          text-align: left;
          color: #ccc;
          opacity: 0.5;
          transition: all 0.1s;

          &:hover {
            opacity: unset;
          }
        }
      }
    }


    .liBodyList {
      font-family: Noto Sans;
      font-size: 16px;
      line-height: 1.4;
      color: #fff;
      font-weight: 400;
      padding: 8px 8px;
      border-radius: 8px;
      // height: 128px;
      width: 100%;
      border: 1px solid transparent;
      cursor: pointer;
      margin-bottom: 5px;
      transition: all 0.1s;

      /* HIDE RADIO */
      [type=radio] {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
      }

      // &:hover {
      //   border: 1px solid #42ff00;
      // }

      .liHead {
        font-weight: 600;
      }

      &.clickToShare {
        background-color: rgba(255, 255, 255, 0.75);
        color: #14181b;

        .priceFeedback {
          .CheckBtn {
            svg {
              path {
                fill: #14181b;
              }
            }

            &:hover {
              svg {
                path {
                  fill: #14181b !important;
                }
              }

            }
          }
        }

        .hightBtn,
        .lowBtn {
          svg {
            g {
              opacity: unset;
            }

            path {
              opacity: 0.3;
              fill: #14181b;
            }
          }
        }

        &:hover {
          border: 1px solid transparent;
        }

        .priceRating {

          .selectPrize {
            color: #000;
          }

          .notSelectPrize {
            color: #000;
          }
        }

        .liHead {
          font-weight: 600;
        }
      }

      .liHeadList {
        font-weight: 500;
      }

      .priceRating {
        display: flex;
        align-items: center;
        justify-content: right;
        text-align: right;
        gap: 12px;

        .selectPrize {
          font-family: Roboto;
          font-size: 32px;
          text-align: left;
          color: #fff;
          display: flex;
          align-items: flex-start;
          gap: 8px;
          line-height: 1.2;

          .doller {
            font-size: 24px;
            line-height: 1;
          }

          .prize {
            display: flex;
            align-items: flex-start;
            line-height: 1;
          }
        }

        .notSelectPrize {
          font-family: Roboto;
          font-size: 16px;
          color: #fff;
          line-height: 1.4;
        }

        label {
          height: 24px;
          display: flex;
          svg{
            height: 24px;
            width: 24px;
          }
        }
      }
    }
  }
}


.lineH {
  padding: 12px 0px;
  position: relative;
  &::after {
    content: "";
    width: 100%;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
  }
}

.hidden {
  display: none;
}


// share widget page design 
.widgetBodyHeader {
  padding: 8px 16px 8px 16px;
  margin: auto;
  width: 600px;
  background-color: rgba(0, 0, 0, 0.9);
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  //margin-top: 20px;

  .bryzos {
    margin: 0;
  }
}

.widgetBody {
  margin: auto;
  width: 600px;
  padding: 20px 24px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  @media (max-width:767px) {
    width: 100%;
    padding: 0px 16px;
  }

  .widgetHead {
    font-family: Noto Sans;
    font-size: 20px;
    font-weight: 500;
    line-height: 1.4;
    text-align: left;
    color: #70ff00;
    margin-bottom: 16px;
    @media (max-width:767px) {
      font-size: 14px;
      margin-bottom: 12px;
    }
  }

  .widgetEmailInput {
    padding: 10px 12px;
    border-radius: 2px;
    border: solid 1px rgba(255, 255, 255, 0.5);
    background-color: #fff;
  }

  .widgetTextInput {
    height: 133px;
    margin-top: 25px;
    padding: 10px 12px;
    border-radius: 2px;
    border: solid 1px rgba(255, 255, 255, 0.5);
    background-color: #fff;
    @media (max-width:767px) {
      padding: 6px 12px;
      margin-top: 26px;
      font-size: 14px;
    }
  }

  .widgetEmailInput,
  .widgetTextInput {
    input {
      font-family: Noto Sans;
      font-size: 16px;
      line-height: 1.4;
      text-align: left;
      border: none;
      color: #14181b;
      outline: none;
      width: 100%;
      @media (max-width:767px) {
        font-size: 14px;
      }

      &::placeholder {
        color: rgba(0, 0, 0, 0.5);
      }
    }
  }

  .widgetButtons {
    text-align: right;
    margin-top: 16px;

    .cancelBtn {
      font-family: Noto Sans;
      font-size: 18px;
      line-height: 1.6;
      text-align: left;
      color: rgba(255, 255, 255, 0.3);
      background-color: transparent;
      border: none;
      margin-right: 40px;
      cursor: pointer;
      @media (max-width:767px) {
        font-size: 16px;
        margin-right: 24px;
      }


      &:hover {
        color: #fff;
      }
    }

    .sendBtn {
      padding: 8px 32px;
      border-radius: 4px;
      background-color: #fff;
      font-family: Noto Sans;
      font-size: 18px;
      font-weight: 500;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: left;
      color: #333;
      cursor: pointer;
      transition: all 0.1s;
      border: none;
      @media (max-width:767px) {
        padding: 6px 24px;
        font-size: 16px;
      }

      &:focus {
        outline: none;
      }

      &:hover {
        background-color: #70ff00;
        color: #000;
      }
      
      &:disabled {
        background-color: #70ff00;
        color: #000;
        opacity: 0.5;
        cursor: not-allowed;
    }
    }
  }

  .errorText1 {
    font-family: Noto Sans;
    font-size: 16px;
    line-height: 1.6;
    text-align: left;
    color: #f00;
    position: absolute;
    @media (max-width:767px) {
      font-size: 14px;
    }
  }

  // tnc 
  .joinBryzos {
    padding: 52px 40px;
    border-radius: 8px;
    box-shadow: inset 0 0 24px 6px rgba(0, 0, 0, 0.5);
    background-color: #fff;
    position: relative;

    .tncIcon {
      text-align: right;
      position: absolute;
      right: 8px;
      top: 10px;

      &:hover {
        .img1 {
          display: none;
        }

        .img2 {
          display: block;
        }
      }

      .img2 {
        display: none;
      }
    }

    .joinBryzosHead {
      font-family: Noto Sans;
      font-size: 24px;
      font-weight: bold;
      line-height: 1.6;
      text-align: left;
      color: #424656;

      span {
        font-family: Noto Sans;
        font-size: 24px;
        font-weight: bold;
        line-height: 1.6;
        text-align: left;
        color: #397aff;
        text-decoration: none;
        padding-left: 5px;
      }
    }

    .joinBryzosContent {
      font-family: Noto Sans;
      font-size: 18px;
      line-height: 1.6;
      text-align: left;
      color: #424656;
    }
  }
}

.smsTextShare {
  width: 100%;
  height: 100%;
  resize: none;
  font-family: Noto Sans;
  font-size: 16px;
  border: 0px;
  @media (max-width:767px) {
    font-size: 14px;
  }

  &:focus {
    outline: none;
  }

  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background:
      #9da2b2;
    border-radius: 50px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

.unitDropdown.unitDropdown {
  width: 82px;
  height: 40px;
  border-radius: 4px;

  .MuiSelect-select {
    padding: 10px 6px 10px 9px !important;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.25);
    display: flex;
    align-items: center;
    border: 1px solid transparent;
    font-family: Roboto;
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    text-align: left;
    color: #fff;

    &[aria-expanded="true"] {
      border: 1px solid #fff;
    }
  }

  .MuiSelect-icon {
    top: unset;
    transform: unset;
    width: 20px;
    height: 20px;
  }

  fieldset {
    border: 0px;
    height: 100%;
  }
}
.multipleunitDropdown.multipleunitDropdown{
  width: 140px;
}
.SelectUnitDropdown.SelectUnitDropdown {
  padding: 8px;
  border-radius: 4px;
  -webkit-backdrop-filter: blur(6px);
  backdrop-filter: blur(6px);
  background-color: rgba(255, 255, 255, 0.7) !important;
  margin-top: 2px;
  background: url(assets/images/DropDownBG.png);

  ul {
    padding: 0px;

    li {
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      padding: 4px 12px;
      border-radius: 4px;
      height: 29px;

      &.Mui-selected {
        background-color: transparent !important;
      }

      &:hover {
        background-color: #fff !important;
        color: #000;
      }
    }
  }

}

// success popup

.successBody {
  margin: auto;
  width: 600px;
  padding: 20px 24px;
  background-color: rgba(0, 0, 0, 0.75);
  -webkit-backdrop-filter: blur(60px);
  backdrop-filter: blur(60px);
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  height: 332px;
  text-align: center;

  .greenText {
    font-family: Noto Sans;
    font-size: 24px;
    font-weight: 500;
    line-height: 1.4;
    color: #70ff00;
    padding-top: 84px;
  }

  .whiteText {
    font-family: Noto Sans;
    font-size: 20px;
    line-height: 1.4;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    cursor: pointer;
  }
}

.selectedPordListScroll {
  height:calc(100% - 140px);
  overflow: auto;
  margin-top: 8px;
}

.priceFeedback {
  .CheckBtn {
    .img2 {
      display: none;
    }

    &:hover {
      .img1 {
        display: none;
      }

      .img2 {
        display: block;
      }
    }
  }

  .goodBtn {
    &:hover {
      svg {
        path {
          g {
            opacity: unset;
          }

          path {
            fill: #70ff00;
            opacity: unset;
          }
        }
      }
    }
  }
}

.TermsTitle {
  font-family: Noto Sans;
  font-size: 14px;
  font-weight: 900;
  line-height: 2;
  text-align: left;
  color: #525f7f;
  margin-bottom: 8px;
}

.ParaText {
  font-family: Noto Sans;
  font-size: 12px;
  font-weight: normal;
  line-height: 2;
  text-align: left;
  color: #525f7f;
  margin-bottom: 12px;
}

// tooltip css 
.MuiPopper-root {
  .tooltipText{
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #000;
  }

  .shareappTooltip.shareappTooltip{
    background-color: rgba(255, 255, 255, 0.7);
    padding: 8px 12px;
    border-radius: 3px;
    max-width: 205px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    top: -6px;
    .MuiTooltip-arrow {
      height: 12px;
      width: 18px;
      color: rgba(255, 255, 255, 0.7);
      top: -4px;
    }
  }

  .priceUnitChangeTooltip.priceUnitChangeTooltip{
    background-color: rgba(255, 255, 255, 0.7);
    padding: 6px 12px;
    border-radius: 3px;
    max-width: 262px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    top: -11px;
    left: -7px;
    .MuiTooltip-arrow {
      height: 12px;
      width: 18px;
      color: rgba(255, 255, 255, 0.7);
      top: -4px;
      left: 5px !important;
    }
  }

  .chooseYourUnitTooltip.chooseYourUnitTooltip{
    background-color: rgba(255, 255, 255, 0.7);
    padding: 8px 12px;
    border-radius: 3px;
    max-width: 232px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    top: -5px;
    .MuiTooltip-arrow {
      height: 12px;
      width: 18px;
      color: rgba(255, 255, 255, 0.7);
      top: -4px;
    }
  }

  .orderPageTooltip.orderPageTooltip{
    background-color: rgba(255, 255, 255, 0.7);
    padding: 8px 12px;
    border-radius: 3px;
    max-width: 307px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    top: -8px;
    .MuiTooltip-arrow {
      height: 12px;
      width: 18px;
      color: rgba(255, 255, 255, 0.7);
      top: -4px;
    }
  }

  .signupTooltip.signupTooltip {
    background-color: rgba(255, 255, 255, 0.7);
    padding: 6px 12px;
    border-radius: 3px;
    max-width: 360px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    top: -2px;

    .MuiTooltip-arrow {
      margin-right: -11px;
      height: 16px;
      width: 11px;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .shareWidgetTooltip {
    background-color: rgba(255, 255, 255, 0.7) !important;
    padding: 12px 16px !important;
    border-radius: 6px;
    max-width: 380px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);

    .shareWidgetText {
      font-family: Noto Sans;
      font-size: 18px;
      font-weight: normal;
      line-height: 1.6;
      text-align: left;
      color: #000;
    }

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.7);
      width: 25px;
      height: 16px;
      bottom: -8px !important;
      left: -15px !important;
    }
  }

  .priceFeedBakTooltip {
    background-color: #fff !important;
    padding: 8px 40px 8px 16px !important;
    border-radius: 6px;
    left: 15px;
    margin-bottom: 10px !important;
    max-width: 334px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);

    .headTooltip {
      font-family: Noto Sans;
      font-size: 20px;
      font-weight: bold;
      text-align: left;
      color: #333;

      span {
        font-style: italic;
      }
    }

    .MuiTooltip-arrow {
      color: #fff;
      width: 23px;
      height: 18px;
      bottom: -8px !important;
      left: -15px !important;
    }
  }

  .rightTooltip {
    background-color: rgba(255, 255, 255, 0.7) !important;
    padding: 6px 12px !important;
    border-radius: 4px;
    max-width: 315px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    margin-left: 10px !important;
    

    .resetTooltipText {
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #000;
    }

    .MuiTooltip-arrow {
      width: 12px !important;
      height: 18px !important;
      left: -4px !important;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .acceptTooltip {
    background-color: rgba(255, 255, 255, 0.7) !important;
    padding: 6px 12px !important;
    border-radius: 6px !important;
    max-width: 381px !important;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);

    .acceptText {
      font-family: Noto Sans;
      font-size: 12px;
      line-height: 1.4;
      text-align: left;
      color: #000;
    }

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.7);
      width: 16px;
      height: 12px;
      top: -4px !important;
      left: 1px !important;
    }
  }

  .inputTooltip {
    background-color: rgba(255, 255, 255, 0.7) !important;
    padding: 6px 12px !important;
    border-radius: 6px !important;
    max-width: 381px !important;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    margin-left: 7px !important;
    margin-bottom: 7px !important;
    margin-top: 7px !important;

    .acceptText {
      font-family: Noto Sans;
      font-size: 12px;
      line-height: 1.4;
      text-align: left;
      color: #000;
    }

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.7);
    }
  }
  .addressTooltip {
    background-color: rgba(255, 255, 255, 0.7) !important;
    padding: 6px 12px !important;
    border-radius: 6px !important;
    max-width: 381px !important;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    margin-left: 7px !important;
    margin-bottom: 7px !important;
    margin-top: 7px !important;
    font-family: Noto Sans;
    font-size: 12px;
    line-height: 1.4;
    text-align: left;
    color: #000;

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.7);
    }
  }
  .partNumberTooltip {
    background-color: rgba(255, 255, 255, 0.7) !important;
    padding: 6px 12px !important;
    border-radius: 6px !important;
    max-width: 381px !important;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    margin-left: 7px !important;
    margin-bottom: 7px !important;
    margin-top: 7px !important;
    font-family: Noto Sans;
    font-size: 12px;
    line-height: 1.4;
    text-align: left;
    color: #000;

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.7);
      left: -75px !important;
    }
  }

  .inputQtyTooltip {
    background-color: #ff0000;
    padding: 6px 12px;
    border-radius: 6px;
    max-width: 381px;
    margin-left: 7px;
    margin-bottom: 7px !important;
    margin-top: 7px;
    font-family: Noto Sans;
    font-size: 12px;
    line-height: 1.4;
    text-align: left;
    color: #fff;

    .MuiTooltip-arrow {
      color: #ff0000;
    }
  }
  .orderPreviewTooltip {
    background-color: rgba(255, 255, 255, 0.7);
    padding: 12px;
    border-radius: 6px;
    max-width: 381px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    margin-left: 7px;
    margin-bottom: 7px !important;
    margin-top: 15px !important;


    .acceptText {
      font-family: Noto Sans;
      font-size: 18px;
      line-height: 1.6;
      text-align: left;
      color: #000;
    }

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.7);
      height: 16px;
      width: 24px;
      top: -8px !important;
      
    }
  }
}

.containTooltip {
  font-family: Roboto;
  font-size: 18px;
  line-height: 1.6;
  text-align: left;
  color: #333;
  display: flex;
  margin-bottom: 8px;
  align-items: center;
  gap: 5px;
}

// TnC

.TncContainer {
  width: 100%;
  height: 560px;
  padding: 32px 32px 32px 32px;
  border-radius: 8px;
  box-shadow: inset 0 0 24px 6px rgba(0, 0, 0, 0.5);
  background-color: #fff;

  .termsAndConditions {
    position: relative;
    overflow: auto;
    max-height: 495px;

    &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background:
        #9da2b2;
      border-radius: 50px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    .tncIcon {
      text-align: right;
      position: absolute;
      right: 8px;
      top: 10px;

      &:hover {
        .img1 {
          display: none;
        }

        .img2 {
          display: block;
        }
      }

      .img2 {
        display: none;
      }
    }

    .termsAndConditionsHead {
      font-family: Noto Sans;
      font-size: 24px;
      font-weight: bold;
      line-height: 1.6;
      text-align: left;
      color: #525f7f;

      span {
        font-family: Noto Sans;
        font-size: 24px;
        font-weight: bold;
        line-height: 1.6;
        text-align: left;
        color: #397aff;
        text-decoration: none;
        padding-left: 5px;
      }
    }

    .tncHead {
      font-family: Noto Sans;
      font-size: 24px;
      font-weight: bold;
      line-height: 1.6;
      text-align: left;
      color: #424656;
      margin-bottom: 8px;
    }

    .effectiveDate {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: left;
      color: #424656;
      margin-bottom: 16px;
    }

    .TnCTitle {
      font-family: Noto Sans;
      font-size: 22px;
      font-weight: 600;
      line-height: 1.6;
      text-align: left;
      color: #424656;
      margin-bottom: 4px;
    }

    .TnCTitle1 {
      font-size: 18px;
    }

    .TnCTitle2 {
      font-size: 14px;
    }

    .TnCPara {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.6;
      text-align: left;
      color: #424656;
      margin-bottom: 12px;
    }

    .TnCList {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.6;
      text-align: left;
      color: #424656;
      padding-left: 29px;
      margin-bottom: 4px;

      li {
        margin-bottom: 8px;
        list-style-type: disc;
      }
    }

    .TnCList1 {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.6;
      text-align: left;
      color: #424656;
      padding-left: 29px;
      margin-bottom: 4px;

      li {
        margin-bottom: 8px;
        list-style-type: decimal;
      }
    }

    .termsAndConditionsContent {
      font-family: Noto Sans;
      font-size: 18px;
      line-height: 1.6;
      text-align: left;
      color: #525f7f;
    }

    .tncContent {
      font-family: Noto Sans;
      font-size: 18px;
      line-height: 1.6;
      text-align: left;
      color: #525f7f;
    }


  }
}
.tncOnboarding{
  .TnCTitle {
    font-family: Noto Sans;
    font-size: 22px;
    font-weight: 600;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    margin-bottom: 4px;
  }
  .TnCTitle2 {
    font-size: 14px;
  }

  .TnCPara {
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    margin-bottom: 12px;
  }
  .TnCList {
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    padding-left: 29px;
    margin-bottom: 4px;

    li {
      margin-bottom: 8px;
      list-style-type: disc;
    }
  }

  .TnCList1 {
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    padding-left: 29px;
    margin-bottom: 4px;

    li {
      margin-bottom: 8px;
      list-style-type: decimal;
    }
  }
}

.tncButtons {
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 20px;

  .disagreeBtn {
    font-family: Noto Sans;
    cursor: pointer;
    transition: all 0.1s;
    border: none;
    font-size: 20px;
    line-height: 1.6;
    color: #fff;
    background: transparent;
    margin-right: auto;
  }

  .agreeBtn {
    transition: all 0.1s;
    margin-left: auto;
    opacity: 0.5;
    font-family: Noto Sans;
    font-size: 20px;
    line-height: 1.6;
    text-align: center;
    color: #fff;
    background: transparent;
    cursor: not-allowed;
    border: 0px;

    &:focus {
      outline: none;
    }
  }

  .agreeBtnEnable {
    cursor: pointer;
    color: #70ff00;
    opacity: unset;
  }

  .downloadTnCBtn{
    font-family: Noto Sans;
    font-size: 14px;
    line-height: 1.5;
    text-align: center;
    color: #fff;
    transition: all 0.1s;
    cursor: pointer;
    margin-right: auto;
    // &:hover{
    //   color: #70ff00;
    // }
  }

  .agreeBtn1 {
    transition: all 0.1s;
    // margin-left: auto;
    font-family: Noto Sans;
    font-size: 20px;
    line-height: 1.6;
    text-align: center;
    color: #fff;
    background: transparent;
    border: 0px;
    cursor: pointer;

    &:focus {
      outline: none;
    }

    // &:hover {
    //   color: #70ff00;
    // }
  }
}

.mainContent {
  width: 100%;
  height: 100%;
  // -webkit-backdrop-filter: blur(60px);
  // backdrop-filter: blur(60px);
  // background-color: rgba(0, 0, 0, 0.75);
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.selectDropdown {
  // height: 100%;
  width: 100%;

  .MuiSelect-icon {
    right: 0px;
    // top: calc(50% - 0.5em);
    transform: unset;
  }

  .MuiSelect-select {
    padding: 0px;
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
  }

  svg {
    color: #fff;
    right: -2px;
    transform: unset;
  }

  fieldset {
    border: 0px;
  }
}

.SelectDeliveryDate{
  padding-left: 10px;
  .MuiSelect-icon {
    right: 6px;
    top: calc(50% - 0.5em);
  }
}



.selectUploadCertDropdown.selectUploadCertDropdown {
  // height: 100%;
  width: 100%;
  margin-bottom: 4px;

  .selectDropdown.selectDropdown {
    padding-right: 22px;
  }  

  .MuiSelect-icon {
    right: 1px;
    top:unset;
    transform: unset;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
  }

  .MuiSelect-select {
    padding: 0px;
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: 300;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    padding: 1px 4px 1px 8px;
    border-radius: 2px;
    background-color: rgba(255, 255, 255, 0.2);
    padding-right: 0px;
  }

  svg {
    color: #fff;
    right: -2px;
    transform: unset;
  }

  fieldset {
    border: 0px;
  }
}

.selectReceivingHours.selectReceivingHours {
  font-family: Noto Sans;
  font-size: 12px;
  font-weight: 300;
  line-height: 1;
  text-align: left;
  color: #fff;
  padding: 0px;
  min-width: 64px;
  max-height: 21px;
  margin-bottom: 6px;

  svg {
    display: none;
  }

  .selectDropdown.selectDropdown {
    padding: 0px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 2px 0px 2px 6px;
  }

  fieldset {
    border: 0px;
  }
}

.btnExpiration.btnExpiration {
  width: 76px;
  height: 20px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 2px 3px;
  border-radius: 2px;
  font-family: Noto Sans;
  font-size: 10px;
  font-weight: normal;
  line-height: 1.6;
  text-align: left;
  color: #fff;

  svg {
    color: #fff;
    right: -2px;
    transform: unset;
  }

  .selectDropdown.selectDropdown {
    padding: 0px 12px 0px 0px;
  }

  fieldset {
    border: 0px;
  }
}


.scrollContent {
  overflow: auto;
  max-height: 600px;
  padding-right: 4px;

  &.scrollSeller {
      max-height: 560px;
  }

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

// .createPoScroll{
//   max-height: 540px;
//   padding-bottom: 3px;

//   &::-webkit-scrollbar {
//     display: none;
//   }
 
// }

.loaderMain {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 600px;

  &.loaderSeller {
    min-height: 560px;
  }

  &.loaderCreatePo {
    min-height: 650px;
  }
}

.loginLoader{
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  inset: 0;
  z-index: 1000;
  // background: black;
  // opacity: 0.2;
}
.isNotProd{
  background-color: #2196F3;
  border-radius: 6px;
}
.isDemo{
  background-color: rgba(33, 244, 77, 0.34);
  border-radius: 6px;
}

.datePickerCreatePO {
  width: 100%;
  padding-top: 0px;
  overflow: hidden;

  .MuiOutlinedInput-root {
    padding: 0px;
  }

  .MuiTextField-root {
    width: 100%;
  }

  .MuiIconButton-root {
    padding: 0px;
    margin: 0px;

    svg {
      path {
        fill: #fff
      }
    }
  }

  fieldset {
    border: 0px;
  }
}
.MuiPickersPopper-root{
  .MuiPaper-root{
    color: #fff;
    backdrop-filter: blur(24px);
    background-color: rgba(0, 0, 0, 0.25);
    margin-top: 7px;
    .MuiPickersArrowSwitcher-root{
      button{
        color: #fff;
      }
      .Mui-disabled{
        opacity: 0.5;
      }
    }
    .MuiDayCalendar-weekDayLabel{
      color: #fff;
    }
    .MuiPickersFadeTransitionGroup-root{
      .MuiPickersDay-root{
        color: #fff;
      }
      .Mui-disabled{
        opacity: 0.5;
      }
    }

  }
}
.loaderClass{
  text-align: center;
}

.MuiAutocomplete-listbox.MuiAutocomplete-listbox {
  span.Mui-focused {
    border-radius: 2px;
    background-color: #fff;
    color: #000;
  }
  span[aria-selected="true"].Mui-focused {
    border-radius: 2px;
    background-color: #fff;
    color: #000;
  }
}
.resetPassword{
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 10px 0px 0px 10px;
  padding: 0px 16px 16px 16px;
  padding-top: 46px;
  display: flex;
  align-items: baseline;
  height: 100%;
  .emailDiv{
    width: 65%;
    input{
      width: 100%;
      font-family: Noto Sans;
      font-size: 20px;
      line-height: 1.2;
      text-align: left;
      color: #fff;
      background-color: transparent;
      border: none;
      outline: none;
    }
    .errorText {
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.6;
      text-align: left;
      color: #f00;
    }
  }
  .passDiv{
    width: 40%;
    margin-right: 0px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    &:nth-child(2){
      padding-left: 12px;
      margin-right: 0px;
    }
    input{
      width: 100%;
      font-family: Noto Sans;
      font-size: 16px;
      line-height: 1.2;
      text-align: left;
      color: #fff;
      background-color: transparent;
      border: none;
      outline: none;
    }
    .errorText {
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.6;
      text-align: left;
      color: #f00;
      &.errorForgetPass{
        font-size: 12px;
      }
    }
  }
  .resetBtnDiv{
    width: 35%;
    display: flex;
    gap: 10px;
    justify-content: right;
    .resetPassBtn{
      padding: 8px 5px;
      border-radius: 4px;
      background-color: #70ff00;
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.4;
      text-align: center;
      color: #000000;
      font-weight: 500;
      border: 1px solid transparent;
    }
    .nextBtn{
      padding: 8px 12px;
      border-radius: 4px;
      background-color: #70ff00;
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.4;
      text-align: center;
      color: #000000;
      font-weight: 500;
      border: 1px solid transparent;
    }
    .backBtn{
      padding: 8px 12px;
      border-radius: 4px;
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.4;
      text-align: center;
      color: #fff;
      font-weight: 500;
      border: 1px solid #fff;
    }
  }
  .passwordBox {
    display: flex;
    gap: 8px;

    input {
      border-bottom: 2px solid rgba(255, 255, 255, 0.5);
      font-family: Noto Sans;
      font-size: 20px;
      line-height: 1.2;
      text-align: center;
      color: #fff;
      width: 22px !important;
    }
  }
}
.forgotPassText{
  font-family: Noto Sans;
  font-size: 14px;
  line-height: 1.6;
  text-align: left;
  color: #70ff00;
  cursor: pointer;
}

.UpdateButton{
  .MuiPaper-root{
    width: 100%;
    height: 100%;
    max-height: 100%;
    margin: 0;
    background-color: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    button{
      font-size: 16px;
      color: #fff;
      border: 1px solid #fff;
      max-width:200px;
      padding: 8px 16px;
      transition: all 0.1s;
      border-radius: 4px;
      &:hover{
        background-color: #fff;
        color: #000;
      }
    }
  }
}
.excitingNews{
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
  justify-content: center;
  color: #fff;
  padding: 15px;
  label{
    font-size: 16px;
    line-height: 1.2;
    font-weight: 500;
    padding-bottom: 6px;
  }
  p{
    font-size: 13px;
    text-align: center;
    line-height: 1.2;
    font-weight: 300;
  }
  span{
    font-size: 13px;
    line-height: 1.2;
    font-weight: 500;
    cursor: pointer;
    text-decoration: underline;
    &:hover{
      color: #70ff00;
    }
  }
}

.selectDropdown1.selectDropdown1 {
  width: 380px;
  height: 40px;
  padding: 10px 0px;

  .dropdownPlaceholderText{
    font-weight: 300;
  }

  &.dropdownFocused {
    box-shadow: 0 0 25px 0 rgba(0, 0, 0, 0.5);
    background-color: rgba(255, 255, 255, 0.75);
    border-radius: 4px 4px 0px 0px;

    .MuiSelect-select {
      color: rgba(0, 0, 0, 0.75);
      background-color: rgba(255, 255, 255, 0.75);
    }

    .MuiSelect-icon {
      color: #474747;
    }

    fieldset {
      border-radius: 4px 4px 0px 0px;
      border: 0;
    }

  }

  &:hover {
    fieldset {
      border: solid 0.5px #fff;
    }
  }

  .MuiSelect-icon {
    right: 0px;
    transform: unset;
    font-size: 34px;
  }

  .MuiSelect-select {
    padding: 0 32px 0 10px;
    line-height: 46px;
    font-family: Noto Sans Display;
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    color: #fff;
  }

  svg {
    color: #fff;
    right: -2px;
    transform: unset;
  }

  fieldset.MuiOutlinedInput-notchedOutline {
    border-radius: 4px;
    border: solid 0.5px #fff;
  }
}

.selectUserType{
  ul{
    li{
      &.Mui-focusVisible{
         background-color: transparent;
      }
    }
  }
}

.displayNone{
  display: none;
}


.ErrorDialog {
  .dialogContent {
      max-width: 300px;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      padding: 30px 34px 30px 34px;
      object-fit: contain;
      border-radius: 10px;
      -webkit-backdrop-filter: blur(24px);
      backdrop-filter: blur(24px);
      box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
      background-color: rgba(0, 0, 0, 0.72);
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.6;
      text-align: center;
      color: #fff;

      p {
          margin-bottom: 20px;
      }


      .submitBtn {
          height: 40px;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          padding: 10px 24px;
          border-radius: 4px;
          border: solid 0.5px #fff;
          background-color: transparent;
          font-family: Noto Sans;
          font-size: 14px;
          font-weight: normal;
          line-height: 1.4;
          text-align: center;
          color: #fff;
          margin-top: 20px;
          transition: all 0.1s;

          &:hover {
              background-color: #70ff00;
              border: solid 0.5px #70ff00;
              color: #000;
          }

          &:disabled {
              opacity: 0.5;
              cursor: not-allowed;

              &:hover {
                  border: solid 0.5px #fff;
                  background-color: transparent;
                  color: #fff;
              }
          }
      }


  }

}
.questionIconPriceChange {
  display: flex;
  align-items: center;
  transition: all 0.1s;
  svg{
    height: 20px;
    width: 20px;
    cursor: pointer;
  }

  .questionIcon2 {
      display: none;
  }

  &:hover {
      .questionIcon1 {
          display: none;
      }

      .questionIcon2 {
          display: block;
      }
  }
}

.tooltipPopper.tooltipPopper {
  pointer-events: unset;

  &.tooltipSearchList{

    &[data-popper-placement*="bottom"] .tooltipRight2{
      
        width: 265px;
        label{
          margin-top: 8px;
        }
        .tooltipArrow {
          &.MuiTooltip-arrow {
            left: 234px !important;
            top:-12px;
      }
    }
    }

     &[data-popper-placement*="top"] .tooltipRight2{
      width: 265px;
      label{
        margin-top: 8px;
      }
      .tooltipArrow {
        &.MuiTooltip-arrow {
          left: 234px !important;
          top:auto;
          margin-bottom: -1em;
    }
  }
}
  }
  .tooltipMain.tooltipMain {
    width: 208px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 5px 12px;
    border-radius: 3px;
    -webkit-backdrop-filter: blur(4.5px);
    backdrop-filter: blur(4.5px);
    background-color: rgba(255, 255, 255, 0.7);
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #000;
    margin-top: 8px;

    &.tooltipMainLogout {
      width: 228px;
      margin-top: 8px;
      padding: 8px 12px;
      left: -9px;

      .tooltipArrow {
        &.MuiTooltip-arrow {
          left: 6px !important;
          top: -12px;
          margin-top: 0px;
          width: 18px;
          height: 12px;

          &::before {
            width: 100%;
            height: 100%;
          }
        }
      }

      label {
        margin-top: 6px;
      }
    }

    &.tooltipCenter {
      width: 500px;
      min-width: 500px;
      padding: 3px 12px;
      margin-top: 4px;

      label {
        margin-top: 3px;
      }
    }

    &.tooltipRight {
      width: 450px;
      min-width: 450px;
      padding: 4px 8px 4px 12px;
      margin-top: 3px;

      &.tooltipRight1 {
        width: 312px;
        min-width: 312px !important;
      }

      label {
        margin-top: 3px;
      }
    }

    &.tooltipDesc{
      width: 183px;
      min-width: 183px;
      margin-bottom: 20px;
      left: 3px;
      .tooltipArrow {
        &.MuiTooltip-arrow {
          top:auto;
          margin-bottom: -1em;
    }
  }
    }

    &.tooltipSearch {
      width: 382px;
      min-width: 382px;
      padding: 8px 12px;
      margin-top: 26px;
      label {
        margin-top: 8px;
      }
      .tooltipArrow {
        &.MuiTooltip-arrow {
          left: -265px !important;
        }
      }
      &.tooltipPayment{
        width: 293px;
        min-width: 293px;
        .tooltipArrow {
          &.MuiTooltip-arrow {
            left: -115px !important;
          }
        }
      }

      &.tooltipAddRow{
      width: 208px;
      min-width: 208px;
      margin-top: 12px;
      .tooltipArrow {
        &.MuiTooltip-arrow {
          left: -178px !important;
        }
      }
      }
      &.tooltipRemove{
        width: 200px;
        min-width: 200px;
        margin-top: -18px;
        left: -11px;
        .tooltipArrow {
          &.MuiTooltip-arrow {
            left: 6px !important;
          }
        }
      }
    }

    .tooltipArrow {
      &.MuiTooltip-arrow {
        left: -4px !important;
        top: -12px;
        margin-top: 0px;
        width: 18px;
        height: 12px;

        &::before {
          width: 100%;
          height: 100%;
          background-color: rgba(255, 255, 255, 0.7);
        }
      }
    }
  }

  .tooltipMain2.tooltipMain2 {
    width: 230px;
    height: 78px;
    padding: 8px 12px;
    border-radius: 3px;
    -webkit-backdrop-filter: blur(4.5px);
    backdrop-filter: blur(4.5px);
    background-color: rgba(255, 255, 255, 0.7);
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #000;
    margin-right: 12px;

    &.tooltipOrder{
      margin-top: -4px;
      .tooltipLeftArrow {
        &.MuiTooltip-arrow {
          margin-top: 4px;
        }
      }
    }

    .tooltipLeftArrow {
      &.MuiTooltip-arrow {
        width: 12px;
        height: 21px;
        color: rgba(255, 255, 255, 0.7);
        margin-right: -1em;
      }
    }

    label {
      margin-top: 8px;
    }
  }

  .tooltipMain3.tooltipMain3{
    width: 204px;
    margin-top: 10px;
    margin-right: 0px;
    .tooltipBottomEndArrow {
      &.MuiTooltip-arrow {
        width: 22px;
        height: 14px;
        color: rgba(255, 255, 255, 0.7);
        top: -5px;
      }
    }
  }
  .tooltipMain4.tooltipMain4{
    width: 227px;
    .tooltipLeftArrow1 {
      &.MuiTooltip-arrow {
        width: 12px;
        height: 21px;
        color: rgba(255, 255, 255, 0.7);
        margin-top: -8px;
        margin-right: -1em;
      }
    }
  }
  .tooltipMain5.tooltipMain5{
    width: 243px;
    margin-left: 12px;
    .tooltipLeftArrow1 {
      &.MuiTooltip-arrow {
        width: 12px;
        height: 21px;
        color: rgba(255, 255, 255, 0.7);
        margin-top: -12px;
        margin-left: -1em;
      }
    }
  }
  .tooltipMain6.tooltipMain6{
    margin-left: 15px;
    min-width: 359px;
    height: 61px;
    .tooltipLeftArrow1 {
      &.MuiTooltip-arrow {
        width: 12px;
        height: 21px;
        color: rgba(255, 255, 255, 0.7);
        left: -3.1px;
      }
    }
  }
  .tooltipMain7.tooltipMain7{
    min-width: 337px;
    min-height: 95px;
    margin-top: 12px;
    margin-right: 0px;
    .tooltipLeftArrow1 {
      &.MuiTooltip-arrow {
        width: 19px;
        height: 13px;
        color: rgba(255, 255, 255, 0.7);
        top: -3.1px;
        margin-top: -10px;
      }
    }
  }

}

.pinTooltipPopper.pinTooltipPopper{
  .pinTooltip.pinTooltip {
    background-color: rgba(255, 255, 255, 0.7);
    padding: 6px 12px;
    border-radius: 3px;
    max-width: 347px;
    top: -8px;

    .pinText {
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #000;
    }

    .arrowTooltip{
      &.MuiTooltip-arrow {
        width: 22px;
        height: 14px;
        color: rgba(255, 255, 255, 0.7);
        margin-top: -1.29em;
        left: -5px !important;
      }
    }
   
  }
}


.bgBackgroundColor {

  .loaderContent,
  .widgetBody,
   .tncBg,
   .bgBlurContent{
    -webkit-backdrop-filter: blur(60px);
    backdrop-filter: blur(60px);
    background-color: rgba(0, 0, 0, 0.75);
  }

.listBody {
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(60px);
}

.searchPanel {
  .homeBody {
    -webkit-backdrop-filter: blur(60px);
    backdrop-filter: blur(60px);
    background-color: rgba(0, 0, 0, 0.75);
  }
}

.resetPosition{
   background-color: #000;
}

.loginBody {
  backdrop-filter: blur(24px);
  background-color: rgba(0, 0, 0, 0.6);

  table {
  
  
    tr {
      td {
      
        &:nth-child(1) {
           background-color: rgba(0, 0, 0, 0.4);
        }

        &:nth-child(2) {
           background-color: rgba(0, 0, 0, 0.75);
        }
      }
    }
  }
}


  .bgBlurContent {
    .orderSearchBg {
      background-color: rgba(0, 0, 0, 0.9);
    }
  }

  .bgImg {
    background-image: url('/public/bg-app.png');
  }
}

.dargPanelError {
  position: absolute;
  left: 1px;
  top: 1px;
  width: 85%;
  height: 45px;
}

.fetalErrorHeader{
  position: absolute;
  top:10px;
  right: 20px;
  button{
    transition: all 0.1s;
    display: inline-flex;
    &:first-child{
      margin-right: 12px;
    }
    &:hover{
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
    }
  }
}

.fatalErrorMain{
  height: 100%;
}

.errorPanel{
  height: 100%;
   
  .errorPage{
    margin: auto;
    width: 100%;
    height: 100%;
    padding: 30px 20px;
    text-align: center;
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    svg{
      width: 100px;
      height: 100px;
    }
    .fatalErrorTitle {
      font-family: Noto Sans Display;
      font-size: 20px;
      line-height: 1.4;
      text-align: center;
      color: #fff;
      margin-top: 8px;
      margin-bottom: 12px;
    }
    p{
      opacity: 0.7;
      font-family: Noto Sans Display;
      font-size: 16px;
      font-weight: 300;
      line-height: 1.4;
      text-align: center;
      color: #fff;
    }
    .restartAppBtn {
      width: 112px;
      height: 36px;
      display: flex;
      justify-content:center;
      align-items: center;
      padding: 8px 6px;
      border-radius: 4px;
      background-color: #70ff00;
      font-family: Noto Sans Display;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      letter-spacing: 0.7px;
      text-align: center;
      color: #000;
      margin: 0 auto;
      margin-top: 24px;
    }
  }
}

.backdropOverlay{
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top:0;
  bottom: 0;
  z-index: 9999;
}

.isbackdropOverlay{
  .bryzos{
    z-index:10000;
  }
}

.companySelectDropdown.companySelectDropdown {
  height: 100%;
  width: 100%;
  padding: 0px;

  .MuiInputBase-root{
    height: 100%;
    &:focus{
      border-radius: 0px 0px 4px 4px;
    }
  }

  .MuiOutlinedInput-root{
    padding: 6px 6px 6px 10px;
  }

  .MuiSelect-select {
    display: flex;
    justify-content: center;
  }


  svg {
    right: 24px;
    top: calc(50% - 0.6em);
    transform: unset;
    color: #fff;
  }

  fieldset {
    border: 0;
  }
}

.alert{
  background-color: #ffefed;
}
.warning{
  background-color: #f5bf4f;
}
.green{
  background-color: #61c654;
}

.snackbar_LOW {
  background-color: #ffd47b;
}

.snackbar_MEDIUM {
  background-color: #f5ad4f;
}

.snackbar_HIGH {
  background-color: #ff594a;
}

.turnOnNotif {
  margin-top: 24px;
  display: flex;
  justify-content: center;

  label {
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: 300;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    .checkmark{
      top:1px
    }
  }
}

// Mobile Style

    .selectDropdown{
      &.disabledDropdown{
      .MuiSelect-select{
        opacity: 0.5;
      }
    }
    }

.star-rating {
  display: inline-flex;
  justify-content: center;
  cursor: pointer;
   input {
    display: none;
   }

 label {
    position: relative;
    display: inline-block;
    margin-bottom: 0;
    width: 24px;
    height: 24px;
    background-image: url('./assets/images/StarOutlined.svg'); 
    background-size: cover;
    transition: background-image 0.1s ease-in-out;
}

 input:checked+label {
    background-image: url('./assets/images/Star.svg'); 
}

label::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  animation: preload 0.1s 1;
}

/* Preload the images */
@keyframes preload {
    0% {
        background-image: url('./assets/images/StarOutlined.svg');
    }
    100% {
        background-image: url('./assets/images/Star.svg');
    }
}

}

.searchPanelMob.searchPanelMob {
  width: 100%;

 
  .listBody {
    width: 100%;
    padding: 0px 8px;

    &.searchInstantPriceList{
      padding: 0px;
      .ulBody {
        margin-top: 8px;
        max-height: calc(100vh - 100px);
      }
    }
  
    .ulBody {
      height: 100%;
      padding: 0 4px 0 0;
      max-height: calc(100vh - 180px);
      grid-gap: 0px;
      overflow: auto;

      .liBody {
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: 300;
        line-height: 1.4;
        text-align: left;
        color: #fff;
        width: auto;
        padding: 5px 8px;

        p {
          opacity: 0.7;
        }

        .liHead {
          font-weight: normal;
          opacity: unset;
        }
      }
     
    }

    .priceRating{
      font-weight: normal;
      .doller{
        font-size: 20px;
      }
    }

    .selectProduct {
      padding: 5px 8px;

      .liBodyList {
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: 300;
        line-height: 1.4;
        text-align: left;
        color: #fff;

        .liHead {
          font-weight: normal;
          opacity: unset;
        }
       
        
      }
      
    }
  }

  .lineH {
    &::after {
      height: 0.5px;
      background-color: rgba(255, 255, 255, 0.35);
    }
  }

  .selectedPordListScroll {
    // overflow: unset;
    table{
      tr{
        td{
          padding: 4px;
        }
      }
     }
     .clickToShare.clickToShare {
      color: #14181b;
  }
  }

  .priceFeedback {
      display: flex;
      align-items: center;
      width: 100%;

      label {
        height: 33px;
        display: inline-flex;
        flex-direction: row;
        justify-content: flex-start;
        flex: 1;
        align-items: center;
        gap: 3px;
        padding: 8px 6px 8px 6px;
        border-radius: 500px;
        border: solid 1px rgba(255, 255, 255, 0.2);
        font-family: Roboto;
        font-size: 12px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: #fff;

        input {
          display: none;
        }

        &:last-child {
          margin-right: 0px;
        }
      }

      .CheckBtn {
        flex-basis: 35px;
      }

      .hightBtn,
      .CheckBtn,
      .lowBtn {

        svg {
          margin-right: 3px;
        }

        &.selectedBtn {
          border: solid 1px #70ff00;
          color: #70ff00;

          svg {
            g {
              opacity: unset;
            }

            path {
              fill: #70ff00;
            }
          }
        }
      }

      .checkIcon {
        display: flex;

        svg {
          width: 18px;

          g {
            opacity: unset;
          }

          path {
            fill: #fff;
          }
        }
      }

      label {
        font-family: Roboto;
        font-size: 11px;
        font-style: normal;
        line-height: 1.4;
        text-align: left;
        color: #fff;
        margin-right: 6px;
      }

    

    .CheckBtn {
      .img2 {
        display: none;
      }

      &:hover {
        .img1 {
          display: none;
        }

        .img2 {
          display: block;
        }
      }
    }

    .hightBtn,
    .lowBtn {
      &:hover {
        @media (min-width:421px) {
          svg {
            g {
              opacity: unset;
            }

            path {
              fill: #70ff00;
              opacity: unset;
            }
          }
        }

      }

    }

    .goodBtn {
      &:hover {
        svg {
          path {
            g {
              opacity: unset;
            }

            path {
              fill: #70ff00;
              opacity: unset;
            }
          }
        }
      }
    }
  }
  
}

.enterEmail {
  position: relative;
  .errorText {
    font-family: Noto Sans;
    font-size: 14px;
    line-height: 1.6;
    text-align: left;
    color: #f00;
    position: absolute;
  }

  .togglePassWrapper{
    position: relative;
    .showPassBtn{
      display: flex;
      position: absolute;
      top: 50%;
      right: 10px;
      transform: translateY(-50%)
    }
  }
}

.companySelectDropdown.companySelectDropdown {
  height: 100%;
  width: 100%;
  padding: 0px;
  margin-top: 4px;
  margin-bottom: 16px;

  .MuiInputBase-root{
    height: 100%;
    input{
      padding: 0px;
      font-size: 14px;
      color: #fff;
      &:disabled{
        -webkit-text-fill-color: #fff;
      }
    }
    &:focus{
      border-radius: 0px 0px 4px 4px;
    }
  }

  .MuiOutlinedInput-root{
    padding: 6px 6px 6px 10px;
  }

  .MuiSelect-select {
    display: flex;
    justify-content: center;
  }


  svg {
    right: 24px;
    top: calc(50% - 0.6em);
    transform: unset;
    color: #fff;
  }

  fieldset {
    border: 0;
  }
}

.errorMsgDropdown{
  .selectDropdown.selectDropdown {
  .MuiSelect-select {
    border: 0.5px solid #ff0000;
  }
}
}

.selectDropdown.selectDropdown {
  width: 100%;
  height: 40px;

  &.deliveryDateDropdown{
    flex-basis: 0;
    .MuiSelect-select {
      background-color: rgba(0, 0, 0, 0.2);
    }
  }

  &.priceDropdown.priceDropdown{
    .MuiSelect-select {
      background-color: transparent;
      &[aria-expanded="true"]{
        background-color: transparent;
      }
    }
  }

  &.uomDrodown{
    height: auto;
    min-width: 40px;
    flex-basis: 0;
    .MuiSelect-select {
      background-color: rgba(0, 0, 0, 0.3);
      height: 36px;
      padding-left: 6px;
      padding-right: 18px;
      border: 0px;
      border-radius: 0px 4px 4px 0px;
      border-left:0.1px solid rgb(255, 255, 255, 0.2);
      font-family: Noto Sans Display;
      font-size: 12px;
      font-weight: 300;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      justify-content: center;
      &[aria-expanded="true"]{
        border: 0;
        border-left:0.1px solid rgb(255, 255, 255, 0.2);
        background-color: rgba(0, 0, 0, 0.3);
      }
    }
    input{
      width: 40px;
    }
    fieldset {
      height: auto;
    }
  }
  
  label{
    width: 100%;
  }
  .MuiSelect-icon {
    right: 0px;
    transform: unset;
  }

  .MuiSelect-select {
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    padding: 0px 8px 0px 12px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    border: solid 0.5px transparent;
    display: flex;
    align-items: center;
    height: 38px;
    &[aria-expanded="true"]{
      border: solid 0.5px #fff;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 4px 4px 0px 0px;
    }
  }

  input{
    margin-bottom: 0px;
    margin-top: 0px;
    height: 100%;
  }

  svg {
    color: #fff;
    right: -2px;
    transform: unset;
  }

  fieldset {
    border: 0px;
    top:0px;
    height: 40px;
  }
}

.selectPaymentMethod.selectPaymentMethod {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0px;
  .MuiSelect-select.MuiSelect-select {
    font-family: Noto Sans Display;
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: center;
    color: rgba(0, 0, 0, 0.8);
    border-radius: 0px;
    border: solid 0.5px transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    height: 48px;
    padding: 0px;
    padding-right: 4px;
    i{
      font-size: 14px;
    }
  }

  input {
    margin-bottom: 0px;
    margin-top: 0px;
    height: 100%;
  }

  svg {
    right: 0;
    top:unset;
    position: unset;
    path{
      fill: #000;
    }
  }

  fieldset {
    border: 0px;
    top: 0px;
    height: 100%;
  }
}

ion-modal.popupMain {
  --width:100%;
  --height: 247px;
  --border-radius: 16px 16px 0px 0px;
  --box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ion-background-color: #6d6d6d;
    align-items: end;
}

ion-modal.savePopup{
  --width:295px;
  --height: auto;
  --border-radius: 16px;
  --box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.5);
  --ion-background-color: rgba(255, 255, 255, 0.8);
    align-items: flex-start;
    padding-top: 200px;
}

ion-modal.shareAppPopup{
  --width:100%;
  --height: 393px;
  --border-radius: 16px 16px 0px 0px;
  --box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ion-background-color: #6d6d6d;
    align-items: end;
}

ion-modal.popupSubmitApp{
  --width:100%;
  --height: 420px;
  --border-radius: 16px 16px 0px 0px;
  --box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ion-background-color: #6d6d6d;
    align-items: end;
}

ion-modal.reminderPopup{
  --width:100%;
  --height: calc(100% - 40px);
  --border-radius: 16px 16px 0px 0px;
  --box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ion-background-color: #6d6d6d;
    align-items: end;
}

ion-modal.reminderPopupSuccess{
  --width:100%;
  --height: calc(100% - 60%);
  --border-radius: 16px 16px 0px 0px;
  --box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ion-background-color: #6d6d6d;
    align-items: end;
}

ion-modal.orderAcceptPopup{
  --width:100%;
  --height: calc(100% - 48px);
  --border-radius: 16px 16px 0px 0px;
  --box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ion-background-color: #6d6d6d;
    align-items: end;
}

ion-modal.placeOrderPopup{
  --width:100%;
  --height: 100% ;
  --border-radius:0px 0px;
  --box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ion-background-color: transparent;
    align-items: end;
}

.placeOrderPopup{
  --backdrop-opacity: 1;
 
  &::part(backdrop) {
      background: transparent;
      -webkit-backdrop-filter: blur(40px);
      backdrop-filter: blur(40px);
      background-color: rgba(13, 13, 13, 0.3);
  }
}

.popupMain,.savePopup,.shareAppPopup,.popupSubmitApp,.reminderPopup{
  --backdrop-opacity: 1;
 
  &::part(backdrop) {
      background: transparent;
      -webkit-backdrop-filter: blur(5px);
      backdrop-filter: blur(5px);
      background-color: rgba(255, 255, 255, 0.02);
  }
}

.errorText2{
  font-family: Noto Sans;
  font-size: 11px;
  line-height: 1.6;
  text-align: left;
  color: #f00;
  gap: 4px;
  display: flex;
  align-items: center;
}

.positionAbsolute{
  position: absolute;
}

.errorTextForget{
  width: 100%;
  justify-content: flex-start;
  margin-top: 3px;
}

.selectDropdown2.selectDropdown2 {
  width: 100%;
  height: 40px;
  padding: 10px 0px;

  .dropdownPlaceholderText{
    font-weight: 300;
    opacity: 0.5;
  }

  &.dropdownFocused {
    box-shadow: 0 0 25px 0 rgba(0, 0, 0, 0.5);
    background-color: rgba(255, 255, 255, 0.75);
    border-radius: 4px 4px 0px 0px;

    .MuiSelect-select {
      color: rgba(0, 0, 0, 0.75);
      background-color: rgba(255, 255, 255, 0.75);
    }

    .MuiSelect-icon {
      color: #474747;
    }

    fieldset {
      border-radius: 4px 4px 0px 0px;
      border: 0;
    }

  }

  &:hover {
    fieldset {
      border: solid 0.5px #fff;
    }
  }

  .MuiSelect-icon {
    right: 0px;
    transform: unset;
    font-size: 34px;
  }

  .MuiSelect-select {
    padding: 0 20px 0 12px;
    line-height: 40px;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: 600;
    text-align: left;
    color: #fff;
  }

  svg {
    color: #fff;
    right: -2px;
    transform: unset;
  }

  fieldset.MuiOutlinedInput-notchedOutline {
    border-radius: 4px;
    border: solid 0.5px #fff;
  }
}

.tncMobileOnboarding{
  color: #fff;
  .TnCTitle {
    font-family: Noto Sans Display;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    margin-bottom: 4px;
  }
  .TnCTitle2 {
    font-size: 14px;
  }

  .TnCPara {
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
  }
  .TnCList {
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    padding-left: 29px;
    margin-bottom: 4px;

    li {
      margin-bottom: 8px;
      list-style-type: disc;
    }
  }

  .TnCList1 {
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    padding-left: 29px;
    margin-bottom: 4px;

    li {
      margin-bottom: 8px;
      list-style-type: decimal;
    }
  }
}

ion-content {
  --ion-background-color: 'transparent'
}

ion-modal.missPopup{
  --width:100%;
  --height: 393px;
  --border-radius: 16px 16px 0px 0px;
  --box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ion-background-color: #6d6d6d;
    align-items: end;
}

.orderList{
  padding-bottom: 0px;
}

.acceptBgOrderList{
  z-index: 9;

  .ion-color-success{
    background:#70ff00;
    color: #000;
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: normal;
    text-transform: capitalize;
    display: none;
  }
}

ion-item.step3TableRowItem {
  --border-color:transparent;
  --inner-padding-end:0;
  --padding-start:0
}

  .removeRowBtn {
    z-index: 9;

    .ion-color-danger {
      width: 74px;
      height: 100%;
      flex-grow: 0;
      background-color: #aa5151;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .swipeCardContent{
    padding: 0px;
  }

  ion-list.orderList{
    ion-item {
      --inner-padding-end:0
    }
  }

 
  ion-modal.noInternet{
    --width:100%;
    --height: 100%;
    --border-radius: 0px;
    --box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --ion-background-color: #6d6d6d;
      align-items: end;
      .ion-page{
        justify-content: center;
        display: flex;
        align-items: center;
        p{
          font-size: 16px;
          color: #fff;
        }
      }
  }
#routeToPayment{
  text-decoration: underline;
}
  
.tncBgMob{
 padding: 16px 16px;
 height: calc(100% - 40px);
 .termsAndConditionsMain{
  height: 100% ;
 }
.TncContainer {
  width: 100%;
  height: 100%;
  padding: 16px;
  border-radius: 4px;
  background-color: #fff;

  .termsAndConditions {
    position: relative;
    overflow: auto;
    max-height: 100%;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background:#9da2b2;
      border-radius: 50px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    .tncIcon {
      text-align: right;
      position: absolute;
      right: 8px;
      top: 10px;

      &:hover {
        .img1 {
          display: none;
        }

        .img2 {
          display: block;
        }
      }

      .img2 {
        display: none;
      }
    }

    .termsAndConditionsHead {
      font-family: Noto Sans Display;
      font-size: 20px;
      font-weight: bold;
      line-height: normal;
      text-align: left;
      color: #525f7f;

      span {
        font-family: Noto Sans Display;
        font-size: 20px;
        font-weight: bold;
        line-height: normal;
        text-align: left;
        color: #397aff;
        text-decoration: none;
        padding-left: 5px;
      }
    }

    .tncHead {
      font-family: Noto Sans Display;
      font-size: 20px;
      font-weight: bold;
      line-height: normal;
      text-align: left;
      color: #424656;
      margin-bottom: 8px;
    }

    .effectiveDate {
      font-family: Noto Sans Display;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: normal;
      letter-spacing: normal;
      text-align: left;
      color: #424656;
      margin-bottom: 16px;
    }

    .TnCTitle {
      font-family: Noto Sans Display;
      font-size: 16px;
      font-weight: 600;
      line-height: normal;
      text-align: left;
      color: #424656;
      margin-bottom: 4px;
    }

    .TnCTitle1 {
      font-size: 16px;
    }

    .TnCTitle2 {
      font-size: 14px;
    }

    .TnCPara {
      font-family: Noto Sans Display;
      font-size: 14px;
      font-weight: normal;
      line-height: normal;
      text-align: left;
      color: #424656;
      margin-bottom: 12px;
    }

    .TnCList {
      font-family: Noto Sans Display;
      font-size: 14px;
      font-weight: normal;
      line-height: normal;
      text-align: left;
      color: #424656;
      padding-left: 16px;
      margin-bottom: 4px;

      li {
        margin-bottom: 8px;
        list-style-type: disc;
      }
    }

    .TnCList1 {
      font-family: Noto Sans Display;
      font-size: 14px;
      font-weight: normal;
      line-height: normal;
      text-align: left;
      color: #424656;
      padding-left: 16px;
      margin-bottom: 12px;

      li {
        margin-bottom: 6px;
        list-style-type: decimal;
      }
    }

    .termsAndConditionsContent {
      font-family: Noto Sans;
      font-size: 16px;
      line-height: normal;
      text-align: left;
      color: #525f7f;
    }

    .tncContent {
      font-family: Noto Sans;
      font-size: 16px;
      line-height: normal;
      text-align: left;
      color: #525f7f;
    }


  }
}

.tncButtons {
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 16px;

  .disagreeBtn {
    font-family: Noto Sans Display;
    cursor: pointer;
    transition: all 0.1s;
    border: none;
    font-size: 14px;
    line-height: normal;
    color: #fff;
    background: transparent;
    margin-right: auto;
  }

  .agreeBtn {
    transition: all 0.1s;
    margin-left: auto;
    opacity: 0.5;
    font-family: Noto Sans Display;
    font-size: 14px;
    line-height: normal;
    text-align: center;
    color: #fff;
    background: transparent;
    cursor: not-allowed;
    border: 0px;

    &:focus {
      outline: none;
    }
  }

  .agreeBtn1{
    font-size: 14px;
  }

  .agreeBtnEnable {
    cursor: pointer;
    color: #70ff00;
    opacity: unset;
  }
}
}


#routeToPayment{
  text-decoration: underline;
}
// new tnc css 
.tncmaintitle{
  font-family:Syncopate;
  font-weight: 600;
  margin-bottom: 20px;
  font-size: 16px;
  margin-top: 40px;
}
.tncwelcometitle{
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  margin-bottom: 20px;
  font-size: 15px;
  margin-top: 40px;
}
.tncnormaltext{
  font-family: 'Inter', sans-serif;
  font-size: 15px;
}
.tncliheading{
  margin-bottom: 20px;
}
.tncliheading::marker{
  font-weight: 600;
}
.tncliheading span{
  font-weight: 600;
}
.tncmargintop{
  margin-top: 10px;
}
.tncstyledecimal{
  list-style-type: decimal;
  margin-left: 40px;
}
.tncstylealpha{
  list-style-type: lower-alpha;
  margin-left: 30px;
}

.react-emoji{
  .react-input-emoji--input{
    padding: 9px 39px 11px 12px;
  }
  .react-input-emoji--container{
    margin-right: 12px;
  }
  .react-input-emoji--button{
    position: absolute;
    right: 14px;
    background: transparent;
    z-index: 999;
  }
}

.react-emoji-picker--container{
  .react-emoji-picker--wrapper{
    bottom: 15px;
    left: -8px;
    right: auto;
  } 
}

ion-modal.shareVideoMain.shareVideoMain{
  --width:100%;
  --height:auto;
  --border-radius: 16px 16px 0px 0px;
  --box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --ion-background-color: #6d6d6d;
    align-items: end;
    .sharePopupContent{
      height: 100%;
      padding:40px 20px;
      @media (max-width:400px) {
        padding:40px 12px;
      }
      .closeShareIcon{
        position: absolute;
        top:0px;
        right: 6px;
        cursor: pointer;
      }
      .titlePopup{
        font-family: Syncopate;
        font-size: 16px;
        font-weight: bold;
        line-height: 1.16;
        text-align: center;
        color: #fff;
        text-transform: uppercase;
      }
      .iconSectionBox{
        margin-top: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        grid-gap: 6px;
        flex-wrap: wrap;
        img{
          max-width: unset;
          @media (max-width:400px) {
            width: 32px;
          }
        }
        button{
          display: flex;
          flex-direction: column;
          align-items: center;
          
          .btnTxt{
            font-family: Inter;
            font-size: 10px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: center;
            color: #fff;
            margin-top: 12px;
            @media (max-width:400px) {
              margin-top: 8px;
            }
          }
        }
      }
      .activeShareBtn{
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.2);
        padding: 6px 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        @media (max-width:400px) {
          padding: 6px;
        }
      }
      .shareBtn{
        padding: 6px 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        @media (max-width:400px) {
          padding: 6px;
        }
      }
      .SendInputSection{
        margin-top: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap:8px;
        input{
          width: 100%;
          height: 40px;
          flex-grow: 0;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          gap: 8px;
          padding: 0 12px;
          border-radius: 4px;
          border: solid 0.5px rgba(255, 255, 255, 0.2);
          background-color: #000;
          font-family: Noto Sans;
          font-size: 14px;
          font-weight: 600;
          font-stretch: normal;
          font-style: normal;
          line-height: 1;
          letter-spacing: normal;
          text-align: left;
          color: #fff;
          &::placeholder{
            opacity: 0.7;
            font-weight: 200;
          }
          &:focus{
            outline: none;            
          }
        }
        button{
          width: 57px;
          height: 40px;
          flex-grow: 0;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          gap: 8px;
          padding: 13px 12px;
          border-radius: 4px;
          font-size: 14px;
          font-family: Noto Sans Display;
          font-weight: 600;
          transition: all 0.1s;
          background-color: #42ff00;
          color: #000;
          &[disabled]{
            cursor: not-allowed;
            background-color: rgba(255, 255, 255, 0.2);
            opacity: 0.5;
            color: #fff;
            font-weight: normal;

          }
          
        }
      }
      .emailError {
        font-family: Noto Sans;
        font-size: 14px;
        line-height: 1.6;
        text-align: left;
        color: #f00;
        position: absolute;
        margin-left: 8px;
      }
      .successMessageStyle {
        font-family: Noto Sans;
        font-size: 14px;
        line-height: 1.6;
        color: #70ff00;
        margin: 10px auto 0;
        width: calc(100% - 68px);
        text-align: center;
      }
    }
  
}

/* SubTitle style */
.subTitleStyle{
  video::-webkit-media-text-track-container {
    height: calc(100% - 52px) !important;
    font-family: Inter;
    bottom: 45px !important;
  }
  video::cue{
    line-height: 1.5;  
  }
}

.subTitleStyleIos{
  video::-webkit-media-text-track-container {
    height: calc(100% - 52px) !important;
    font-family: Inter;
    bottom: 50px !important;
    line-height: normal;
  }
}

.noSubTitleStyleIos{
  video::-webkit-media-text-track-container {
    font-family: Inter;
  }
}
