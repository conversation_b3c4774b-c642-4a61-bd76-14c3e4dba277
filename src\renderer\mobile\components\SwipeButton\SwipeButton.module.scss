.swipeButton {
      width: 158px;
      height: 53px;
      display: flex;
      align-items: center;
      background-image: url(../../../assets/mobile-images/Swipe_Button.svg);
      background-repeat: no-repeat;
      background-position: center;
      position: relative;
}

.disabled {
      opacity: 0.5;
}

.track {
      width: 140px;
}

ion-card {
      width: 18px;
      height: 18px;
      background-color: #70ff00;
      box-shadow: none;
      border-radius: 50%;
      margin: 0px;
}

.swipeBalltoRight {
      font-family: Noto Sans Display;
      font-size: 10px;
      font-weight: normal;
      line-height: 1;
      text-align: center;
      color: rgba(255, 255, 255, 0.8);
      position: absolute;
      bottom: 3px;
      left: 35px;
}

.toPlaceOrder {
      font-family: Noto Sans Display;
      font-size: 12px;
      font-weight: normal;
      line-height: 1;
      text-align: center;
      color: rgba(0, 0, 0, 0.8);
      position: absolute;
      top: 0;
      left: 35px;
}

.acceptSwipeButton {
      width: 100%;
      height: 53px;
      display: flex;
      align-items: center;
      background-repeat: no-repeat;
      background-position: center;
      position: relative;
      justify-content: center;
      &.disabled{
            .track{
                  background-color: rgba(255, 255, 255, 0.2);
            }
            ion-card {
                  background-color: rgba(0, 0, 0, 0.3);
            }
      }
      ion-card {
            width: 32px;
            height: 32px;
            border-radius: 500px;
            background-color: #000;
            background-image: url(../../../assets/mobile-images/right-arrow-swipe.svg);
            background-repeat: no-repeat;
            background-position: 9px 8px;
            background-size: 16px;
            z-index: 1;
      }

      .trackToHide {
            background: #70ff00;
            position: absolute;
            left: 0;
            height: 32px;
            z-index: 1;
      }

      .track {
            width: 300px;
            height: 48px;
            border-radius: 500px;
            background-color: #70ff00;
            padding-top: 8px;
            padding-bottom: 8px;
            padding-left: 8px;
            position: relative;
            display: flex;
            overflow: hidden;

            .slideToAccept {
                  position: absolute;
                  top: 12px;
                  width: 100%;
                  text-align: center;
            }
      }
}

.claimSwipeButton {
      width: 100%;
      height: 74px;
      display: flex;
      align-items: center;
      background-repeat: no-repeat;
      background-position: center;
      position: relative;
      justify-content: center;
      overflow: hidden;

      ion-card {
            z-index: 2;
            width: 68px;
            height: 68px;
            border-radius: 37px;
            background-image: url(../../../assets/mobile-images/B.png);
            background-repeat: no-repeat;
            background-color: transparent;
            background-position: center;
            background-size: cover;
            &:before {
                  content: "";
                  position: absolute;
                  background: radial-gradient(circle at 50% 120%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0) 70%);
                  border-radius: 50%;
                  bottom: 2.5%;
                  left: 5%;
                  opacity: 0.6;
                  height: 100%;
                  width: 90%;
                  filter: blur(5px);
                  z-index: 2;
            }
            &:after {
                  content: "";
                  width: 100%;
                  height: 100%;
                  position: absolute;
                  top: 5%;
                  left: 10%;
                  border-radius: 50%;
                  background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8) 14%, rgba(255, 255, 255, 0) 24%);
                  transform: translateX(-80px) translateY(-90px) skewX(-20deg);
                  filter: blur(10px);
            }
      }

      .track {
            width: 100%;
            height: 74px;
            padding-top: 3px;
            padding-bottom: 3px;
            padding-left: 3px;
            position: relative;
            display: flex;
            overflow: hidden;
            border-radius: 37px;
            box-shadow: inset 0 25px 40px 2px #fff, inset 0 -25px 40px 2px #fff;
            background-color: #787878;

            .slideToAccept {
                  position: absolute;
                  top: 0;
                  width: 100%;
                  font-family: Noto Sans Display;
                  font-size: 12px;
                  font-weight: normal;
                  line-height: 1;
                  text-align: center;
                  color: #000;
                  background-image: url(/src/renderer/assets/mobile-images/SwipeBtnArrow.svg);
                  background-repeat: no-repeat;
                  background-position: center;
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  margin-left: 20px;
                  &::after{
                        content: 'SLIDE BALL TO RIGHT TO PURCHASE';
                        position: absolute;
                        margin-left: -30px;
                  }
            }
      }

      .trackToHide {
            box-shadow: inset 0 25px 40px 2px #fff, inset 0 -25px 40px 2px #fff;
            background-color: #787878;
            position: absolute;
            left: 0;
            top:0;
            height: 74px;
            z-index: 1;
            border: none;
       }
    
}