.snackbarContainer {
    width: 96%;
    position: absolute;
    right: 0px;
    left: 8px;
    // top: 50px;
    z-index: 11000;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 6px;
    padding: 12px;
    border-radius: 6px;
    background-color: #ffefed;
    transition: opacity 0.3s ease-in-out;

    &.errorBorder { 
        border: solid 1px #ff5d47;
    }

    &.hide {
        opacity: 0;
    }

    &.show {
        opacity: 1;
    }


    .actionBtn {
        background: #fff;
        color: #000;
        line-height: normal;
        height: 24px;
        width: 76px;
        border-radius: 3px;
        font-size: 12px;
        margin-left: 16px;
    }

    .errorTest {
        width: 100%;

        .content {
            flex: 1;
            font-size: 14px;
            color: #fff;
        }

        .errorContent {
            align-self: stretch;
            font-family: Noto Sans;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: normal;
            text-align: left;
            color: #ff5d47;
        }

        .errorSms {
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: 600;
            line-height: 1.6;
            letter-spacing: normal;
            text-align: left;
            color: #ff5d47;
            margin-bottom: 4px;
        }
    }

    .closeBtn {
        display: flex;
        border-radius: 50%;
        position: relative;
        left: 5px;
        transition: all 0.1s;

        svg {
            width: 20px;
            height: 20px;
            position: absolute;
            top: -9px;
            right: -3px;
            z-index: 9;
        }


    }
}