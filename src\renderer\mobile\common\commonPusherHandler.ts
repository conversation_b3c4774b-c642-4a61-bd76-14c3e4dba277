// @ts-3nocheck
import { notificationHandler } from './notification';

import config from '../../../main/config';
import { Channel } from 'pusher-js';

const { commonAppEventsofPusher } = config
 
const { privateEvents } = commonAppEventsofPusher;

 
export const userForceLogout = (channel: Channel) => {
    channel.bind( privateEvents.userForceLogout, ( ) => {
        console.log(`Recieved Force logout event`);
        // mainWindow?.webContents.send(channelWindow.forceLogout);
    });
}

export const referenceProductChangeHandler = (channel: Channel, channelEvent: string[]) => {
    channelEvent.forEach($event => {
        channel.bind( $event, (data: any) => {
        // console.log('Received notification:', data);
            notificationHandler(data.notification);
            // mainWindow?.webContents.send(channelWindow.productReferenceChanged);
        });
    });
} 