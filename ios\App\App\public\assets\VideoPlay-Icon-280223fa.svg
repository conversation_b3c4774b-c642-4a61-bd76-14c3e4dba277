<svg width="42" height="42" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#v8lftbxrpa)">
        <circle cx="21" cy="21" r="20" fill="#000" fill-opacity=".25"/>
        <circle cx="21" cy="21" r="20.217" stroke="url(#2z2bfwqgmb)" stroke-opacity=".2" stroke-width=".433"/>
    </g>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M28.58 22.124a1.3 1.3 0 0 0 0-2.252l-11.263-6.497a1.3 1.3 0 0 0-1.95 1.126v12.994a1.3 1.3 0 0 0 1.95 1.126l11.263-6.497z" fill="#fff"/>
    <defs>
        <linearGradient id="2z2bfwqgmb" x1="36.323" y1="3.742" x2="21" y2="41" gradientUnits="userSpaceOnUse">
            <stop stop-color="#fff"/>
            <stop offset="1" stop-color="#fff" stop-opacity="0"/>
        </linearGradient>
        <filter id="v8lftbxrpa" x="-8.099" y="-8.099" width="58.198" height="58.198" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="4.333"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2664_16387"/>
            <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_2664_16387" result="shape"/>
            <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feMorphology radius=".633" operator="dilate" in="SourceAlpha" result="effect2_innerShadow_2664_16387"/>
            <feOffset dx="-.316" dy=".316"/>
            <feGaussianBlur stdDeviation=".158"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
            <feBlend in2="shape" result="effect2_innerShadow_2664_16387"/>
        </filter>
    </defs>
</svg>
