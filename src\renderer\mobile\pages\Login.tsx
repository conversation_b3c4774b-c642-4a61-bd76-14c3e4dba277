// @ts-nocheck
import styles from './Login.module.scss'
import { ReactComponent as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../assets/mobile-images/B_Logo.svg';
import { ReactComponent as CrossLogo } from '../../assets/mobile-images/crossBtn.svg';
import { ReactComponent as BryzosIcon } from '../../assets/mobile-images/Bryzos.svg';
import { ReactComponent as ShowPassIcon } from '../../assets/mobile-images/show-pass.svg';
import { ReactComponent as HidePassIcon } from '../../assets/mobile-images/hide-pass.svg';
import {  IonContent, IonPage, useIonRouter, useIonViewWillLeave, } from '@ionic/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { appEnvironment,  mobileRoutes } from '../library/common';
import { useEventListener } from '@mantine/hooks';
import { useForm } from 'react-hook-form';
import * as yup from "yup";
import { useEffect, useRef, useState } from 'react';
import { useAuthStore, useGlobalStore } from '@bryzos/giss-ui-library';
import clsx from 'clsx';
import { useSplashScreenStore } from '../library/stores/splashScreenStore';

const Login = () => {
  const router = useIonRouter();
  const { showLoader , setIsManualLogin} = useGlobalStore();
  const [enableLoginBtn, setEnableLoginBtn] = useState(false);
  const env = import.meta.env.VITE_ENVIRONMENT_UI;
  const envTitle = env.charAt(0).toUpperCase() + env.slice(1);
  const envClass = env + '_env';
  const { register, getValues, watch, handleSubmit, setError,  clearErrors, reset, formState: { errors } } = useForm({
    resolver: yupResolver(
      yup.object({
        email: yup.string().matches(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/, {
          message: 'Please enter valid email'
        }).required('Email is Required'),
        password: yup.string().test('len', 'Password must be 6 digits', val => val?.length >= 6),
      }).required()

    ),
    mode: 'onSubmit',
    defaultValues: {
      'email': '',
      'password': ''
    }
  });

  //const hasLoggedInEver = localStorage.getItem("hasLoggedInEver") === '1' || !!hasCookiesAvailable;

  const { ref, ...rest } = register("email");
  const emailRef = useRef<HTMLIonInputElement>(null);
  const signupUser = useGlobalStore(state => state.signupUser);
  const {loginError,setLoginError , submitLogin } = useAuthStore()
  const [passwordVisibility, setPasswordVisibility] = useState(true);
  const {showSplashScreen} = useSplashScreenStore();

  // useEffect(() => {
  //   if (hasLoggedInEver || splashVideoFinished) {
  //     const t = setTimeout(() => {
  //       clearTimeout(t);
  //       emailRef.current?.focus();
  //     }, 300);
  //   }
  // }, [hasLoggedInEver, splashVideoFinished]);

  useEffect(()=>{
    if(loginError){
        setError("root.serverError",{message: loginError?.message})
    }
  },[loginError])
  
  useEffect(()=>{
    if(signupUser){
        submitLogin(signupUser);
    }
  },[signupUser])

  useIonViewWillLeave(()=>{
    reset();    

  },[]);


  useEffect(() => {
    if (errors.root?.serverError) clearErrors('root.serverError');
    if(watch('email') && watch('password').length >= 6) 
      setEnableLoginBtn(true)
    else
      setEnableLoginBtn(false)
  }, [watch('email'), watch('password')])


  const keydownRef = useEventListener('keydown', function (event) {
    if (event.key === 'Enter') {
      handleSubmit(submitLogin)()
    }
  }
  );

  // const handleVideoEnd = () => {
  //   setSplashVideoFinished(true);
  // };
  const forgotPasswordHandler = () => {
    router.push(mobileRoutes.forgotPassword,{animate:true,direction:'forward'});
  }

  const handleOnClickLogin = () => {
    setIsManualLogin(true)
    handleSubmit(submitLogin)()
  }

  const togglePasswordVisibility = () => {
    setPasswordVisibility(!passwordVisibility);
  };

  if(showLoader)return <IonPage><IonContent><></></IonContent></IonPage>
  return (
    <IonPage>
      <IonContent> 
          <div className={styles.appMain}>
          {
          // (!hasLoggedInEver && !splashVideoFinished) ? <VideoSplashScreen onVideoEnd={handleVideoEnd} /> : 
          (!showSplashScreen && 
            <div className={clsx(styles.loginContainer, styles[envClass])}>
              
              <div className={styles.bryzosLogo}>
                <BryzosLogo />
              </div>
              <h1 className={styles.welcomBryzosText}>Welcome to Bryzos</h1>
              <div className={styles.badgeContainer}> {env !== appEnvironment.prod && <span className={clsx(styles.badge)}>{envTitle}  &nbsp; v{import.meta.env.VITE_REACT_APP_VERSION}</span> }</div>
              <table ref={keydownRef}>
                <tbody>
                  <tr className={clsx(styles.inputBody, 'inputBody')}>
                    <td className='enterEmail'>
                      <input type="email" {...rest}
                        ref={(e) => { ref(e); emailRef.current = e; }}
                        onChange={(e) => {
                          setLoginError();
                          register("email").onChange(e);
                        }}
                        placeholder='Enter Email Address' />
                      <p className='errorText2 positionAbsolute'>{(errors.email ) ? <CrossLogo /> : ""}{ errors.email?.message}</p>
                      
                    </td>
                    <td className='enterEmail'>
                      <div>
                      <span className='togglePassWrapper'>
                        <input type={passwordVisibility ? 'password' : 'text'} {...register("password")}  placeholder='Enter Password'  
                        onChange={(e) => {
                          setLoginError();
                          register("email").onChange(e);
                        }}/> 
                        <button className='showPassBtn' onClick={togglePasswordVisibility}>
                            {passwordVisibility ? <ShowPassIcon /> : <HidePassIcon />}
                        </button>
                        </span>
                        <span className='errorText2'>{errors.root?.serverError ? <CrossLogo /> : ""}{errors.root?.serverError?.message}</span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
                <button className={styles.loginBtn} onClick={handleOnClickLogin} disabled={!(enableLoginBtn && !errors.root?.serverError?.message)} >Login</button>
                <button className={styles.forgotBtn} onClick={forgotPasswordHandler}>Forgot Password?</button>
               <div className={styles.loginBtnSection}>
                <div className={styles.testContainer}>
                  <h1 className={styles.bryzosText}><BryzosIcon/></h1>
                  {/* <span className={styles.forgotCredentialsText}>Forgot Credentials</span> */}
                  <span className={styles.forgotCredentialsText} onClick={()=>{router.push("/onboarding-detail",{animate:true,direction:'forward'})}}>Join Bryzos</span>
                </div>
               </div>
             
            </div>)}
          </div> 
      </IonContent>
    </IonPage>

  )
}

export default Login