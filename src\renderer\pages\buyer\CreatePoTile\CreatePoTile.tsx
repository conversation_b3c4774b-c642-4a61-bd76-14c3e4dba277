// @ts-nocheck
import { useContext, useEffect, useState } from "react";
import styles from "../createPo.module.scss";

import { Controller } from "react-hook-form";
import { Autocomplete, ClickAwayListener, Fade, Tooltip } from "@mui/material";
import { CustomMenu } from "../CustomMenu";
import { formatToTwoDecimalPlaces, get2DigitFormater, get4DigitFormater, } from "../../../helper";
import clsx from "clsx";
import {
  EmptyString,
  MinSearchDataLen,
} from "../../../../common";
import { useImmer } from "use-immer";
// import { ReactComponent as QuestionIcon } from "../../../assets/images/setting-question.svg";
import { UserContext } from "../../../UserContext";
import { ReactComponent as RemoveLineIcon } from "../../../assets/images/Remove-line.svg";
import { ReactComponent as RemoveLineHoverIcon } from "../../../assets/images/Remove-line-hover.svg";
import { ReactComponent as QuestionIcon } from '../../../assets/images/setting-question.svg';
import { ReactComponent as QuestionHoverIcon } from '../../../assets/images/question-white-hover.svg';
import { CommonTooltip } from "../../../component/Tooltip/tooltip";
import useSaveUserActivity from "../../../hooks/useSaveUserActivity";
import { v4 as uuidv4 } from 'uuid';

const SAME_TAG_ERROR_MESSAGE =
  "Same part? <br />You have applied this part number to another product already.";

const CreatePoTile = ({
  index,
  register,
  fields,
  updateValue,
  products,
  control,
  setValue,
  watch,
  errors,
  qtyUnitM,
  priceUnitM,
  getValues,
  remove,
  userPartData,
  sessionId,
  selectedProduct,
  searchStringData,
  setSearchString,
  setLineSessionId,
  lineSessionId,
  handleCreatePOSearch,
  setHandleSubmitValidation,
  handleStateZipValidation,
  apiCallInProgress,
}) => {
  const user = useContext(UserContext)?.user;

  const [descriptionValue, setDescriptionValue] = useImmer(null);
  const [descriptionInput, setDescriptionInput] = useState("");
  const [sameTagErrorMsg, setSameTagErrorMsg] = useState(null);
  const [openDescription, setOpenDescription] = useState(false);
  const [isQtyInEditMode, setIsQtyInEditMode] = useState(true);
  const [showDomesticMaterialOnly, seShowDomesticMaterialOnly] = useState(false);
  const [enableRejectSearchAnalytic, setEnableRejectSearchAnalytic]= useState(false);

  const { mutate: logUserActivity, } = useSaveUserActivity();

  useEffect(() => {
    setValue(`descriptionLines.${index}.sessionId`, uuidv4());
    if (index > 0) {
      saveUserActivity();
    }
  }, []);

  useEffect(() => {
    const description = getValues(`descriptionLines.${index}.descriptionObj`);

    if (user.referenceData) {
      setValue(
        `descriptionLines.${index}.domesticMaterialOnly`,
        false
      );
    }
    if (description) {
      if (!description.domestic_material_only) {
        seShowDomesticMaterialOnly(false);
        setValue(`descriptionLines.${index}.domesticMaterialOnly`, null);
      } else {
        seShowDomesticMaterialOnly(true);
      }
    } else {
      seShowDomesticMaterialOnly(true);
    }
  }, [user?.referenceData, watch(`descriptionLines.${index}.descriptionObj`)]);

  useEffect(() => {
    const partNumber = getValues(`descriptionLines.${index}.partNumber`);
    const description = getValues(`descriptionLines.${index}.descriptionObj`);

    if (description) {
      if (!description.domestic_material_only) {
        seShowDomesticMaterialOnly(false);
        setValue(`descriptionLines.${index}.domesticMaterialOnly`, null);
      } else {
        seShowDomesticMaterialOnly(true);
      }
    } else {
      seShowDomesticMaterialOnly(true);
    }

    if (description) {
      if (userPartData?.length) {
        let isMappingExist = false;

        userPartData.forEach((partData) => {
          if (partData.tag === partNumber) {
            isMappingExist = true;
            const i = partData.product_id.findIndex((id) => id === description.Product_ID);
            setSameTagErrorMsg(i > -1 ? null : SAME_TAG_ERROR_MESSAGE);
          }
        });
        if (!isMappingExist) {
          setSameTagErrorMsg(null);
        }
      }
    } else {
      setSameTagErrorMsg(null);
      setValue(`descriptionLines.${index}.partNumber`, "");
    }
  }, [
    watch(`descriptionLines.${index}.partNumber`),
    watch(`descriptionLines.${index}.descriptionObj`),
  ]);

  useEffect(() => {
    if (!getValues(`descriptionLines.${index}.descriptionObj`) || errors?.descriptionLines?.[index]?.qtyVal) {
      setIsQtyInEditMode(true);
    }
  }, [
    getValues(`descriptionLines.${index}.descriptionObj`),
    errors?.descriptionLines?.[index]?.qtyVal,
  ]);


useEffect(() => {
  if(selectedProduct && selectedProduct.UI_Description === descriptionInput && lineSessionId === getValues(`descriptionLines.${index}.sessionId`)){
    setEnableRejectSearchAnalytic(false)
    handleCreatePOSearch(searchStringData, 'Accept', getValues(`descriptionLines.${index}.sessionId`))
  }
},[selectedProduct])

useEffect(() => {
  if((!selectedProduct && descriptionInput.length >= MinSearchDataLen) || (descriptionInput.length >= MinSearchDataLen)){
    setSearchString(descriptionInput)
    setLineSessionId(getValues(`descriptionLines.${index}.sessionId`))
  }
  if(descriptionInput.length === 1 ){
    setEnableRejectSearchAnalytic(true)
  }
  if(descriptionInput.length === 0 && searchStringData.length !== 0 && searchStringData.length >= MinSearchDataLen && enableRejectSearchAnalytic){
    handleCreatePOSearch(searchStringData, 'Reject', getValues(`descriptionLines.${index}.sessionId`))
    setEnableRejectSearchAnalytic(false)
  }
},[descriptionInput])



  const getValidSearchData = (searchString) => {
    if (!searchString) return;
    const regex = /[^\w'./"-]+/g;
    const words = searchString.toLowerCase().split(regex).filter(Boolean);

    const phrasesToConcatenate = [
      "type b",
      "sch 40",
      "sch 80",
      "1 1/4",
      "1 1/2",
      "1 1/8",
      "1 3/8",
      "1 5/8",
      "1 7/8",
      "1 3/4",
      "2 1/2",
      "2 1/4",
      "2 1/8",
      "2 3/4",
      "2 7/8",
    ];

    for (let i = 0; i < words.length - 1; i++) {
      const currentAndNextWordConcat = words[i] + " " + words[i + 1];
      if (phrasesToConcatenate.includes(currentAndNextWordConcat)) {
        words[i] = currentAndNextWordConcat;
        words.splice(i + 1, 1);
      } else if (words[i].includes("x") && i > 0 && i < words.length - 1) {
        words.splice(i, 1); //delete x
      }
    }

    const uniqueWords = new Set(words);
    return Array.from(uniqueWords);
  };

  function display(data) {
    const lines = data.UI_Description.split("\n");
    const firstLine = lines[0];
    const restLines = lines.slice(1);

    return (
      <>
        <p className="liHead">{firstLine}</p>
        {restLines.map((line, index) => (
          <p key={index}>{line}</p>
        ))}
      </>
    );
  }

  function searchProducts(products, searchStrings, searchData) {
    setDescriptionValue(null);
    if (
      Array.isArray(products) &&
      searchData !== "" &&
      searchData.length >= MinSearchDataLen
    ) {
      return products.filter((product) => {
        // Check if all search strings match
        return searchStrings.every((searchString) => {
          // Check if searchString matches any of the Key fields

          // Check matching for part mapping tag
          if (userPartData?.length) {
            const _userPartData = userPartData.filter((partData) =>
              partData.tag.toLowerCase().startsWith(searchData.toLowerCase())
            );

            if (_userPartData.length) {
              const isFoundMapping = _userPartData.some((partData) => {
                return partData.product_id.some(
                  (id) => id === product.Product_ID
                );
              });
              if (isFoundMapping) {
                return true;
              }
            }
          }

          return Object.keys(product).some((key) => {
            if (key.startsWith("Key")) {
              const keyElement = product[key];
              if (keyElement === EmptyString || keyElement === null) {
                return false;
              }
              return keyElement.toLowerCase().startsWith(searchString);
            }
            return false;
          });
        });
      });
    }
    return [];
  }

  const qtyEditModeCloseHandler = () => {
    if (
      getValues(`descriptionLines.${index}.descriptionObj`) &&
      getValues(`descriptionLines.${index}.qtyVal`) &&
      !errors?.descriptionLines?.[index]?.qtyVal
    ) {
      setIsQtyInEditMode(false);
    } else {
      setIsQtyInEditMode(true);
    }
  };

  const quantitySizeValidator = (e) => {
    if (isNaN(e.target.value)) {
      return;
    }
    if (e.target.value) {
      const arr = e.target.value.split(".");

      if (arr.length > 1) {
        // e.target.value = arr[0].slice(0, 8) + "." + arr[1].slice(0, 2);
        e.target.value = arr[0].slice(0, 8) + "." + arr[1];
        setValue(`descriptionLines.${index}.qtyVal`, e.target.value);
      } else {
        e.target.value = arr[0].slice(0, 8);
        setValue(`descriptionLines.${index}.qtyVal`, e.target.value);
      }
    }
    register(`descriptionLines.${index}.qtyVal`).onChange(e);
    updateValue(index);
  };

  const dollerPerUmFormatter = (umVal) => {
    const umUnit = getValues(`descriptionLines.${index}.umUnit`);

    const decimal2Formater = get2DigitFormater();
    const decimal4Formater = get4DigitFormater();

    if (umUnit === "Lb") {
      return decimal4Formater.format(umVal).replace("$", "");
    } else {
      return decimal2Formater.format(umVal).replace("$", "");
    }
  }

  const saveUserActivity = (isRemovingLine = false) => {
    const payload = {
      "data": {
        "session_id": sessionId ? sessionId : null,
        "po_line": (index + 1),
        "line_session_id": getValues(`descriptionLines.${index}.sessionId`) ? getValues(`descriptionLines.${index}.sessionId`) : null,
        "in_po_line": !isRemovingLine,
        "description": getValues(`descriptionLines.${index}.descriptionObj`)?.UI_Description ? getValues(`descriptionLines.${index}.descriptionObj`)?.UI_Description : null,
        "qty": getValues(`descriptionLines.${index}.qtyVal`) ? getValues(`descriptionLines.${index}.qtyVal`) : null,
        "qty_unit": getValues(`descriptionLines.${index}.qtyUnit`) ? getValues(`descriptionLines.${index}.qtyUnit`) : null,
        "product_tag": getValues(`descriptionLines.${index}.partNumber`) ? getValues(`descriptionLines.${index}.partNumber`) : null,
        "price": getValues(`descriptionLines.${index}.umVal`) ? getValues(`descriptionLines.${index}.umVal`) : null,
        "price_unit": getValues(`descriptionLines.${index}.umUnit`) ? getValues(`descriptionLines.${index}.umUnit`) : null,
        "extended": getValues(`descriptionLines.${index}.totalVal`) ? getValues(`descriptionLines.${index}.totalVal`) : null,
        "product_id": getValues(`descriptionLines.${index}.descriptionObj`)?.Product_ID ? getValues(`descriptionLines.${index}.descriptionObj`)?.Product_ID : null,
        "reference_product_id": getValues(`descriptionLines.${index}.descriptionObj`)?.id ? getValues(`descriptionLines.${index}.descriptionObj`)?.id : null,
        "shape": getValues(`descriptionLines.${index}.descriptionObj`)?.Key2 ? getValues(`descriptionLines.${index}.descriptionObj`)?.Key2 : null,
        "domestic_material_only": getValues(`descriptionLines.${index}.domesticMaterialOnly`),
      }
    }
    logUserActivity({ url: `${import.meta.env.VITE_API_SERVICE}/user/saveCreatePoLineData`, payload });
  }

  return (
    <>
    <tr>
      <td className={styles.removeLine}>
        {fields.length > 1 && index > 0 && (
           <CommonTooltip
           title={'Click the - button to remove any line items from your order.'}
           tooltiplabel={<>
                <button
                  onClick={() => {
                      setHandleSubmitValidation(false)
                      handleStateZipValidation()
                      saveUserActivity(true);
                      remove(index);
                      updateValue(0);
                    }}
                  >
                    <RemoveLineIcon className={styles.removeLineIcon} />
                    <RemoveLineHoverIcon className={styles.removeLineHoverIcon} />
          </button>
           </>
              
           }
           placement={'bottom-start'}
           classes={{
               popper: 'tooltipPopper',
               tooltip: 'tooltipMain tooltipSearch tooltipAddRow tooltipAddRow tooltipRemove',
               arrow: 'tooltipArrow'
           }}
           localStorageKey="removeLineTooltip"
       />
         
        )}
        <div className={styles.prodId}>{index + 1}</div>
      </td>
      <td>
        <>
          <Controller
            name={register(`descriptionLines.${index}.descriptionObj`).name}
            control={control}
            render={({ field: { ...rest } }) => (
              <Autocomplete
                id={`combo-box-demo${index}`}
                disabled={apiCallInProgress}
                open={descriptionInput.length > 1 && openDescription}
                onOpen={() => setOpenDescription(true)}
                onClose={(e) => {
                  if(e.keyCode === 27){
                    setDescriptionInput('')
                  }
                  setOpenDescription(false)
                }}
                options={products?.length ? products : []}
                value={descriptionValue}
                inputValue={descriptionInput}
                onInputChange={(event, value) => {
                  setDescriptionInput(value);
                }}
                disablePortal={true}
                // className='autoCompleteMain'
                classes={{
                  root: styles.autoCompleteDesc,
                  popper: styles.autocompleteDescPanel,
                  paper: styles.autocompleteDescInnerPanel,
                  listbox: styles.listAutoComletePanel,
                }}
                filterOptions={(options, state) => {
                  return (
                    state.inputValue.length > 1 &&
                    searchProducts(
                      options,
                      getValidSearchData(state.inputValue),
                      state.inputValue
                    )
                  );
                }}
                isOptionEqualToValue={(option, value) =>
                  option.UI_Description === value.UI_Description
                }
                getOptionLabel={(item) => {
                  return item.UI_Description ? item.UI_Description : "";
                }}
                renderInput={(params) => (
                  <div className={styles.lineNone} ref={params.InputProps.ref}>
                    {descriptionInput.length === 0 &&
                      <Tooltip
                        title={'Add products to your order by typing them into this box'}
                        arrow
                        disableInteractive
                        TransitionComponent={Fade}
                        TransitionProps={{ timeout: 600 }}
                        placement={'top'}
                        classes={{
                          popper: 'tooltipPopper',
                          tooltip: 'tooltipMain tooltipDesc',
                          arrow: 'tooltipArrow'
                        }}
                      >
                        <span className={styles.questionIconDesc}>
                          <QuestionIcon className={styles.questionIcon1} />
                          <QuestionHoverIcon className={styles.questionIcon2} />
                        </span>
                      </Tooltip>
                    }

                                     
                    <textarea
                      type="text"
                      {...params.inputProps}
                      placeholder="Click to Enter Line"
                      className={clsx(styles.poDescription)}
                    />
                  </div>
                )}
                {...rest}
                onChange={(event, item) => {
                  updateValue(index, item);
                  setIsQtyInEditMode(true);
                  rest.onChange(item);
                }}
                onBlur={(e) => {
                  rest.onBlur(e);
                  saveUserActivity();
                }}
                renderOption={(props, option) => {
                  return (
                    <span key={option.id} {...props}>
                      {display(option)}
                    </span>
                  );
                }}
              />
            )}
          />
          <Controller
            control={control}
            name={register(`descriptionLines.${index}.partNumber`).name}
            render={({ field: { value, onBlur, ...field }, fieldState: { error } }) => (
              <Tooltip
                title={
                  sameTagErrorMsg && (
                    <span
                      dangerouslySetInnerHTML={{ __html: sameTagErrorMsg }}
                    ></span>
                  )
                }
                arrow
                placement={"bottom-end"}
                disableInteractive
                TransitionComponent={Fade}
                TransitionProps={{ timeout: 200 }}
                classes={{
                  tooltip: "partNumberTooltip",
                }}
              >
                <div className={styles.partNumberFiled}>
                  <input
                    value={value ?? ""}
                    disabled={apiCallInProgress}
                    onKeyUp={(e) => {
                      setValue(
                        `descriptionLines.${index}.partNumber`,
                        e.target.value
                      );
                    }}
                    placeholder="Tag with your part #"
                    className={clsx({ [styles.errorInput]: error?.message })}
                    {...field}
                    onBlur={(e) => {
                      onBlur(e);
                      saveUserActivity();
                    }}
                  />
                  {/* <span>
                    <QuestionIcon />
                  </span> */}
                </div>
              </Tooltip>
            )}
          />

        </>
      </td>
      <td>
        <div className={styles.poQty}>
          <div>
            {isQtyInEditMode ? (
              <ClickAwayListener  onClickAway={qtyEditModeCloseHandler}>
                <Tooltip
                  title={
                    watch(`descriptionLines.${index}.qtyVal`) !== null &&
                    errors?.descriptionLines?.[index]?.qtyVal?.message
                  }
                  arrow
                  placement={"top"}
                  disableInteractive
                  TransitionComponent={Fade}
                  TransitionProps={{ timeout: 200 }}
                  classes={{
                    tooltip: "inputQtyTooltip",
                  }}
                >
                  <input
                    type="text"
                    disabled={apiCallInProgress}
                    className={
                      errors?.descriptionLines?.[index]?.qtyVal?.message &&
                      watch(`descriptionLines.${index}.qtyVal`) !== null &&
                      styles.errorInput
                    }
                    {...register(`descriptionLines.${index}.qtyVal`)}
                      value={watch(`descriptionLines.${index}.qtyVal`) ?? ""}
                    onChange={(e) => {
                        quantitySizeValidator(e);
                    }}
                    onBlur={(e) => {
                      register(`descriptionLines.${index}.qtyVal`).onBlur(e);
                      qtyEditModeCloseHandler();
                      saveUserActivity();
                    }}
                  />
                </Tooltip>
              </ClickAwayListener>
            ) : (
              <p
                onClick={() => {
                  setIsQtyInEditMode(true);
                }}
              >
                {formatToTwoDecimalPlaces(
                  watch(`descriptionLines.${index}.qtyVal`)
                )}
              </p>
            )}
          </div>
          <span className={styles.selectUom}>
            <CustomMenu
              name={register(`descriptionLines.${index}.qtyUnit`).name}
              control={control}
              disabled={apiCallInProgress}
              onChange={() => {
                updateValue(index);
                saveUserActivity();
              }}
              items={
                qtyUnitM[index]?.map((x) => ({ title: x, value: x })) ?? []
              }
              className={styles.uomDrodown}
              MenuProps={{
                classes: {
                  paper: styles.selectUomPaper,
                },
              }}
              placeholder={"Ft"}
            />
          </span>
        </div>
      </td>
      <td>
        <div className={styles.poPerUm}>
          <div>
            {dollerPerUmFormatter(
              watch(`descriptionLines.${index}.umVal`) ?? 0
            )}
          </div>
          <span className={styles.selectUom1}>
            <CustomMenu
              name={register(`descriptionLines.${index}.umUnit`).name}
              control={control}
              disabled={apiCallInProgress}
              onChange={() => {
                updateValue(index);
                saveUserActivity();
                }}
              items={
                priceUnitM[index]?.map((x) => ({ title: x, value: x })) ?? []
              }
              className={styles.uomDrodown}
              MenuProps={{
                classes: {
                  paper: styles.selectUomPaper,
                },
              }}
              placeholder={"Ft"}
            />
          </span>
        </div>
      </td>
      <td>
        <div>
          {formatToTwoDecimalPlaces(
            watch(`descriptionLines.${index}.totalVal`) ?? 0
          )}
        </div>
      </td>
    </tr>
    <tr>
      <td></td>
      <td colSpan={4}>
        <div className={styles.domesticMaterialCheckbox}>
          <div className={styles.div50}>
            {showDomesticMaterialOnly && (
              <label className={clsx(styles.lblCheckbox, "containerChk")}>
                <input
                  type="checkbox"
                  checked={watch(
                    `descriptionLines.${index}.domesticMaterialOnly`
                  )}
                  {...register(
                    `descriptionLines.${index}.domesticMaterialOnly`
                  )}
                  onChange={(e) => {
                    register(`descriptionLines.${index}.domesticMaterialOnly`).onChange(e);
                    saveUserActivity();
                  }}
                />
                <span className={styles.domesticMaterialTex}>
                  Domestic (USA) Material Only
                </span>
                <span className={clsx(styles.checkmark, "checkmark")} />
              </label>
            )}
          </div>
        </div>
      </td>

    </tr>
    </>
  );
};

export default CreatePoTile;
