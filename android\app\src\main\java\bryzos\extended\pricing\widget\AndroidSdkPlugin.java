package bryzos.extended.pricing.widget;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;

@CapacitorPlugin(name = "AndroidSdkPlugin")
public class AndroidSdkPlugin extends Plugin {
    
    @PluginMethod
    public void getTargetSdkVersion(PluginCall call) {
        try {
            JSObject ret = new JSObject();
            int targetSdkVersion = getContext().getApplicationInfo().targetSdkVersion;
            ret.put("targetSdkVersion", targetSdkVersion);
            call.resolve(ret);
        } catch (Exception e) {
            call.reject("Failed to get targetSdkVersion", e);
        }
    }
}