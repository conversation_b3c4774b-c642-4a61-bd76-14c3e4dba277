// @ts-nocheck
import { Notification } from 'electron';
import path from 'path';

let mainWindow;
let notification;
// let notificationList = [];
let userId = null;
let store;
let notificationListOfUsers={};
let channelWindow;

export function setNotificationConstants(_userId, _store, _channelWindow, _mainWindow){
    userId = _userId;
    store = _store;
    channelWindow = _channelWindow;
    mainWindow = _mainWindow;
    // else{
    //     store.set('notificationListOfUsers',{});
    // }
    if(store.has('notificationListOfUsers')){
        notificationListOfUsers = store.get('notificationListOfUsers');
    }
    if( !notificationListOfUsers[userId] ) notificationListOfUsers[userId] = [];
    console.log("Users Notification bucket", notificationListOfUsers[userId].length);
}

// export function resetNotificationList(){
//     if(userId){
//         notificationListOfUsers[userId] = [];
//         // store.set('notificationListOfUsers',notificationListOfUsers);
//     }
// }

function decodeHtmlEntities(html) {
    return html.replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");
}

export function markAsReadNotification(newNotification){
    let notificationArray = [];
    if(newNotification)
    notificationArray = newNotification;
    if(store.has('notificationListOfUsers')){
        console.log('markAsReadNotification')
        const _notificationListOfUsers = store.get('notificationListOfUsers');
        console.log('markAsReadNotification:length>>>',_notificationListOfUsers[userId]?.length);
        if(_notificationListOfUsers[userId]?.length > 0){
            notificationArray = notificationArray.concat(_notificationListOfUsers[userId]);
        }
        // console.log("notificationListOfUsers",notificationListOfUsers);
    }
    if(notificationArray.length > 0)
    mainWindow?.webContents.send(channelWindow.markAsReadNotification, JSON.stringify(notificationArray));
}

export function getNotificationList(){
    if(userId)
        return notificationListOfUsers[userId];
    return [];
} 

export function showNotification(notificationSchema) {
    const iconPath = path.join(__dirname, '..', '..', 'public','Widget_Icon.png');
  
    //  notification.on('click', (event, action) => {
    //   console.log("ACtionnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnnn")
    //   if (action === 'protocol') {
    //     const launchArgument = 'launchArgument'; // Replace with your launch argument
    //     // Perform actions based on the launch argument
    //     console.log('Notification clicked with launch argument:', launchArgument);
    //   }
    // });
    const isDuplicateNotification = notificationListOfUsers[userId].some((_notification) => _notification.notificationId === notificationSchema.notificationId);
    if(!isDuplicateNotification){
        if(process.platform === 'win32') notification = createWindowsNotification(notificationSchema, iconPath);
        else notification = createMacNotification(notificationSchema, iconPath);
        notification.notificationId = notificationSchema.notificationId;
        notification.show();

        notificationListOfUsers[userId].push(notification);
        // store.set('notificationListOfUsers',notificationListOfUsers);
    }
}

const createActionUrl = (navigationSchema, notificatonId) => {
    let actionUrl = `bryzos://${userId}/${notificatonId}/`;
    if(navigationSchema){
        if(navigationSchema.routePath)actionUrl+=`${navigationSchema.routePath}`;
        const state = navigationSchema.state;
        if(state){
            let stateUrl = '/';
            for(const key in state){
                stateUrl+=`${key}=${state[key]},`;
            }
            actionUrl+=`${stateUrl.substring(0,stateUrl.length-1)}`;
        }
    }
    return actionUrl;
}

const createWindowsNotification = (notificationSchema, iconPath) => {
    const title = notificationSchema.title;
    const message = notificationSchema.body;
    const navigationSchema = notificationSchema.navigation;
    // console.group("navigationSchema",navigationSchema)
    const actionUrl = createActionUrl(navigationSchema, notificationSchema.notificationId);
    notification = new Notification({
        appId: 'com.squirrel.bryzos_extended_pricing_widget.bryzos',
        toastXml: `<toast launch="${actionUrl}" activationType="protocol">
           <visual>
             <binding template="ToastImageAndText02">
                <image id="1" src="file://${iconPath}" alt="Image" />
               <text id="1">${title}</text>
               <text id="2">${message}</text>
             </binding>
           </visual>
        </toast>`,
    });
    return notification;
}

const createMacNotification = (notificationSchema, iconPath)=> {
    const title = decodeHtmlEntities(notificationSchema.title);
    const body = decodeHtmlEntities(notificationSchema.body);
    const navigationSchema = notificationSchema.navigation;
    // console.group("navigationSchema",navigationSchema);
    const actionUrl = createActionUrl(navigationSchema, notificationSchema.notificationId);
    notification = new Notification({
        title,
        body,
        icon: iconPath,
      });
      
    notification.on('click', () => {
        // count down button is first element in actions array
        if (mainWindow) {
            mainWindow.webContents.send(channelWindow.handleURI, actionUrl);
            if (mainWindow.isMinimized()) mainWindow.restore();
            if(!mainWindow.isVisible()){
                mainWindow.show();
            }
        }
    });
    return notification;
}

export const notificationHandler = (notification) => {
    showNotification(notification);
    mainWindow?.webContents.send(channelWindow.markAsReadNotification, JSON.stringify([notification]));
}

export const notificationEventsHandler = (channel, channelEvents) => {
    channelEvents.forEach($event => {
        channel.bind( $event, (data) => {
        // console.log('Received notification:', data);
            notificationHandler(data.notification);
        });
    });
}