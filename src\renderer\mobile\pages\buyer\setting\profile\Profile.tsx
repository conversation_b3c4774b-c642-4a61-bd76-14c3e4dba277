// @ts-nocheck
import { IonPage, IonContent, useIonRouter, useIonViewWillEnter, useIonViewDidLeave, useIonViewWillLeave, useIonLoading, useIonViewDidEnter } from '@ionic/react';
import styles from './Profile.module.scss';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { profileSchema } from '../SettingSchema';
import { useEffect, useRef, useState } from 'react';
import { buyerSettingPayLoadFormatter, formatPhoneNumber, formatPhoneNumberRemovingCountryCode, formatPhoneNumberWithCountryCode, saveUserSetting, unformatPhoneNumber } from '../../../../library/helper';
import clsx from 'clsx';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import axios from 'axios';
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import useBuyerSettingStore, { Profile as ProfileModel } from '../BuyerSettingStore';
import useDialogStore from '../../../../components/Dialog/DialogStore';
import { buyerSettingConst,mobileDiaglogConst } from '../../../../library/common';

const Profile = () => {
    const {
        register,
        handleSubmit,
        clearErrors,
        setValue,
        reset,
        formState: { errors, dirtyFields, isDirty, isValid } 
    } = useForm({
            resolver: yupResolver(profileSchema)
        });

    const { ref, ...rest } = register("first_name");

    const [isSaveDisable, setIsSaveDisable] = useState(true);
    const {userData, setShowLoader} = useGlobalStore();
    const { profileInfo, setProfileInfo, buyerSetting, setBuyerSettingInfo } = useBuyerSettingStore();
    const router = useIonRouter();
    const { showCommonDialog, resetDialogStore } = useDialogStore();

    const firstNameRef = useRef<HTMLInputElement>(null);


    useIonViewDidEnter(() => {
        firstNameRef.current!.focus();
    }, []);

    useIonViewWillEnter(() => {
        if (profileInfo) {
            setValue('first_name', profileInfo.first_name);
            setValue('last_name', profileInfo.last_name);
            setValue('email_id', profileInfo.email_id);
            setValue('phone', formatPhoneNumberRemovingCountryCode(profileInfo.phone));
            clearErrors();
        }
    }, [profileInfo])

    useIonViewWillLeave(() => {
        setIsSaveDisable(true);
        reset();
        resetDialogStore();
    }, [])

    useEffect(() => {
        if (isDirty)
            setIsSaveDisable(false);

        // const handleBackButton = (ev: BackButtonEvent) => {
        //     ev.detail.register(10, async () => {
        //         backToSetting();
        //     });
        // };

        // document.addEventListener('ionBackButton', handleBackButton);
        // return () => {
        //     document.removeEventListener('ionBackButton', handleBackButton);
        // };
    }, [isDirty])

    const handlePhoneNoChange = (event) => {
        const { value } = event.target;
        setValue("phone", formatPhoneNumber(value));
    };

    const onSubmit = async(data) => {
        setShowLoader(true)
        const detail : ProfileModel = data;  
        // const _buyerSetting = {...buyerSetting, ...detail};
        // const payload: any = buyerSettingPayLoadFormatter(_buyerSetting);
        try{
            await saveUserSetting(buyerSettingConst.apiRoutesForSave.profile, {...detail, phone: unformatPhoneNumber(detail.phone)}, userData);
            // setBuyerSettingInfo(_buyerSetting);
            setProfileInfo(detail);
            router.push('/setting',{animate:true,direction:'forward'});
            setShowLoader(false);
        }catch(err){
            setShowLoader(false);
        }
    }

    const handleFormSubmit = () => {
        console.log("CLICKED",Object.keys(errors))
        if (Object.keys(errors).length === 0) {
            handleSubmit(onSubmit)();
        } else {
            console.log("error",Object.keys(errors))
            return;
        }
    }
    const routeBackToSetting = ()=>{
        router.push('/setting',{animate:true,direction:'forward'});
        resetDialogStore();
    }

    const backToSetting = () => {
        if(isDirty)
        showCommonDialog(mobileDiaglogConst.unsavedChangesMsg,[{name: mobileDiaglogConst.stay, action: resetDialogStore},{name: mobileDiaglogConst.goBackToSetting, action: routeBackToSetting}]);
        else
        routeBackToSetting();
    }


    return (
        <IonPage>
            <IonContent>
                <div className={styles.profile}>
                    <h2 className={styles.heading}><BackBtnArrowIcon onClick={backToSetting}/><span>Profile</span></h2>
                    <div className={styles.settingsContent}>
                        <div className={styles.profileComponent}>
                            <label > First Name</label>
                            <input type="text" {...rest} placeholder="First Name"
                                className={clsx(styles.inputField, errors?.first_name?.message && styles.errorMsg)}
                                onBlur={(e) => {
                                    e.target.value = e.target.value.trim();
                                    rest.onBlur(e);
                                }}
                                ref={(e) => { ref(e); firstNameRef.current = e; }}
                            />

                        </div>
                        <div className={styles.profileComponent}>
                            <label > Last Name</label>
                            <input type="text" {...register("last_name")} placeholder="Last Name"
                                className={clsx(styles.inputField, errors?.last_name?.message && styles.errorMsg)}
                                onBlur={(e) => {
                                    e.target.value = e.target.value.trim();
                                    register("last_name").onBlur(e);
                                }} />
                        </div>
                        <div className={styles.profileComponent}>
                            <label > Email</label>
                            <input type="email" {...register("email_id")} placeholder="Email address"
                                className={clsx(styles.inputField, errors?.email_id?.message && styles.errorMsg)}
                                onBlur={(e) => {
                                    e.target.value = e.target.value.trim();
                                    register("email_id").onBlur(e);
                                }} />
                        </div>
                        <div className={styles.profileComponent}>
                            <label > Phone</label>
                            <input
                                className={clsx(styles.inputField, errors?.phone?.message && styles.errorMsg)}
                                pattern="[0-9]"
                                maxLength="14"
                                type="tel"
                                {...register("phone")} onChange={(e) => {
                                    register("phone").onChange(e)
                                    handlePhoneNoChange(e)
                                }}
                                placeholder="(xxx) xxx-xxxx"
                            />
                        </div>
                    </div>
                    <div className={styles.btnSection}>
                        <button className={styles.saveBtn} onClick={() => handleFormSubmit()} disabled={isSaveDisable}>Save</button>
                    </div>

                </div>
            </IonContent>
        </IonPage>
    )
}

export default Profile;