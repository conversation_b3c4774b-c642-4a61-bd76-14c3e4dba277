.splashContainer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: white;
    font-family: Noto Sans Display;
    text-align: center;
    background-color: #000; 
  }
  
  .bryzos<PERSON>ogo {
    animation: fadeInOut 3s infinite; // Apply animation to the logo
    svg{
        height: 100px;
        width:100px
    }
  }
  
  @keyframes fadeInOut {
    0% {
      opacity: 0; // Start at invisible
    }
    50% {
      opacity: 1; // Fully visible at the halfway point
    }
    100% {
      opacity: 0; // End at invisible again
    }
  }
  
.capgoUpdateTxt{
  font-size: 16px;
  line-height: normal;
}