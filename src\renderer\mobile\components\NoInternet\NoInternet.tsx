import React, { useContext } from "react";
import { IonModal } from "@ionic/react";
import { ReactComponent as NoInternetIcon } from '../../../assets/mobile-images/No_Internet_Connection.svg';

type Props = {
    open: boolean;
}

const NoInternet: React.FC<Props> = ({ open }) => {

    return (
        <IonModal className="noInternet" isOpen={open} backdropDismiss={false}>
            <div>
                <NoInternetIcon/>
            </div>
            <p>Internet not available</p>
        </IonModal>
    );
};

export default NoInternet;