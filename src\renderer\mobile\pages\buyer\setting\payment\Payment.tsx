// @ts-nocheck
import { IonPage, IonContent, useIonRouter, useIonLoading, useIonViewWillEnter, useIonViewWillLeave } from '@ionic/react';
import styles from './Payment.module.scss';
import {  useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import {  paymentSettingSchema} from '../SettingSchema';
import { useEffect, useState } from 'react';
import {  buyerSettingPayLoadFormatter, formatCurrency, removeCommaFromCurrency, saveUserSetting } from '../../../../library/helper';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import clsx from 'clsx';
import {  buyerSettingConst, mobileDiaglogConst, userRole , BNPL_STATUS } from '../../../../library/common';
import ApplyForBnpl from './bnpl/applyForBnpl/ApplyForBnpl';
import RequestForCreditLimit from './bnpl/requestForCreditLimit/RequestForCreditLimit';
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import useBuyerSettingStore from '../BuyerSettingStore';
import useDialogStore from '../../../../components/Dialog/DialogStore';

const Payment = () => {
  
    const { 
        register, 
        handleSubmit, 
        getValues, 
        clearErrors, 
        setValue, 
        watch, 
        setError, 
        control, 
        reset,
        formState: { errors, isDirty, dirtyFields } } = useForm({
        resolver: yupResolver(paymentSettingSchema)
    });
    const [net30Default, setNet30Default] = useState('');
    const [achDefault, setAchDefault] = useState('');
    // const [achTruevaultId, setAchTruevaultId] = useState(null);
    const [isApproved, setIsApproved] = useState(undefined);
    const [achApproved, setAchApproved] = useState('');
    const [creditStatus, setCreditStatus] = useState('');
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
    const [isSaveDisable, setIsSaveDisable] = useState(true);
    const [isSaveEnable, setIsSaveEnable] = useState(true);
    const [validationInProgress, setValidationInProgress] = useState(true);
    const [net30ApplyStatus, setNet30ApplyStatus] = useState(false);
    const {userData, setShowLoader, referenceData} = useGlobalStore();
    const [isRoutingMasked, setRoutingMasked] = useState(true);
    const [isAccountMasked, setAccountMasked] = useState(true);
    const {paymentInfo, setPaymentInfo, setBuyerSettingInfo, buyerSetting} = useBuyerSettingStore();
    const router = useIonRouter();
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const [bnplStatus, setBnplStatus] = useState(BNPL_STATUS.enabled);
    const [maxRestrictedAmount, setMaxRestrictedAmount] = useState(0);

    useEffect(() => {
        if (dirtyFields.bankName || dirtyFields.routingNo || dirtyFields.accountNo) {
            setIsSaveDisable(false);
        }
        if (dirtyFields.dnBNumber || dirtyFields.creditLine || dirtyFields.einNumber) {
            handlePaymentMethodChange(net30Default)
            setIsSaveDisable(true);
        }
        // const handleBackButton = (ev: BackButtonEvent) => {
        //     ev.detail.register(10, async () => {
        //         backToSetting();
        //     });
        // };

        // document.addEventListener('ionBackButton', handleBackButton);
        // return () => {
        //     document.removeEventListener('ionBackButton', handleBackButton);
        // };
    }, [isDirty])

    useIonViewWillLeave(()=>{
        setAchDefault('')
        setNet30Default('');
        setIsSaveDisable(true);
        reset();
        resetDialogStore();
    },[])

    useIonViewWillEnter(()=>{
        if(referenceData){
            referenceData.ref_pgpm_mapping.map((paymentMethod) => {
                if (paymentMethod.user_type === userRole.buyerUser.toLowerCase() && paymentMethod.payment_gateway === 'BALANCE') {
                    setNet30Default(paymentMethod.payment_method);
                }
                if (paymentMethod.user_type === userRole.buyerUser.toLowerCase() && paymentMethod.payment_gateway === 'DEFAULT') {
                    setAchDefault(paymentMethod.payment_method);
                }
            })
            if(referenceData.ref_account_routing_number[0]){
                if(referenceData.ref_account_routing_number[0]?.account_number){
                    setValue('refAccountNo', referenceData.ref_account_routing_number[0]?.account_number);
                }
                if(referenceData.ref_account_routing_number[0]?.routing_number){
                    setValue('refRoutingNo', referenceData.ref_account_routing_number[0]?.routing_number);
                }
            }
        }
    },[referenceData])

    useIonViewWillEnter(() => {
        if (paymentInfo) {
            const {ach, bnpl} = paymentInfo;
            setSelectedPaymentMethod(paymentInfo.default_payment_method);
            if (ach) {
                setValue('bankName', ach.bank_name);
                setValue('routingNo', ach.routing_number);
                setValue('accountNo', ach.account_number);
                // setAchTruevaultId(ach.truvault_document_id);
                setAchApproved(ach.is_approved);
            }
            setBnplSettingToForm(bnpl);
            clearErrors();
        }
    }, [paymentInfo])

    const setBnplSettingToForm = (bnpl) => {
        if (bnpl) {
            let requestedCredit = formatCurrency(parseFloat(bnpl.requested_credit_limit))
            let balanceCredit = formatCurrency(bnpl.balance_credit_limit);
            let requestedIncreaseCredit = formatCurrency(parseFloat(bnpl.requested_increase_credit))
            setValue('creditLimit', balanceCredit)
            if (bnpl.is_approved !== 0) {
                setValue('bnplAvailable', true);
                setValue('dnBNumber', bnpl.duns);
                setValue('einNumber', bnpl.ein_number);
                setValue('creditLine', requestedCredit);
            }
            if(bnpl.is_approved === null){
                setValue('creditLine', requestedCredit)
            }else if(bnpl.is_approved === 1 && bnpl?.credit_status !== 'Pending Increase'){
                setValue('creditLine', balanceCredit);
            }else if(bnpl.is_approved === 1 && bnpl.credit_status === 'Approved'){
                setValue('creditLine', balanceCredit)
            }else{
                setValue('creditLine', requestedIncreaseCredit)
            }
            setNet30ApplyStatus(true);
            let availableBalanceCredit = bnpl.balance_available_credit_limit ? formatCurrency(parseFloat(bnpl.balance_available_credit_limit)) : ''
            setValue('availableBalance', availableBalanceCredit)
            let outstandingValue = '';
            if (bnpl.outstanding_amount !== null && bnpl.outstanding_amount !== 0) {
                outstandingValue = bnpl.outstanding_amount;
            }
            let outstandingBalanceAmount = outstandingValue.length !== 0  ? formatCurrency(parseFloat(outstandingValue)) : '';
            setValue('outstandingAmount', outstandingBalanceAmount)
            setIsApproved(bnpl.is_approved);
            if(bnpl.is_approved === null) setValue('creditStatus', 'Pending')
            else if(bnpl.is_approved === 1 && (bnpl.credit_status === null || bnpl.credit_status === 'Approved' || bnpl.credit_status === 'Cancelled')) setValue('creditStatus', 'Approved')
            else if(bnpl.credit_status) setValue('creditStatus', bnpl.credit_status)
            setCreditStatus(bnpl.credit_status);
            if(bnpl.bnpl_status){
                setBnplStatus(bnpl.bnpl_status);
            }
            if(bnpl.max_restricted_amount){
                setMaxRestrictedAmount(bnpl.max_restricted_amount);
            }

        } else {
            setIsApproved(undefined);
            setCreditStatus('');
        }
    }

    const handlePaymentMethodChange = (method) => {
        console.log('check calling payemtn method change')
    if (selectedPaymentMethod === method) {
        // Deselect the selected method
        setSelectedPaymentMethod(null);
        let fields = [];
        if (method === achDefault) {
            fields = ['bankName', 'routingNo', 'accountNo'];
        } else if (method === net30Default) {
            fields = ['dnBNumber', 'einNumber', 'creditLine'];

        }
        for (let trial in fields) {
            clearErrors(fields[trial]);
        }
    } else {
        setSelectedPaymentMethod(method);
        if(method === achDefault){
            setValue('net30CheckBox', false);
            const fields = ['dnBNumber', 'einNumber', 'creditLine'];
            for (let trial in fields) {
                clearErrors(fields[trial]);
            }
        }
    }
        if (method === achDefault) {
            setIsSaveDisable(false);
        }
        // if (method === net30Default && ) {
        //     setIsSaveDisable(false);
        // }
    };

    const requestCreditLineChangeHandler = (e, fieldName) => {
        let value = e.target.value;
        
        if(!isNaN(removeCommaFromCurrency(value))) {
            console.log("CHANGE",removeCommaFromCurrency(value))
            setValue(fieldName, removeCommaFromCurrency(value));
        }
    }

    const onSubmit = async(isEnable) => {
        setShowLoader(true);
        const detail = {default_payment_method: selectedPaymentMethod}
        // const _buyerSetting = {...buyerSetting, ...payload};
        // const payload: any = buyerSettingPayLoadFormatter(_buyerSetting);
        try{
            await saveUserSetting(buyerSettingConst.apiRoutesForSave.saveDefaultPaymentMethod, detail, userData);
            // setBuyerSettingInfo(_buyerSetting);
            setPaymentInfo({...paymentInfo, ...detail});
            console.log(isEnable)
            !isEnable && router.push('/setting',{animate:true,direction:'forward'});
            setShowLoader(false);
        }catch(err){
            setShowLoader(false);
        }
    }

    const handleFormSubmit = () => {
        console.log('check errorss', errors)
        if(Object.keys(errors).length === 0 && validationInProgress){
            onSubmit();
        }else{
            return;
        }
    }

    const routeBackToSetting = ()=>{
        router.push('/setting',{animate:true,direction:'forward'});
        resetDialogStore();
    }

    const backToSetting = () => {
        if(!isSaveDisable)
        showCommonDialog(mobileDiaglogConst.unsavedChangesMsg,[{name: mobileDiaglogConst.stay, action: resetDialogStore},{name: mobileDiaglogConst.goBackToSetting, action: routeBackToSetting}]);
        else
        routeBackToSetting();
    }

    return (
        <IonPage>
            <IonContent>
                <div className={styles.profile}>
                    <h2 className={styles.heading}><BackBtnArrowIcon onClick={backToSetting}/><span>Payment Settings</span></h2>
                    <div className={styles.settingsContent}>
                        <div className={clsx(bnplStatus === BNPL_STATUS.onHold || bnplStatus === BNPL_STATUS.restricted ? styles.disableSection : '')}>
                        {isApproved !== undefined ?
                                <RequestForCreditLimit 
                                    userData={userData}
                                    register={register}
                                    getValues={getValues}
                                    watch={watch}
                                    handleSubmit={handleSubmit}
                                    setValue={setValue}
                                    setError={setError}
                                    errors={errors}
                                    clearErrors={clearErrors}
                                    isApproved={isApproved}
                                    creditStatus={creditStatus}
                                    net30Default={net30Default}
                                    selectedPaymentMethod={selectedPaymentMethod}
                                    handlePaymentMethodChange={handlePaymentMethodChange}
                                    requestCreditLineChangeHandler={requestCreditLineChangeHandler}
                                    setIsSaveDisable={setIsSaveDisable}
                                /> 
                            :
                                <ApplyForBnpl
                                    userData={userData}
                                    register={register}
                                    watch={watch}
                                    getValues={getValues}
                                    setValue={setValue}
                                    setError={setError}
                                    clearErrors={clearErrors}
                                    setNet30ApplyStatus={setNet30ApplyStatus}
                                    net30Default={net30Default}
                                    selectedPaymentMethod={selectedPaymentMethod}
                                    setIsApproved={setIsApproved}
                                    handlePaymentMethodChange={handlePaymentMethodChange}
                                    handleSubmit={handleSubmit}
                                    requestCreditLineChangeHandler={requestCreditLineChangeHandler}
                                    onSubmit={onSubmit}
                                    dirtyFields={dirtyFields}
                                    errors={errors}
                                    isSaveDisable={isSaveDisable}
                                />
                            }
                        </div>
                        <div className={styles.bnplStatusNoteContainer}>
                            {
                                bnplStatus === BNPL_STATUS.onHold && (
                                    <div className={styles.bnplStatusNote}>
                                        <p>Your BNPL payment method is on hold because payment for your previous order(s) has not been received. Please choose different payment method. Please contact Bryzos support for more information.</p>
                                    </div>
                                )
                            }
                            {
                                bnplStatus === BNPL_STATUS.restricted && (
                                    <div className={styles.bnplStatusNote}>
                                        <p>Your BNPL payment credit limit is restricted upto <span>{formatCurrency(maxRestrictedAmount)}</span>. Please contact Bryzos support for more information.</p>
                                    </div>
                                )
                            }
                        </div>
                      
                        <div>
                            <div className={styles.cashInAdvancedSection}>
                                <label className={styles.containerRadio}>
                                    <input type='checkbox' {...register('achCheckBox')} value={achDefault} checked={selectedPaymentMethod === achDefault} onChange={(e) => {
                                        register("achCheckBox").onChange(e)
                                        handlePaymentMethodChange(achDefault)
                                    }} />
                                    <span>Cash In Advance</span>
                                    <span className={styles.checkmark} />
                                </label>
                            </div>
                            <div>
                                <span className={clsx(styles.inputSection, styles.bdrRadius0, styles.bdrRight0, errors.bankName && styles.borderOfError)}>
                                    <label>Bank Name</label>
                                    <span className={styles.textHoverMask}>
                                        <span>{getValues('bankName')}</span>
                                    </span>
                                </span>
                                <span className={clsx(styles.inputSection, styles.accountNo, styles.bdrRadius0, styles.bdrRight0, errors.routingNo && styles.borderOfError)}>
                                    <label>Routing No.</label>
                                    <span className={styles.textHoverMask} onTouchStart={()=>{setRoutingMasked(false)}} onTouchEnd={()=>{setRoutingMasked(true)}}>
                                        <span>{isRoutingMasked ? getValues("routingNo") : getValues('refRoutingNo')}</span>
                                    </span>
                                </span>
                                <span className={clsx(styles.inputSection, styles.accountNo, errors.accountNo && styles.borderOfError)}>
                                    <label>Account No.</label>
                                    <span className={styles.textHoverMask} onTouchStart={()=>{setAccountMasked(false)}} onTouchEnd={()=>{setAccountMasked(true)}}>
                                        <span>{isAccountMasked ? getValues("accountNo") : getValues('refAccountNo')}</span>
                                    </span>
                                </span>
                            </div>
                        </div>
                        </div>
                    <div className={styles.btnSection}>
                        <button onClick={() => handleFormSubmit()}  disabled={ isSaveDisable }>Save</button>
                    </div>
                </div>
            </IonContent>
        </IonPage>
    )
}

export default Payment


