// @ts-nocheck
import { IonContent, IonPage, useIonLoading, useIonRouter } from "@ionic/react";
import { commomKeys, fileType, orderConfirmationConst, prefixUrl, userRole } from "../../library/common";
import { useGlobalStore } from "@bryzos/giss-ui-library";
import styles from './OrderConfirmation.module.scss';
import { useEffect, useState } from "react";
import clsx from "clsx";
import { v4 as uuidv4 } from 'uuid';
import axios from "axios";
import { downloadFiles, downloadFileWithAnyExtension, uploadBuyerDocumentInfoToS3 } from "../../library/helper";
import useDialogStore from "../Dialog/DialogStore";

const OrderConfirmation = (props: any) => {
    const userData = useGlobalStore((state: any) => state.userData);
    const PAYMENT_TYPE_ACH_CREDIT = "ach_credit";
    const router = useIonRouter();
    const { showCommonDialog, resetDialogStore }: any = useDialogStore();
    const [disableUpload, setDisableUpload] = useState(false);
    const [uploadProgress, setUploadProgress] = useState<any>(null);
    const [internalPoNumber, setInternalPoNumber] = useState();
    const [rating, setRating] = useState(0);
    const buyer = userData?.data?.type === userRole.buyerUser;
    const seller = userData?.data?.type === userRole.sellerUser;
    const setShowLoader = useGlobalStore(state => state.setShowLoader);

    useEffect(() => {
        if(seller){
            setShowLoader(true);
            axios.get(import.meta.env.VITE_API_SERVICE + '/user/getResaleListOfBuyer/' + props.poNumber)
                .then(res => {
                    setInternalPoNumber(res.data.data.buyer_internal_po);
                    setShowLoader(false);
                })
                .catch(err => {
                    console.error(err)
                    setShowLoader(false)
                });
        }
        return(()=>{
            resetDialogStore()
        })
    }, [])

    const uploadFile = async (event: any, prefix: string, bucketName: string, suffixUrl: string) => {
        const file = event.target.files[0];

        if (event.target.files.length !== 0) {
            // setUploadCertName1(file.name);
            setShowLoader(true)
            setDisableUpload(true);
            setUploadProgress(true);
            try{
                const certUrl = await uploadBuyerDocumentInfoToS3(file,userData,prefix,bucketName);
                const payload:any = {
                    "data": {
                        "po_number": props.poNumber,
                        "upload_po_s3_urls": [certUrl]
                    }
                }
                await axios.post(import.meta.env.VITE_API_SERVICE + '/user/save/' + suffixUrl, payload)
                setShowLoader(false)
                showCommonDialog(orderConfirmationConst.uploadPoDialogContent, [{ name: commomKeys.successBtnTitle, action: resetDialogStore }])
                setUploadProgress(false);
            }catch(error){
                setShowLoader(false)
                setDisableUpload(false);
                setUploadProgress(null);
                showCommonDialog(commomKeys.errorContent, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
                console.error(error)
            }
        }
    }

    const downloadCertificate = async()=>{
        try{
            setShowLoader(true);
            let res;
            if(buyer) res= await axios.get(import.meta.env.VITE_API_SERVICE + '/user/buyer/w9form')
            else res= await axios.get(import.meta.env.VITE_API_SERVICE + '/user/bryzosStatesResaleCertUrl')
            if(res.data.data){
                const url = res.data.data
                const fileName = url.split('/')
                await downloadFiles(url, fileName[fileName.length -1], fileType.pdf)
            }
        }catch(e) {
            showCommonDialog(commomKeys.errorContent, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
        } finally {
            setShowLoader(false);
        }
    };

    const cancelOrder = () => {
        resetDialogStore()
        setShowLoader(true)
        const payload = {
            data: {
                po_number: props.poNumber,
                type: (buyer ? orderConfirmationConst.buyerCancel : orderConfirmationConst.sellerCancel)
            }
        }
        axios.post(import.meta.env.VITE_API_ADMIN_SERVICE_NODE + '/cancel_order/user', payload)
            .then(res => {
                setShowLoader(false);
                buyer && props.instantPurchansingInit() 
                seller && router.push('/order-listing',{animate:true,direction:'forward'});
            })
            .catch(err => { setShowLoader(false); console.error(err) });
    };

    const handleCheckboxClick = (value: number) => {
        setRating(value);
        const poRatingPayload = {
            "data": {
                "po_number": props.poNumber,
                "rating": value.toString()
            }
        }
        const suffixUrl = buyer ? 'purchaseOrderRatings' : 'salesOrderRatings'
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/save/' + suffixUrl, poRatingPayload)
            .then(res => { })
            .catch(err => console.error(err))
    };


    const buyerConfirmation = (
        <div>
            <span className={styles.orderConfirmation}>Thanks for the Order !!</span>
            <div className={styles.emailHeder} >
                <span className={styles.orderEmail}>We have just emailed the order </span>
                <br />
                {props.selectedOptionPayment === PAYMENT_TYPE_ACH_CREDIT ?
                    <span className={styles.orderEmail}> confirmation to <span className={styles.emailIdInvoice}>{userData.data.email_id} <span className={styles.orderEmail}>and will send invoice to</span> {props.sendInvoicesTo}</span>.</span> :
                    <span className={styles.orderEmail}> confirmation to <span className={styles.emailIdInvoice}>{userData.data.email_id}</span>.</span>
                }
            </div>
            <div className={styles.poBoxOne}>
                <div className={styles.poNumberCreator}>{props.poNumber}<span className={styles.poNumber}>(PO# {props.jobNumber})</span></div>

            </div>

        </div>
    );
    const sellerConfirmation = (
        <div>
            <span className={styles.orderConfirmation}>Congrats on the new order!</span>
            <div className={styles.emailHeder} >
                <span className={styles.orderEmail}>We have just emailed the order </span>
                <br />
                <span className={styles.orderEmail}> confirmation to <span className={styles.emailIdInvoice}>{userData.data.email_id}</span>.</span>
            </div>
            <div className={styles.poBoxOne}>
                <div className={styles.poNumberCreator}>{props.poNumber}<span className={styles.poNumber}>(PO# {internalPoNumber})</span></div>

            </div>
        </div>
    )

    const backToSearchPage = () => {
        router.push('/search',{animate:true,direction:'forward'}); 
        setDisableUpload(false);
        seller && props.setOpenOrderConfirmation(false)
    }
    return (
                <div className={styles.orderConfirmationMain}>
                    <div className={styles.orderConfirmationContent}>
                    {buyer ?
                        buyerConfirmation
                        :
                        seller &&
                        sellerConfirmation
                    }

                    <div className={styles.uploadYourPo1}>

                        <label>
                            <div className={clsx(styles.uploadYourPoDiv, (disableUpload) && styles.disabled)}>
                                {uploadProgress === true ?
                                    <span className={styles.poNumber}>Uploading </span> :
                                    uploadProgress === false ?
                                        <span className={styles.poNumber}>{buyer ? 'PO Uploaded' : 'SO Uploaded'} </span> :
                                        buyer ?
                                            <>
                                                <span className={styles.poNumber}>Upload Your PO </span>
                                                <span className={styles.emailIdInter}>or, <NAME_EMAIL></span>
                                                <input type='file' onChange={(e) => uploadFile(e, prefixUrl.buyerPo, import.meta.env.VITE_S3_UPLOAD_SETTINGS_PO_BUCKET_NAME, 'uploadPoS3Url')} disabled={disableUpload} />
                                            </>
                                            : seller &&
                                            <>
                                                <span className={styles.poNumber}>Upload Your SO</span>
                                                <span className={styles.emailIdInter}>or, <NAME_EMAIL></span>
                                                <input type='file' onChange={(e) => uploadFile(e, prefixUrl.sellerSo, import.meta.env.VITE_S3_UPLOAD_SETTINGS_SO_BUCKET_NAME, 'uploadSoS3Url')} disabled={disableUpload} />
                                            </>
                                }
                            </div>
                        </label>
                    </div>
                    {props?.selectedOptionPayment === PAYMENT_TYPE_ACH_CREDIT && (
                        <div className={styles.uploadYourPo}>
                            <span className={styles.downoadInvoiceBtn}>Download Invoice<br />
                                <span className={styles.emailIdInter}>Order cannot be fulfilled until payment received</span></span>
                        </div>
                    )}

                    <div className={clsx(styles.uploadYourPo)} >
                        <span>
                            <span className={clsx(styles.poNumber)} >Download Confirmation & </span>
                            <span className={clsx(styles.poNumber, styles.pointer)} onClick={() => { downloadCertificate() }}>
                                {buyer ? <span>W-9</span> : <span><br />Sales Tax Exemption Certificate</span>}
                            </span>
                        </span>
                    </div>

                    <div onClick={() => { buyer ? props.instantPurchansingInit() : router.push('/order-listing',{animate:true,direction:'forward'}) }} className={clsx(styles.uploadYourPo, styles.pointer)}>
                        <span className={styles.poNumber}> {buyer ? 'Create Another Purchase' : 'Claim Another Order'}</span>
                    </div>
                    <div onClick={() => { showCommonDialog("Do you want to continue?", [{ name: 'Yes', action: cancelOrder }, { name: 'No', action: resetDialogStore }]) }} className={clsx(styles.uploadYourPo, styles.pointer)}>
                        <span className={styles.poNumber}>Cancel Order</span>
                    </div>
                    <div className={styles.setRatingBox} >
                        {/* <span><Staroutlinedicon /><Staroutlinedicon /><Staroutlinedicon /><Staroutlinedicon /><Staroutlinedicon /></span> */}
                        <div className="star-rating">
                            <input type="checkbox" id="star1" name="rating" value="1" checked={rating >= 1}
                                onChange={() => handleCheckboxClick(1)} />
                            <label htmlFor="star1"></label>
                            <input type="checkbox" id="star2" name="rating" value="2" checked={rating >= 2}
                                onChange={() => handleCheckboxClick(2)} />
                            <label htmlFor="star2"></label>
                            <input type="checkbox" id="star3" name="rating" value="3" checked={rating >= 3}
                                onChange={() => handleCheckboxClick(3)} />
                            <label htmlFor="star3"></label>
                            <input type="checkbox" id="star4" name="rating" value="4" checked={rating >= 4}
                                onChange={() => handleCheckboxClick(4)} />
                            <label htmlFor="star4"></label>
                            <input type="checkbox" id="star5" name="rating" value="5" checked={rating >= 5}
                                onChange={() => handleCheckboxClick(5)} />
                            <label htmlFor="star5"></label>
                        </div>

                        <span className={styles.emailIdInter} >Rate Your Purchasing Experience!</span>
                    </div>

                    <div onClick={() => {backToSearchPage() }} className={styles.returnInstantPricing}>{seller ? 'Return to Instant Pricing' : 'Return to Home'}</div>
                    </div>
                </div>
    )
}
export default OrderConfirmation;