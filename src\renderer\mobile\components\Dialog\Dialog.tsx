import { IonModal } from '@ionic/react';
import styles from './Dialog.module.scss';
import useDialogStore, { DialogAction } from './DialogStore';
import clsx from 'clsx';


const Dialog = (props: any) => {

    const { openDialog, header, actions, content } = useDialogStore();
    return (
        <IonModal className={'savePopup'} isOpen={!!openDialog} backdropDismiss={false}>
            <p className={styles.containOfError}>{header}</p>
            <p className={styles.dialogContent}>{content}</p>
            {
                actions?.map((action: DialogAction, index: number)=>{
                    return <button key={index} className={styles.saveChangesBtn} onClick={action.action}>{action.name}</button>
                })
            }
        </IonModal>
    )
}

export default Dialog;