ion-header{
    position: sticky;
    z-index: 999;
    top: 0px;
    // -webkit-backdrop-filter: blur(20px);
    // backdrop-filter: blur(20px);
    // box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.4);
    background-image: linear-gradient(to top, #000 7%, rgba(0, 0, 0, 0) 99%, rgba(0, 0, 0, 0) 99%);
}

.topHeaderBG {
    width: 100%;
    height: var(--headerHeight1);
    // padding: 0px 0 10px;
}

.topTitle {
    padding: 3px 15px;
    color: #fff;
    font-size: 14px;
    border-top: solid 0.5px rgba(255, 255, 255, 0.3);
    border-bottom: solid 0.5px rgba(255, 255, 255, 0.3);
    background-color: rgba(0, 0, 0, 0.3);
    font-family: Noto Sans Display;
    font-size: 12px;
    font-weight: 300;
    line-height: 1.4;
    text-align: left;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: row;
}

.searchHeader {
    width: 100%;
    height: 50px;
    display: inline-flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px 15px;

    span {
        display: inline-flex;
        align-items: center;
    }

    .headerMenu {
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: 300;
        line-height: 1.2;
        text-align: left;
        color: rgba(255, 255, 255, 0.8);
        margin-right: 10px;
        text-align: center;
    }

    .disabledMenu{
        opacity: 0.4;
    }

    .activeMenu {
        color: #70ff00;
        font-weight: 600;
        svg{
            path{
                fill:#70ff00
            }
        }
    }

    .logoutBtn{
        font-family: Noto Sans Display;
        font-size: 13px;
        font-weight: normal;
        line-height: 1.4;
        color: #70ff00;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        svg{
            margin-right: 5px;
            path{
                fill: #70ff00;
            }
        }
     }
    
}

.closePopupBtn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 9;
}

.hidden{
    display: none;
}

.badgeContainer{
    flex: 1;
    text-align: center;
    .badge{
        padding: 2px 16px;
        border-radius: 20px;
        font-family: Noto Sans;
        color: #fff;
    }
}

.dev_env, .staging_env{
    .badge{
        background-image: linear-gradient(to right, #00c6ff, #0072ff);
    }
}

.qa_env{
    .badge{
        background-image: linear-gradient(to right, #006757, #299501);
    }
}

.demo_env{
    .badge{
        background-image: linear-gradient(to right, #ec008c, #fc6767);
    }
}

.prod_env{
}


.chatIcon {
    margin-top: 0px;
    padding: 2px;
    svg{
        opacity: 0.7;
        width: 26px;
        height: 26px;
    }
}

.activeMenu {
    svg {
        opacity: unset;
        path {
            fill: #70ff00;
        }
    }
}