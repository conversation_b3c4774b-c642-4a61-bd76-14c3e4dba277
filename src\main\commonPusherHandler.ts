// @ts-nocheck
import { notificationHandler } from './notification';

import config from './config';

const { commonAppEventsofPusher } = config

let mainWindow, channelWindow;
const { privateEvents } = commonAppEventsofPusher;

export const commonPusherHandlerInit = (_mainWindow, _channelWindow) => {
    mainWindow = _mainWindow;
    channelWindow = _channelWindow;
}

export const userForceLogout = (channel) => {
    channel.bind( privateEvents.userForceLogout, (data) => {
        console.log(`Recieved Force logout event`);
        mainWindow?.webContents.send(channelWindow.forceLogout);
    });
}

export const referenceProductChangeHandler = (channel, channelEvent) => {
    channelEvent.forEach($event => {
        channel.bind( $event, (data) => {
        // console.log('Received notification:', data);
            notificationHandler(data.notification);
            mainWindow?.webContents.send(channelWindow.productReferenceChanged);
        });
    });
} 