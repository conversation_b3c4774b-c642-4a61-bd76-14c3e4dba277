// @ts-nocheck
import { useState, useEffect, useContext } from 'react';
import { useLocation, useNavigate } from 'react-router';
import {UserContext} from '../UserContext';
import axios from 'axios';
import { fileType, routes } from '../../common';
import { useHeightListener } from '../hooks/useHeightListener';
import { useStore } from '../helper/store';
import { Auth } from 'aws-amplify';
import { createSocket, getSocketConnection, resetConnection } from '../helper/socket';
import { downloadFiles } from '../helper';
import { Dialog } from '@mui/material';
import { useIonLoading } from '@ionic/react';

function Tnc() {
    const userContext = useContext(UserContext);
    const navigate = useNavigate();
    const [tandCData, setTandCData] = useState({});
    const [, setTncBottom] = useState(false);
    const [tnc, setTnc] = useState('');
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const [openErrorDialog, setOpenErrorDialog] = useState(false);
    const userId = userContext.user;

    const ref = useHeightListener();
    const location = useLocation();
    const { isViewMode, navigateTo } = location.state;
    const backNavigation = useStore(state => state.backNavigation);
    const setShowLoader = useStore(state => state.setShowLoader);
    const setResetHeaderConfig = useStore(state => state.setResetHeaderConfig);
    const setDisableBryzosNavigation = useStore(state => state.setDisableBryzosNavigation);
    const [present, dismiss] = useIonLoading();

    useEffect(() => {
        if (userId.data === null) {
            navigate(routes.loginPage);
        }
        const referenceData = userId.referenceData;
        referenceData.ref_bryzos_terms_conditions.forEach(termsAndCondition => {
            if(userId.data.type === termsAndCondition.type){
                setTandCData(termsAndCondition)
            }
        });
        
    }, [userId]);
    const handleLogout = async () => {
      if (userId.data && !isLoggingOut) {
        setIsLoggingOut(true);
        const userEmail = {
          data: {
            email_id: userId.data.email_id,
          },
        };

        axios
          .post(import.meta.env.VITE_API_SERVICE + "/user/logout", userEmail)
          .finally(async () => {
            try {
              await Auth.signOut();
            } catch (error) {
              console.log("error signing out: ", error);
            }
            navigate(routes.loginPage);
            setIsLoggingOut(false);
            userContext.setUser({ data: null });
            console.log("header-----");
            if (getSocketConnection()) {
                console.log("TNC");
              createSocket()?.disconnect();
            }
            for (let i = 0; i < sessionStorage.length; i++) {
              const key = sessionStorage.key(i);
              if (key !== "isSticky" && key !== "localStorageStickyItemKey") {
                sessionStorage.removeItem(key);
              }
            }
            document.cookie.split(";").forEach((c) => {
              document.cookie = c
                .replace(/^ +/, "")
                .replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);
            });
            resetConnection();
            setResetHeaderConfig(true);
            setDisableBryzosNavigation(true);
          });
      }
    };
    const handleSubmitTnc = () => {
        const payload = {
            data: {
                "bryzos_terms_condtion_id": tandCData.id,
                "terms_conditions_version": tandCData.terms_conditions_version,
                "email_id": userId.data.email_id
            }
        };
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/saveTermsCondition', payload).then(response => {
            if (response.data !== null) {
                setDisableBryzosNavigation(false);
                navigate(routes.homePage);
            }
        })
            .catch(error => { console.error(error) });
    }
    const FetchHtml = async () => {
        const response = await fetch(tandCData.cloudfront_url);
        return await response.text(); // Returns it as Promise
    };
    const SetHtml = async () => {
        await FetchHtml().then((text) => {
            setTnc(text);
            setShowLoader(false);
        });
    };
    useEffect(() => {
        if(Object.keys(tandCData).length !== 0){
            SetHtml(true)
        }
    },[tandCData]);

    const handleScroll = (e) => {
        const tncWorking = e.target.scrollHeight - e.target.scrollTop
        const bottom =
            Math.floor(tncWorking) === e.target.clientHeight;
        if (bottom) {
            setTncBottom(bottom);
        }
    };
    
    const downloadReports = (fileUrl, fileName, fileType) => {
        present('Downloading...');
        const showError = downloadFiles(fileUrl, fileName, fileType)
        showError.then(res => {
            dismiss();
            if (res) {
                setOpenErrorDialog(false);
            } else {
                setOpenErrorDialog(true);
            }
        })
        .catch(e => {
            dismiss();
        });

    }

    return (
        <>
          {/* <div className='bgImg' ref={ref}>
          <div className='headerPanel commonHeader'>
                <Header />
            </div> */}
            <div className='widgetBody tncBg' ref={ref}>
                <div className='termsAndConditions'>
                  
                        <div className='TncContainer'>
                            <div className='termsAndConditions' onScroll={handleScroll}
                            >
                                <div>
                                    <div className='tncHead'>
                                        <div>Bryzos Instant Pricing Desktop Widget Terms of Use</div>
                                    </div>
                                    <p className='effectiveDate'>Effective as of: March 8, 2023</p>
                                    <div  dangerouslySetInnerHTML={{ __html: tnc }}></div>

                                </div>
                            </div>
                        </div>

                </div>
                <div className='tncButtons'>
                    {isViewMode ?
                        <>
                            <button onClick={()=>navigate(backNavigation)} className='disagreeBtn'>Back</button>
                            <div onClick={() => { downloadReports(import.meta.env.VITE_FILE_URL_DOWNLOAD_TNC_PDF, 'Tnc', fileType.pdf) }} className='downloadTnCBtn'> Click here to download T&C </div>
                        </>
                        :
                        <>
                        <button onClick={handleLogout} className='disagreeBtn'>Disagree</button>
                        {/* <button className={`agreeBtn ${tncBottom ? 'agreeBtnEnable' : ''}`} onClick={handleSubmitTnc} disabled={!tncBottom}>Agree</button> */}
                        <div onClick={() => { downloadReports(import.meta.env.VITE_FILE_URL_DOWNLOAD_TNC_PDF, 'Tnc', fileType.pdf) }} className='downloadTnCBtn'> Click here to download T&C </div>
                        <button className='agreeBtn1' onClick={handleSubmitTnc}>Agree</button>
                        </>
                    }

                    
                    
                </div>
            </div>
            <>
                <Dialog
                    open={openErrorDialog}
                    onClose={(event) => setOpenErrorDialog(false)}
                    transitionDuration={200}
                    hideBackdrop
                    classes={{
                        root: 'ErrorDialog',
                        paper: 'dialogContent'
                    }}

                >
                    <p>No data found. Please try again in sometime</p>
                    <button className={'submitBtn'} onClick={(event) => setOpenErrorDialog(false)}>Ok</button>
                </Dialog>
            </>
          {/* </div> */}
          
        </>
    );
}
export default Tnc;
