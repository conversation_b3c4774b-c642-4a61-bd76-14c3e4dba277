import { getValidSearchData, searchProducts } from "@bryzos/giss-ui-library"
import styles from './SearchProductList.module.scss';
import DisplayProductDescription from "../DisplayProductDescription";
import clsx from "clsx";

const SearchProductList = (props: any) => {
    return (
        <div className={`searchPanelMob ${(props.searchString === '' && props.selectedProducts.length === 0 || props.showListPanel) ? '' : 'bdrRadiusNone'}`}>
            <div className={props.lineNo ? styles.searchInstantPrice : styles.searchList}>
                {props.lineNo && <span className={styles.lineNoLbl}>Line {props.lineNo}</span>}
                <span className={styles.searchInput} >
                    <input
                        className={styles.searchInputElemnt}
                        type='search'
                        name='search'
                        value={props.searchString}
                        onChange={e => { props.productSearchHandler(e) }}
                        placeholder={props.selectedProducts.length === 0 ? 'Search by Product for Instant Pricing' : 'Search Another Product'}
                        ref={props.searchProductInputRef} 
                    />
                </span>
                <span>
                {(props.lineNo || props.searchString !== '') &&
                    <button className={styles.cancelBtn} onClick={()=>{
                        props.setHideHeader && props.setHideHeader(false)
                        props.setSearchString("")
                    }}>
                        Cancel
                    </button>
                    }
                </span>
             
            </div>
            <div className={clsx('listBody', props.lineNo ? 'searchInstantPriceList' : '')}>
                <div className='ulBody'>
                    {
                        searchProducts(props.products, getValidSearchData(props.searchString), props.searchString, props.userPartData, props.excludeSafeProduct).map((product) => (

                            <div className={`liBody `}
                                key={product.id}
                                onClick={() => props.selectProduct(product)}>
                                <DisplayProductDescription description={product?.UI_Description}/>
                            </div>

                        ))
                    }
                </div>
            </div>

        </div>
    )
}

export default SearchProductList;