// @ts-nocheck
import { <PERSON>P<PERSON>, IonContent, useIonRouter, useIonViewWillEnter, useIonViewDidLeave, useIonViewWillLeave, useIonLoading } from '@ionic/react';
import styles from './FundingSetting.module.scss';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { paymentSettingSchema } from '../SettingSchema';
import { useEffect, useRef, useState } from 'react';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import clsx from "clsx";
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import useSellerSettingStore, { FundingSettings as FundingSettingModel } from '../SellerSettingStore';
import { mobileDiaglogConst, sellerSettingConst, userRole } from '../../../../library/common';
import useDialogStore from '../../../../components/Dialog/DialogStore';
import TrueVaultClient from 'truevault';
import axios from 'axios';
import { getTruevaultData, saveUserSetting } from '../../../../library/helper';
import Cass from '../Cass';

function FundingSetting() {
    const {
        register,
        handleSubmit,
        getValues,
        clearErrors,
        setValue,
        watch,
        setError,
        reset,
        control,
        formState: { errors, isDirty, dirtyFields,isValid } } = useForm({
            resolver: yupResolver(paymentSettingSchema)
        });

        const { ref: ref1, ...rest1 } = register("bankName1");
        const { ref, ...rest } = register("bankName");

    const [isSaveDisable, setIsSaveDisable] = useState(true);
    const {userData, setShowLoader ,referenceData} = useGlobalStore();
    const { fundingSettingsInfo, setFundingSettingsInfo, sellerSetting, setSellerSettingInfo } = useSellerSettingStore();
    const router = useIonRouter();
    const [present, dismiss] = useIonLoading();
    const [states, setStates] = useState([]);
    const childRef = useRef();
    const { showCommonDialog, resetDialogStore } = useDialogStore();

    const bankName1Ref = useRef<HTMLIonInputElement>(null);
    const bankNameRef = useRef<HTMLIonInputElement>(null);


    useIonViewWillEnter(() => {
        if (referenceData) {
            setStates(referenceData.ref_states)
                referenceData.ref_pgpm_mapping.forEach(paymentMethod => {
                    if (paymentMethod.user_type === userRole.sellerUser.toLowerCase() && paymentMethod.payment_method === 'ACH') {
                        setValue('achPgpmMappingId', String(paymentMethod.id))
                        if (fundingSettingsInfo && fundingSettingsInfo.pgpm_mapping_id === paymentMethod.id) {
                            setValue('fundingRadioBtn', String(fundingSettingsInfo.pgpm_mapping_id))
                            setValue('bankName1', fundingSettingsInfo.bank_name);
                            setValue('routingNo', fundingSettingsInfo.routing_number);
                            setValue('accountNo', fundingSettingsInfo.account_number);
                            setValue('referenceDocumentId', fundingSettingsInfo.reference_document_id);
                        }
                        return;
                    }
                    if (paymentMethod.user_type === userRole.sellerUser.toLowerCase() && paymentMethod.payment_method === 'WIRE') {
                        setValue('wirePgpmMappingId', String(paymentMethod.id))
                        if (fundingSettingsInfo && fundingSettingsInfo.pgpm_mapping_id === paymentMethod.id) {
                            setValue('fundingRadioBtn', String(fundingSettingsInfo.pgpm_mapping_id))
                            setValue('bankName', fundingSettingsInfo.bank_name);
                            setValue('routingNumber', fundingSettingsInfo.routing_number);
                            setValue('accountNumber', fundingSettingsInfo.account_number);
                            setValue('referenceDocumentId', fundingSettingsInfo.reference_document_id);
                        }
                        return;
                    }
                })
            clearErrors();
        }
    }, [referenceData,fundingSettingsInfo])

    useIonViewWillLeave(() => {
        setIsSaveDisable(true);
        reset();
        resetDialogStore();
    }, [])

    useEffect(() => {
        if (isDirty)
            setIsSaveDisable(false);

            // const handleBackButton = (ev: BackButtonEvent) => {
            //     ev.detail.register(10, async () => {
            //         backToSetting();
            //     });
            // };
    
            // document.addEventListener('ionBackButton', handleBackButton);
            // return () => {
            //     document.removeEventListener('ionBackButton', handleBackButton);
            // };
    }, [isDirty])

    const wireCheckBoxChagneHandler = () => {
        const t = setTimeout(() => {
            bankNameRef.current?.focus();
        }, 200);

        setValue('fundingRadioBtn', getValues('wirePgpmMappingId'))
        setValue('bankName1', '');
        setValue('routingNo', '');
        setValue('accountNo', '')
        clearErrors()

        return () => {
            clearTimeout(t);
        }
    }

    const achCreditCheckBoxChagneHandler = () => {
        const t = setTimeout(() => {
            bankName1Ref.current?.focus();
        }, 200);

        setValue('fundingRadioBtn', getValues('achPgpmMappingId'))
        setValue('bankName', '');
        setValue('routingNumber', '');
        setValue('accountNumber', '')
        clearErrors()

        return () => {
            clearTimeout(t);
        }
    }
    const fundingSettingChanged = (field) => {
        const bankName = getValues("bankName");
        const routingNumber = getValues("routingNumber");
        const accountNumber = getValues("accountNumber");
        const bankName1 = getValues("bankName1");
        const routingNo = getValues("routingNo");
        const accountNo = getValues("accountNo");
        clearErrors()

        if (field === "bankName") {
            if (routingNumber?.includes("x")) {
                setValue('routingNumber', "");
            }
            if (accountNumber?.includes("x")) {
                setValue('accountNumber', "");
            }

        } else if (field === "routingNumber") {
            if (bankName?.includes("x")) {
                setValue('bankName', "");
            }
            if (accountNumber?.includes("x")) {
                setValue('accountNumber', "");
            }
        } else if (field === "accountNumber") {
            if (bankName?.includes("x")) {
                setValue('bankName', "");
            }
            if (routingNumber?.includes("x")) {
                setValue('routingNumber', "");
            }
        } else if (field === "bankName1") {
            if (routingNo?.includes("x")) {
                setValue('routingNo', "");
            }
            if (accountNo?.includes("x")) {
                setValue('accountNo', "");
            }
        }
        else if (field === "routingNo") {
            if (bankName1?.includes("x")) {
                setValue('bankName1', "");
            }
            if (accountNo?.includes("x")) {
                setValue('accountNo', "");
            }
        }
        else if (field === "accountNo") {
            if (bankName1?.includes("x")) {
                setValue('bankName1', "");
            }
            if (routingNo?.includes("x")) {
                setValue('routingNo', "");
            }
        }
    }

    const handleValidation = (submitForm: boolean = false) => {
        const fundingRadioBtn = getValues("fundingRadioBtn");
        const wireMappingid = getValues("wirePgpmMappingId");
        const achMappingId = getValues("achPgpmMappingId");
        if (fundingRadioBtn === wireMappingid) {
            if (!getValues("bankName")) setError("bankName", { message: "Wire is not valid" });
            if (!getValues("routingNumber") || isNaN(getValues("routingNumber")) || getValues("routingNumber").length!==9) setError("routingNumber", { message: "Wire is not valid" });
            if (!getValues("accountNumber") || isNaN(getValues("accountNumber"))) setError("accountNumber", { message: "Wire is not valid" });
        } else if (fundingRadioBtn === achMappingId) {
            if (!getValues("bankName1")) setError("bankName1", { message: 'ACH Credit is not valid' });
            if (!getValues("routingNo") || isNaN(getValues("routingNo")) || getValues("routingNo").length!==9) setError("routingNo", { message: 'ACH Credit is not valid' });
            if (!getValues("accountNo") || isNaN(getValues("accountNo"))) setError("accountNo", { message: 'ACH Credit is not valid' });
        }
        if (submitForm) {
            handleFormSubmit()
        }
    }

    const handleFormSubmit = () => {
        console.log("CLICKED", Object.keys(errors))
        if (Object.keys(errors).length === 0) {
            handleSubmit(onSubmit)();
        } else {
            console.log("RETURN")
            return;
        }
    }


    const onSubmit = async (data) => {
        setShowLoader(true)

        if (referenceData?.ref_general_settings.length) {
            let obj = referenceData.ref_general_settings.find((obj) => obj.name === "CASS_MASTER_DATA_CREATE");
            if (obj?.value === "true") {
                try {
                    const result= await childRef.current.startCassCreateion();
                    console.log(result)
                    console.log("setup sucess")
                } catch (error) {

                }
            }
        }
        if (data.fundingRadioBtn === data.achPgpmMappingId) {

            if (dirtyFields.bankName1 || dirtyFields.routingNo || dirtyFields.accountNo) {

                if (isNaN(data.routingNo) || isNaN(data.accountNo)) {
                    const key = isNaN(data.routingNo) ? 'routingNo' : 'accountNo'
                    setError(key, { message: 'ACH Credit is not valid' }, { shouldFocus: true });
                    setShowLoader(false)
                    return
                }
                const document = {
                    "company_name": data.companyName,
                    "user_id": userData.data.id,
                    "bank_name": data.bankName1,
                    "routing_number":  data.routingNo,
                    "account_number": data.accountNo,
                    "pgpm_mapping_id": data.achPgpmMappingId
                };
                const truevaultUrl = import.meta.env.VITE_API_SERVICE + '/user/getAccessToken';
                const vaultId = import.meta.env.VITE_TRUE_VAULT_ID_SELLER_VAULT_ID;
                getTruevaultData(truevaultUrl, document, vaultId ).then(documentIdFromTruevault => {
                    const achFundingSetting = {};
                    const convertedRoutingNo = data.routingNo.slice(-4).padStart(data.routingNo.length, 'x');
                    const convertedAccountNO = data.accountNo.slice(-4).padStart(data.accountNo.length, 'x');
                    achFundingSetting.bank_name = data.bankName1;
                    achFundingSetting.routing_number = convertedRoutingNo;
                    achFundingSetting.account_number = convertedAccountNO;
                    achFundingSetting.reference_document_id = documentIdFromTruevault;
                    achFundingSetting.pgpm_mapping_id = parseInt(data.achPgpmMappingId);
                    submitData(achFundingSetting);
                })
                .catch(e => {
                    setShowLoader(false)
                });
            } else {

                const achFundingSetting = {};
                achFundingSetting.bank_name = data.bankName1;
                achFundingSetting.routing_number = data.routingNo;
                achFundingSetting.account_number = data.accountNo;
                achFundingSetting.reference_document_id = referenceDocumentId;
                achFundingSetting.pgpm_mapping_id = parseInt(data.achPgpmMappingId);
                submitData(achFundingSetting);
            }


        } else if (data.fundingRadioBtn === data.wirePgpmMappingId) {

            if (dirtyFields.bankName || dirtyFields.routingNumber || dirtyFields.accountNumber) {

                if (isNaN(data.routingNumber) || isNaN(data.accountNumber)) {
                    const key = isNaN(data.routingNumber) ? 'routingNumber' : 'accountNumber'
                    setError(key, { message: 'Wire is not valid' }, { shouldFocus: true });
                    setShowLoader(false)
                    return
                }
                const document = {
                    "company_name": data.companyName,
                    "user_id": userData.data.id,
                    "bank_name": data.bankName,
                    "routing_number": data.routingNumber,
                    "account_number": data.accountNumber,
                    "pgpm_mapping_id": data.wirePgpmMappingId
                };
                const truevaultUrl = import.meta.env.VITE_API_SERVICE + '/user/getAccessToken';
                const vaultId = import.meta.env.VITE_TRUE_VAULT_ID_SELLER_VAULT_ID;
                getTruevaultData(truevaultUrl, document, vaultId ).then(documentIdFromTruevault => {
                    const wireFundingSetting = {};
                    const convertedRoutingNo = data.routingNumber.slice(-4).padStart(data.routingNumber.length, 'x');
                    const convertedAccountNO = data.accountNumber.slice(-4).padStart(data.accountNumber.length, 'x');
                    wireFundingSetting.bank_name = data.bankName;
                    wireFundingSetting.routing_number = convertedRoutingNo;
                    wireFundingSetting.account_number = convertedAccountNO;
                    wireFundingSetting.reference_document_id = documentIdFromTruevault;
                    wireFundingSetting.pgpm_mapping_id = parseInt(data.wirePgpmMappingId);
                    submitData(wireFundingSetting);
                })
                .catch(e => {
                    setShowLoader(false)
                });
            } else {
                const wireFundingSetting = {};
                wireFundingSetting.bank_name = data.bankName;
                wireFundingSetting.routing_number = data.routingNumber;
                wireFundingSetting.account_number = data.accountNumber;
                wireFundingSetting.reference_document_id = referenceDocumentId;
                wireFundingSetting.pgpm_mapping_id = parseInt(data.wirePgpmMappingId);
                submitData(wireFundingSetting);
            }


        }


    }

    const submitData = async (payload) => {
        const detail: FundingSettingModel = payload;
        try {
            await saveUserSetting(sellerSettingConst.apiRoutesForSave.fundingInfo, detail, userData);
            setFundingSettingsInfo(detail);
            router.push('/seller-setting',{animate:true,direction:'forward'});
            setShowLoader(false)
        } catch (err) {
            console.error(err)
            setShowLoader(false)
        }
    }

    const routeBackToSetting = () => {
        router.push('/seller-setting',{animate:true,direction:'forward'});
        resetDialogStore();
    }

    const backToSetting = () => {
        if (isDirty)
            showCommonDialog(mobileDiaglogConst.unsavedChangesMsg, [{ name: mobileDiaglogConst.stay, action: resetDialogStore }, { name: mobileDiaglogConst.goBackToSetting, action: routeBackToSetting }]);
        else
            routeBackToSetting();
    }


    return (
        <IonPage>
            <IonContent>
                <>
                    <div className={styles.profile}>
                        <h2 className={styles.heading}><BackBtnArrowIcon onClick={backToSetting} /> <span>Funding Settings</span> </h2>
                        <div className={styles.settingsContent}>
                            <div>
                                <span>
                                    <label className={styles.containerRadio}>
                                        <input type='radio' onClick={wireCheckBoxChagneHandler}
                                            value={getValues('wirePgpmMappingId')}
                                            checked={getValues('fundingRadioBtn') === getValues('wirePgpmMappingId') ?? false}
                                            {...register('fundingRadioBtn')}
                                        />
                                        <span className={styles.checkmark}></span>
                                        <span>Wire</span>
                                    </label>
                                </span>
                                <span>
                                    <div className={styles.profileComponent}>
                                        <label>Bank Name</label>
                                        <input className={clsx(styles.inputField, errors?.bankName?.message && styles.errorMsg )} {...rest} type='text' onChange={(e) => {
                                            rest.onChange(e)
                                            setValue('fundingRadioBtn', getValues('wirePgpmMappingId'))
                                            fundingSettingChanged("bankName");
                                            handleValidation();
                                        }}
                                            ref={(e) => { ref(e); bankNameRef.current = e; }}
                                            onBlur={(e) => {
                                                e.target.value = e.target.value.trim();
                                                rest.onBlur(e)
                                            }}
                                            placeholder="Enter Bank Name"
                                            disabled={watch('fundingRadioBtn') !== getValues('wirePgpmMappingId')}
                                        />
                                    </div>
                                    <div className={styles.profileComponent}>
                                        <label>Routing No.</label>
                                        <input className={clsx(styles.inputField, errors?.routingNumber?.message && styles.errorMsg )} maxLength={9} {...register("routingNumber")} onChange={(e) => {
                                            e.target.value = e.target.value.replace(/\D/g, '');
                                            register("routingNumber").onChange(e)
                                            setValue('fundingRadioBtn', getValues('wirePgpmMappingId'))
                                            fundingSettingChanged("routingNumber");
                                            handleValidation();
                                        }} type='tel' placeholder='xxxxxxxxx'
                                            onBlur={(e) => {
                                                e.target.value = e.target.value.trim();
                                                register("routingNumber").onBlur(e)
                                            }}
                                            disabled={watch('fundingRadioBtn') !== getValues('wirePgpmMappingId')}
                                        />
                                    </div>
                                    <div className={styles.profileComponent}>
                                        <label>Account No.</label>
                                        <input className={clsx(styles.inputField, errors?.accountNumber?.message && styles.errorMsg )} {...register("accountNumber")} onChange={(e) => {
                                            e.target.value = e.target.value.replace(/\D/g, '');
                                            register("accountNumber").onChange(e)
                                            setValue('fundingRadioBtn', getValues('wirePgpmMappingId'))
                                            fundingSettingChanged("accountNumber");
                                            handleValidation();
                                        }} type='tel' placeholder='xxxxxxxxxx'
                                            onBlur={(e) => {
                                                e.target.value = e.target.value.trim();
                                                register("accountNumber").onBlur(e)
                                            }}
                                            disabled={watch('fundingRadioBtn') !== getValues('wirePgpmMappingId')}
                                        />
                                    </div>

                                </span>

                            </div>
                            <div>
                                <span>
                                   <label className={styles.containerRadio}>
                                        <input type='radio'
                                            value={getValues('achPgpmMappingId')}
                                            checked={getValues('fundingRadioBtn') === getValues('achPgpmMappingId') ?? false}
                                            {...register('fundingRadioBtn')}
                                            onClick={achCreditCheckBoxChagneHandler}
                                        />
                                        <span className={styles.checkmark}></span>
                                        <span>ACH Credit</span>
                                    </label>
                                </span>
                                <span>
                                    <div className={styles.profileComponent}>
                                        <label>Bank Name</label>
                                        <input className={clsx(styles.inputField, errors?.bankName1?.message && styles.errorMsg )} {...rest1} type='text' onChange={(e) => {
                                            rest1.onChange(e)
                                            setValue('fundingRadioBtn', getValues('achPgpmMappingId'))
                                            fundingSettingChanged("bankName1")
                                            handleValidation();
                                        }} placeholder='Enter bank name'
                                            onBlur={(e) => {
                                                e.target.value = e.target.value.trim();
                                                rest1.onChange(e)
                                            }}
                                            ref={(e) => { ref1(e); bankName1Ref.current = e; }}
                                            disabled={watch('fundingRadioBtn') !== getValues('achPgpmMappingId')}
                                        />
                                    </div>
                                    <div className={styles.profileComponent}>
                                        <label>Routing No.</label>
                                        <input className={clsx(styles.inputField, errors?.routingNo?.message && styles.errorMsg )} maxLength={9} {...register("routingNo")} type='tel' onChange={(e) => {
                                            e.target.value = e.target.value.replace(/\D/g, '');
                                            register("routingNo").onChange(e)
                                            setValue('fundingRadioBtn', getValues('achPgpmMappingId'))
                                            fundingSettingChanged("routingNo")
                                            handleValidation();
                                        }} placeholder='xxxxxxxxx'
                                            onBlur={(e) => {
                                                e.target.value = e.target.value.trim();
                                                register("routingNo").onBlur(e)
                                            }}
                                            disabled={watch('fundingRadioBtn') !== getValues('achPgpmMappingId')}
                                        />
                                    </div>
                                    <div className={styles.profileComponent}>
                                        <label>Account No.</label>
                                        <input className={clsx(styles.inputField, errors?.accountNo?.message && styles.errorMsg )} {...register("accountNo")} type='tel' onChange={(e) => {
                                            e.target.value = e.target.value.replace(/\D/g, '');
                                            register("accountNo").onChange(e)
                                            setValue('fundingRadioBtn', getValues('achPgpmMappingId'))
                                            fundingSettingChanged("accountNo")
                                            handleValidation();
                                        }} placeholder='xxxxxxxxxx'
                                            onBlur={(e) => {
                                                e.target.value = e.target.value.trim();
                                                register("accountNo").onBlur(e)
                                            }}
                                            disabled={watch('fundingRadioBtn') !== getValues('achPgpmMappingId')}
                                        />
                                    </div>
                                </span>
                            </div>
                        </div>
                        <div className={styles.btnSection}>
                            <button className={styles.saveBtn} onClick={() => handleValidation(true)} disabled={isSaveDisable}>Save</button>
                        </div>
                    </div>
                </>
                <Cass ref={childRef} getValues={getValues} sellerInfo={sellerSetting} referenceData={referenceData} states={states} present={()=>setShowLoader(true)} dismiss={()=>setShowLoader(false)} />
            </IonContent>
        </IonPage>
    );
}
export default FundingSetting;
