// @ts3-nocheck 
import Pusher from "pusher-js";
import config from '../../../main/config';
import { showNotification, setNotificationConstants, markAsReadNotification, notificationEventsHandler } from './notification';
import { userForceLogout, referenceProductChangeHandler } from './commonPusherHandler';
import { Auth } from 'aws-amplify';
import { addAuth, addInterest, initPushNoti } from "./pushNoti";

const { pusherNotification, commonAppEventsofPusher } = config

export const channelWindow = {

    pusher: 'pusherNotifier',
    logout: 'logout',
    showNotification: 'showNotification',
    markAsReadNotification: 'markAsReadNotification',
    resetAppCacheAndStorage: 'resetAppCacheAndStorage',
    reloadWindow: 'reload-window',
    badgeCountHandler: 'badge-count',
    isMarkAsReadNotification: 'marked-as-read-notification',

    getAccessToken: 'get-refreshed-token',
    refreshPrivateChannel: 'refresh-private-pusher'
}

let pusher: null | Pusher = null;
let channelId: null | string = null;
let userId: null | string = null;

let pusherHeader = {
    accessToken: '',
    emailId: ''
};
let isPrivateChannelDisconnected = false;
const [publicChannel, privateChannel, buyerChannel, sellerChannel] = pusherNotification.channels;
const { privateEvents, publicEvents, buyerEvents, sellerEvents } = pusherNotification.channelEvents;

function pusherInit() {
    
    // ipcMain.on(channelWindow.showNotification, (event, notificationList) => {
    //    if (notificationList && Array.isArray(notificationList)) {
    //         notificationList.forEach(notificationSchema => showNotification(notificationSchema.notification));
    //         notificationList = notificationList.map(notificationSchema => notificationSchema.notification);
    //     }
    //     markAsReadNotification(notificationList);
    // });
   
    
    // ipcMain.on(channelWindow.logout, (event, data) => {
    //     console.log("logout");
    //     setNotificationConstants(null);
    //     unsubscribeToChannel();

    // });

}
// init
export const createPusherChannel = (user: { id: string | null; pusher_id: string; type: string; }, pusherId: string, emailId: string, accessToken: string) => {
    console.log("pusher", pusher === null);
    initPushNoti()

    if (pusher === null) {
        userId = user.id;
        const pusherId = user.pusher_id;

        const env = import.meta.env.VITE_ENVIRONMENT
        const prefix = env === 'prod' ? '' : env === 'demo' ? '-demo' : '-staging'
        const channelName = `private-channel${prefix}-${pusherId}`

        pusherHeader.accessToken = accessToken;
        pusherHeader.emailId = emailId;
        setNotificationConstants(userId!);
        console.log("Pusher Notifier init for user = " + channelName);
        // pusher = new Pusher(pusherNotification.pusher.key, {
        //     cluster: pusherNotification.pusher.cluster,
        //     channelAuthorization: {
        //         endpoint: `${pusherNotification.authUrl}/notification/auth`,
        //         headers: pusherHeader,
        //         transport: 'ajax'
        //     },
        // });
        addAuth(channelName, `${pusherNotification.authUrl}/notification/pusher/beams-auth`, pusherHeader)

        subscribeToGlobalChannel();
        // subscribeToPrivateChannel(pusherId);
        if (user.type === 'SELLER') subscribeToSellerChannel();
        if (user.type === 'BUYER') subscribeToBuyerChannel();
    }
    else {
        if (isPrivateChannelDisconnected) {
            console.log("Creating Fresh private channel", pusherId);
            // reconnectToPrivateChannel(accessToken);
        }
    }
}

const reconnectToPrivateChannel = (accessToken: string) => {
    pusher?.unsubscribe(`${privateChannel}${channelId}`);
    pusherHeader.accessToken = accessToken;
    subscribeToPrivateChannel(channelId!);
}

const subscribeToGlobalChannel = () => {
    console.log("Pusher Notifier init");
   // const channel = pusher?.subscribe(publicChannel);
//    console.log("hjfjfhjf",publicChannel)
   addInterest(publicChannel)
   // if(!channel) return
   // channel.bind("pusher:subscription_succeeded", () => { console.log("Subscribed to public channel :") });
   // channel.bind("pusher:subscription_error", (error: string) => { console.log("Error public channel :" + error) });
    // console.log("publicEvents",publicEvents);
   // notificationEventsHandler(channel, publicEvents);
}
const subscribeToBuyerChannel = () => {
    // console.log("Pusher Notifier init");
    // const channel = pusher?.subscribe(buyerChannel);
    addInterest(buyerChannel);
    // if(!channel) return
    // channel.bind("pusher:subscription_succeeded", () => { console.log("Subscribed to buyer channel :") });
    // channel.bind("pusher:subscription_error", (error: string) => { console.log("Error buyer channel :" + error) });
    // // console.log("buyerEvents",buyerEvents);
    // notificationEventsHandler(channel, buyerEvents);
    // referenceProductChangeHandler(channel, commonAppEventsofPusher.buyerEvents.referenceProductChanges);
}
const subscribeToSellerChannel = () => {
    // console.log("Pusher Notifier init");
    // const channel = pusher?.subscribe(sellerChannel);
    addInterest(sellerChannel);
//     if(!channel) return
//     channel.bind("pusher:subscription_succeeded", () => { console.log("Subscribed to seller channel :") });
//     channel.bind("pusher:subscription_error", (error: string) => { console.log("Error seller channel :" + error) });
//     // console.log("sellerEvents",sellerEvents);
//     notificationEventsHandler(channel, sellerEvents);
//     referenceProductChangeHandler(channel, commonAppEventsofPusher.sellerEvents.referenceProductChanges);
}

const subscribeToPrivateChannel = (pusherId: string) => {
    channelId = pusherId;
    console.log("Pusher Private channel init = " + `${privateChannel}${channelId}`);
    const channel = pusher?.subscribe(`${privateChannel}${channelId}`);
    if(!channel) return
    channel.bind("pusher:subscription_succeeded", () => {
        console.log("Subscribed to private channel :");
        isPrivateChannelDisconnected = false;
    });
    channel.bind("pusher:subscription_error", async (error:string) => {
        console.log("Error private channel :", error);
        isPrivateChannelDisconnected = true;
        // setTimeout(() => {
        //     mainWindow?.webContents.send(channelWindow.getAccessToken, pusherId);
        // }, 5000);
        const user = await Auth.currentSession();
        const accessToken = user.getAccessToken();
        const jwt = accessToken.getJwtToken();
        if (pusher && isPrivateChannelDisconnected) {
            reconnectToPrivateChannel(jwt);
        }
    });
    notificationEventsHandler(channel, privateEvents);
    userForceLogout(channel);
}

const unsubscribeToChannel = () => {
    if (pusher) {
        pusher.unsubscribe(publicChannel);
        pusher.unsubscribe(`${privateChannel}${channelId}`);
        pusher.unsubscribe(buyerChannel);
        pusher.unsubscribe(sellerChannel);
    }
    pusher = null;
    channelId = null;
    userId = null;
    pusherHeader = {
        accessToken: '',
        emailId: ''
    };
    isPrivateChannelDisconnected = false;
}

 