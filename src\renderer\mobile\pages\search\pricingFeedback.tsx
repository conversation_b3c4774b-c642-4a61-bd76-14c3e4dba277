import { ReactComponent as LowPriceIcon } from '../../../assets/mobile-images/icon_down_arrow.svg';
import { ReactComponent as HighPriceIcon } from '../../../assets/mobile-images/icon_up_arrow.svg';
import { ReactComponent as CheckDIcon } from '../../../assets/images/unCheck.svg';
import { commomKeys, snackbarSeverityType } from '../../library/common';
import { useGlobalStore, handlePriceRatingChange } from '@bryzos/giss-ui-library';
import axios from 'axios';
import { useState } from 'react';
import useSnackbarStore from '../../library/component/Snackbar/snackbarStore';
import { ProductPricingModel, ProductQtyType } from '../../types/Search';

type PriceFeedback = {
  product: ProductPricingModel,
  isUserChangedSelectedOption: boolean,
  selectedOption: ProductQtyType,
  index: number
};

const PricingFeedback: React.FC<PriceFeedback> = ({product, isUserChangedSelectedOption, selectedOption, index}) => {
    const { userData, setUserData } = useGlobalStore();
    const {showToastSnackbar, resetSnackbarStore}: any = useSnackbarStore();
    const initialFeedBackData = userData?.feedBackData ?? {} ;
    const [feedbackData, setFeedBackData] = useState(initialFeedBackData);

    const onChangePriceRateFeedback = (rating:string) => {
      handlePriceRatingChange(product, selectedOption, rating, isUserChangedSelectedOption,feedbackData, setFeedBackData, showOnError);
    };

      const showOnError = () => {
        showToastSnackbar(commomKeys.errorContent, snackbarSeverityType.alert, null, resetSnackbarStore, null, 5000);
      }

    return (
        <div className='priceFeedback'>
          {(!product.is_safe_product_code) &&
            <>
              <label className={feedbackData[product.id] === 'LOW' ? 'lowBtn selectedBtn' : 'lowBtn'}>
                <input
                  type="radio"
                  value="LOW"
                  checked={feedbackData[product.id] === 'LOW'}
                  onChange={(event) => onChangePriceRateFeedback(event.target.value)}
                />
                <LowPriceIcon /><span>Pricing low</span>

              </label>
              <label className={feedbackData[product.id] === 'GOOD' ? 'goodBtn CheckBtn selectedBtn' : 'CheckBtn'}>
                <input
                  type="radio"
                  value="GOOD"
                  checked={feedbackData[product.id] === 'GOOD'}
                  onChange={(event) => onChangePriceRateFeedback(event.target.value)}
                />
                <span className='checkIcon'><CheckDIcon /></span><span>Pricing is good</span>
              </label>
              <label className={feedbackData[product.id] === 'HIGH' ? 'hightBtn selectedBtn' : 'hightBtn'}>
                <input
                  type="radio"
                  value="HIGH"
                  checked={feedbackData[product.id] === 'HIGH'}
                  onChange={(event) => onChangePriceRateFeedback(event.target.value)}
                />
                <HighPriceIcon /><span>Pricing high</span>
              </label>
            </>
          }
        </div>
    )
}
export default PricingFeedback;