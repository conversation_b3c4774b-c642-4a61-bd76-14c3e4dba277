import {App as NativeApp} from '@capacitor/app'; 
import { useEffect, useState } from 'react';
import { useGlobalStore, SocketHeader, createSocket, getSocketConnection, reconnectToSocket } from '@bryzos/giss-ui-library';
import { raygunKeys, userRole } from '../common';
import { Auth } from 'aws-amplify';
import { dispatchRaygunError } from '../helper';
import { CognitoUser } from 'amazon-cognito-identity-js';


const useCreateNewSocketConnection = (cognitoUser: CognitoUser | undefined) =>{
    const { userData, isUserLoggedIn, setForceLogout, isAppStateChangeActive }: any = useGlobalStore()
    // const [socketInstance, setSocketInstance] = useState<any>(null)

    const onSocketConnectionError = (errorMsg: string)=>{
      dispatchRaygunError(
        new Error(errorMsg),
        [raygunKeys.socketInvalidToken.tag]
      );
      setForceLogout(true);
    }
      
    useEffect(()=>{
        if(!cognitoUser || !userData?.data) return
        if(isAppStateChangeActive && cognitoUser && isUserLoggedIn){
          reconnectToSocket();
        }
    },[isAppStateChangeActive])

    useEffect(()=>{
        if(cognitoUser && isUserLoggedIn && userData.data){
            createSocketConnection(userData.data, cognitoUser?.signInUserSession?.accessToken.jwtToken);
        }
    },[cognitoUser, userData, isUserLoggedIn])

    // const refreshTokenAndConnectToSocket = async ()=>{
    //   const user = await Auth.currentAuthenticatedUser({ bypassCache: true });
    //   createSocketConnection(userData.data, user.signInUserSession?.accessToken.jwtToken);
    // } 

    const createSocketConnection = async(userData: any, token: string) => {
        const socketHeader :SocketHeader = {
          gissToken: import.meta.env.VITE_WEB_SOCKET_GISS_TOKEN,
          email: userData.email_id,
          //@ts-ignore
          accessToken: token
        };
        const socketInstance = getSocketConnection();
        if(!socketInstance)
        createSocket(
          import.meta.env.VITE_WEB_SOCKET_SERVER, 
          socketHeader,
          {
            userRole: userData?.type,
            onSocketConnectionError
          }
        );
      }
}

export default useCreateNewSocketConnection;