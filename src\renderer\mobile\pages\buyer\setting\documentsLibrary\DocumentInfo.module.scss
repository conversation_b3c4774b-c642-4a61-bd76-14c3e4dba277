@import url(../../setting/profile/Profile.module.scss);

.heading {
    margin-bottom: 0px;
}

.resaleCertheading {
    font-family: Noto Sans Display;
    opacity: 0.7;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    padding: 8px 0px;
    margin-top: 0px;
    display: flex;
    align-items: center;
}

.UploadCertStatus {
    width: 100%;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 10px 8px 10px 12px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 0px;
    box-shadow: none;
    font-family: Noto Sans Display;
    font-size: 14px;
    line-height: 1.6;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
}

.uploadFileStatus {
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 10px 8px 10px 12px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    font-family: Noto Sans Display;
    font-size: 14px;
    line-height: 1.4;
    text-align: left;
    color: #fff;

    .uploadedFileName {
        margin-right: auto;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.documentInfoMain {
    margin-bottom: 12px;

    input {
        display: none;
    }
}

.stateNo.stateNo {
    font-family: Noto Sans Display;
    font-size: 16px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 0px;
}

.delRow{
    color: #ff532d;
}

.uploadFileBtn.uploadFileBtn {
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 10px 8px 10px 12px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    margin-bottom: 0px;

    button {
        margin-left: 9px;
        opacity: 0.5;
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: #fff;
    }

    input {
        display: none;
    }
}

.viewCertlbl.viewCertlbl {
    height: unset;

    .viewCertBtn {
        color: #70ff00;
        margin-left: 4px;
    }
}

.containOfError {
    font-family: Noto Sans Display;
    font-size: 18px;
    line-height: 1.4;
    color: #000;
    height: 73px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.saveChangesBtn {
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
    color: #000;
    height: 48px;
    border-top: solid 0.5px rgba(0, 0, 0, 0.1);
    border-bottom: solid 0.5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.dontSaveBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    border-bottom: solid 0.5px rgba(0, 0, 0, 0.1);

    span {
        opacity: 0.5;
        font-family: Noto Sans Display;
        font-size: 14px;
        line-height: 1.4;
        color: #000;
    }
}

.goSettingPage {
    height: 48px;
    font-family: Noto Sans Display;
    font-size: 14px;
    line-height: 1.4;
    color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dropdownHeader {
    color: #000;
}

.certificateDropdown {
    li:first-child {
        background-color: transparent;
        font-size: 14px;
        font-weight: 500;
        line-height: 1.57;
        padding-left: 0;
        position: unset;

    }
}

.btnSection {
    display: flex;
    flex-direction: column;
    align-items: center;

    .backToPoBtn {
        font-family: Noto Sans Display;
        font-size: 16px;
        font-weight: 300;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: center;
        color: #70ff00;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        gap: 8px;

    }
}

.stateGrid{
    display: flex;
    align-items: center;
    justify-content: space-between;

    .pendingStatus {
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: left;
        color: #ff532d;
      }

      .approvedStatus{
        opacity: 0.7;
        font-family: Noto Sans Display;
        font-size: 12px;
        font-weight: normal;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        display: flex;
        align-items: center;
        svg{
            margin-right: 2px;
        }
      }
}

.settingsContent{
    scroll-behavior: smooth;
}

.addLineSeprator{
        width: 100%;
        height: 0.5px;
        background-color: rgba(255, 255, 255, 0.15);
        margin-top: 15.5px;
        margin-bottom: 8px;
}

.addStateSection{
  display: flex;
  justify-content: center;
  margin-top: 42px;
  margin-bottom: 42px;

    .addStateBtn {
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: 300;
        line-height: 1.1;
        text-align: left;
        color: #70ff00;
        display: flex;
        align-items: center;

        svg{
            width: 17px;
            height: 17px;
            margin-right: 9.7px;
        }
      }
}

.disabledCert{
    pointer-events: none;
    opacity: 0.5;
}