import { registerPlugin, Plugin } from "@capacitor/core";

// we can take advantage of TypeScript here!
interface NativePluginInterface extends Plugin {
//   NativeMethod: () => Promise<Record<"message", string>>;
//   NotifyListeners: () => Promise<void>;
};

// it's important that both Android and iOS plugins have the same name
export const CrashPlugin = registerPlugin<NativePluginInterface>(
  "CrashPlugin"
);