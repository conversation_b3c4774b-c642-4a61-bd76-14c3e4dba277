import { Click<PERSON>wayListener, Dialog } from '@mui/material';
import { useEffect, useRef, useState } from "react";
import {  formatCurrencyWithComma, formatEIN, formatToTwoDecimalPlaces, removeCommaFromCurrency, submitNet30Details } from "../../../../../../library/helper";
import styles from "./ApplyForBnpl.module.scss";
import clsx from 'clsx';
import { buyerSettingConst, snackbarSeverityType } from '../../../../../../library/common';
import { ReactComponent as CloseIcon } from '../../../../../../../assets/mobile-images/close_Popup.svg';
import useBuyerSettingStore from '../../../BuyerSettingStore';
import { IonModal, useIonViewDidEnter, useIonViewWillLeave } from '@ionic/react';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import useSnackbarStore from '../../../../../../library/component/Snackbar/snackbarStore';
const ApplyForBnpl = (props: any) => {
    // const [editModeDesiredCreditLine, setEditModeDesiredCreditLine] = useState(true);
    const [openSubmitApp, setOpenSubmitApp] = useState(false);
    const [successAppSubmit, setSuccessAppSubmit] = useState(false);
    const [isChecked, setIsChecked] = useState(false);
    const { companyInfo, paymentInfo } = useBuyerSettingStore();
    const modal = useRef<HTMLIonModalElement>(null);
    const handleEinNoChange = (event: any) => {
        const { value } = event.target;
        props.setValue('einNumber', formatEIN(value));
    };
    const {showToastSnackbar, setSnackbarOpen}: any = useSnackbarStore();
    const { setShowLoader }: any = useGlobalStore();

    const dnBNumberRef = useRef<HTMLIonIconElement>(null);

    const isNet30DeDirty = props.dirtyFields.dnBNumber || props.dirtyFields.einNumber || props.dirtyFields.creditLine;
    
    
    useIonViewDidEnter(() => {
        if (!paymentInfo?.bnpl) {
            dnBNumberRef.current?.focus();
        }
    }, [paymentInfo]);

    useIonViewWillLeave(() => {
        setOpenSubmitApp(false);
        setSuccessAppSubmit(false);
    },[])

    // const desiredCreditLineEditModeHandler = (editMode: any) => {
    //     if (editMode) {
    //         setEditModeDesiredCreditLine(editMode);

    //     } else if (+props.getValues('creditLine')) {
    //         setEditModeDesiredCreditLine(editMode);
    //     }
    // }
    // useEffect(() => {
    //     if (editModeDesiredCreditLine) {
    //         const input = document.getElementById('desired-credit-line');
    //         if (input) {
    //             input.focus();
    //         }
    //     }
    // }, [editModeDesiredCreditLine])

    const handleSnackbarClose = () => {
        setSnackbarOpen(false);
      };

    const submitNetData = () => {
        if (!props.getValues('dnBNumber') || !props.getValues('creditLine') || !props.getValues('einNumber')) {
            const net30Fields = ['creditLine', 'dnBNumber', 'einNumber'];
            for (let trial in net30Fields) {
                if (props.getValues(net30Fields[trial]) === '') {
                    props.setError(net30Fields[trial], { message: `Net 30 Terms is not valid` });
                }
            }
        } else {
            if (!/^\d{9}$/.test(props.getValues('dnBNumber'))) {
                props.setError('dnBNumber', { message: 'D&B Number is not valid' }, { shouldFocus: true });
                return
            }
            if (!/^\d{2}-\d{7}$/.test(props.getValues('einNumber'))) {
                props.setError('einNumber', { message: 'EIN Number is not valid' }, { shouldFocus: true });
                return
            }
            if (+props.getValues('creditLine') <= 0) {
                props.setError('creditLine', { message: `Net 30 Terms is not valid` });
                return;
            }
            if (+removeCommaFromCurrency(props.getValues('creditLine')) > buyerSettingConst.buyerCreditLineLimit) {
                props.setError('creditLine', { message: buyerSettingConst.creditLimitErrorMessage }, { shouldFocus: true });
                showToastSnackbar(buyerSettingConst.creditLimitErrorMessage, snackbarSeverityType.alert, null, handleSnackbarClose, null, 5000);
                return
            }
            props.clearErrors(['dnBNumber', 'creditLine', 'einNumber']);
            applyNetData()
            // setOpenSubmitApp(true)
            // onSubmit(data, true);
        }

    }

    const applyNetData = async () => {
        setShowLoader(true);
        // setOpenSubmitApp(false)
        const net_30_ein= props.getValues('einNumber');
        const dnb_number= props.getValues('dnBNumber');
        const creditLine= removeCommaFromCurrency(props.getValues('creditLine'));
        try{
        const res = await submitNet30Details(props.userData.data.id,props.net30Default,net_30_ein, dnb_number ,creditLine ,companyInfo);
        props.onSubmit(true)
        setSuccessAppSubmit(true)
        setShowLoader(false);
        props.setNet30ApplyStatus(true);
        }catch(err){
            console.error(err)
            setShowLoader(false);
        }
    }
    console.log("check error data", props.errors, props.getValues())
    return (
        <div >
            <div className={styles.radiobtnSectionTop}>
                    <label className={clsx(styles.containerRadio)}>
                        {isNet30DeDirty && 
                            <>
                                <input type='checkbox' {...props.register("net30CheckBox")} value={props.net30Default} checked={props.selectedPaymentMethod === props.net30Default} onChange={(e) => { props.register("net30CheckBox").onChange(e); props.handlePaymentMethodChange(props.net30Default) }} />
                                <span className={clsx(styles.checkmark)} />
                            </>
                        }
                        <span>Net 30 Terms</span>
                    </label>
                <button className={styles.applyForNetTermsBtn} onClick={(event) => props.handleSubmit(submitNetData)()}>{isNet30DeDirty ? 'Submit Application' : 'Apply For Net Terms'}</button>
            </div>
             <div>
                <label> D&B Number </label>
                <input type='tel' className={clsx(styles.inputField, props.errors?.dnBNumber && styles.errorMsg)} {...props.register("dnBNumber")} pattern="[0-9]" maxLength="9" onChange={(e) => {
                    props.register("dnBNumber").onChange(e)
                    const dnb = e.target.value.replace(/\D/g, '');
                    props.setValue('dnBNumber', dnb);

                }} placeholder='D&B Number'
                    onBlur={(e) => {
                        e.target.value = e.target.value.trim();
                        props.register("dnBNumber").onBlur(e);
                    }} 
                    ref={dnBNumberRef}
                    />
            </div>
            <div>
                <label> EIN </label>
                <input type='tel' className={clsx(styles.inputField, props.errors?.einNumber && styles.errorMsg)} maxLength={10} {...props.register("einNumber")} onChange={(e) => {
                    props.register("einNumber").onChange(e)
                    handleEinNoChange(e)
                }} placeholder='EIN'
                    onBlur={(e) => {
                        e.target.value = e.target.value.trim();
                        props.register("einNumber").onBlur(e);
                    }} />
            </div>
            <div>
                <label> Desired Credit Line </label>
                {/* {editModeDesiredCreditLine ? (
                    <ClickAwayListener 
                        onClickAway={() => desiredCreditLineEditModeHandler(false)}
                    >*/}
                        <div className={clsx(styles.desiredcreditLineInput, props.watch("creditLine") && styles.desiredcreditLine1)}>
                            {props.watch("creditLine") ? <span className={styles.desiredcreditLineDollarSign}>$</span> : ""}
                            <input
                                id='desired-credit-line'
                                type="tel"
                                className={clsx(styles.inputField, props.errors?.creditLine && styles.errorMsg)}
                                {...props.register("creditLine")}
                                value={
                                    (+props.watch("creditLine")) ?
                                        (formatCurrencyWithComma(props.watch("creditLine")) ?? "") :
                                        (props.watch("creditLine") ?? "")
                                }
                                onChange={(e) => {
                                    props.register("creditLine").onChange(e);
                                    props.requestCreditLineChangeHandler(e, "creditLine")
                                }}
                                placeholder="Desired Credit Line"
                            />
                        </div>

                    {/* </ClickAwayListener>
                ) : (
                    <span className={styles.desiredcreditLine} onClick={() => desiredCreditLineEditModeHandler(true)}>
                        $ {formatToTwoDecimalPlaces(props.watch("creditLine"))}
                    </span>
                )} */}
            </div>

            <IonModal className={'popupSubmitApp'} ref={modal} isOpen={openSubmitApp} backdropDismiss={false}>
                <SubmitApplicationDialog
                    setOpenSubmitApp={setOpenSubmitApp}
                    applyNetData={applyNetData}
                    dismiss={()=>{setShowLoader(false)}}
                    setIsChecked={setIsChecked}
                    isChecked={isChecked}
                />
            </IonModal>

            <IonModal className={'popupSubmitApp'} ref={modal} isOpen={successAppSubmit} backdropDismiss={false}>
                <SubmitApplicationSuccessDialog
                        setSuccessAppSubmit={setSuccessAppSubmit}
                        setIsApproved={props.setIsApproved}
                        setValue={props.setValue}
                    />
            </IonModal>

        </div>
    )
}

export default ApplyForBnpl;


export function SubmitApplicationDialog({ setOpenSubmitApp, applyNetData, dismiss, setIsChecked, isChecked }: any) {
    function openLink() {
        const url = import.meta.env.VITE_FILE_URL_BALANCE_TNC;
        window.open(url, '_blank');
    };
    return (
        <>
        <div className={styles.submitAppMain}>
            <span className={styles.closePopupBtn} onClick={(event) => { setOpenSubmitApp(false); setIsChecked(false); dismiss() }}><CloseIcon /></span>
                <p>To submit your credit application,<br /> you must confirm that you have read &<br /> understand the Credit Terms & Conditions.</p>
                <p>Click <span onClick={openLink} className={styles.hereLink}>HERE</span> to view Credit Terms & Conditions</p>

                <label className={clsx(styles.submitAppChk,'containerChk')}>
                    <input type='checkbox' value={isChecked} onClick={() => setIsChecked((prev: any) => !prev)} />
                    <span className={clsx(styles.checkmark,'checkmark')}/>
                    <span className={clsx(styles.lblSubmitApp,'lblChk')}>
                        I have read and understand the Credit Terms
                        & Conditions. I choose, at my absolute discretion,
                        to proceed with applying for extended payment
                        terms (“Credit”).
                    </span>
                </label>

                    <button disabled={!isChecked}
                        className={styles.submitAppBtn}
                        onClick={() => { applyNetData(); }}>Submit Application</button>
        </div>
           
        </>
    );
}

export function SubmitApplicationSuccessDialog(props: any) {
    const handleCloseSuccessPopup = () => {
        props.setSuccessAppSubmit(false); 
        props.setIsApproved(null);
        props.setValue("creditStatus", "Pending")
    }

    return (
        <>
           <div className={styles.submitAppSuccessPopup}>
           <button className={styles.closePopupBtn} onClick={(event) => { handleCloseSuccessPopup() }}><CloseIcon /></button>
            <p className={styles.successPopupTitle}>Success</p>
            <p>Your application has been submitted<br /> and is currently being reviewed.</p>
            <p>Typically, we will have an answer to you in<br /> minutes. Sometimes it takes longer.</p>
            <p>We will reach out if additional<br /> information is needed.</p>
            <p>We will notify you, via email and<br /> desktop notification, once a credit<br /> decision has been made.</p>
            <button className={styles.gotItBtn} onClick={(event) => { handleCloseSuccessPopup() }}>Got it</button>

           </div>
             </>
    );
}