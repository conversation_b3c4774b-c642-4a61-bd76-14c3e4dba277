import { Auth } from "aws-amplify";
import axios, { InternalAxiosRequestConfig } from "axios";

const addTokenToRequestHeader = async (request: InternalAxiosRequestConfig) => {
    const user = await Auth.currentSession();
    const accessToken = user.getAccessToken();
    const jwt = accessToken.getJwtToken();
    if (request.headers.skipInterceptor !== "true") {
      request.headers.AccessToken = jwt;
    }
    return request;
  }
  
export const addAxiosInterceptor = () => {
    axios.interceptors.request.use(async (request) => {
        request = await addTokenToRequestHeader(request);
        return request;
    });
}