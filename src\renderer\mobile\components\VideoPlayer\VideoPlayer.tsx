// @ts-nocheck
import React, { useEffect, useRef, useState } from 'react';
import styles from './VideoPlayer.module.scss';
import { ReactComponent as VideoPlayIcon } from '../../../assets/mobile-images/VideoPlay-Icon.svg';
import { ReactComponent as VideoPlayControlIcon } from '../../../assets/mobile-images/Play.svg';
import { ReactComponent as VideoPauseIcon } from '../../../assets/mobile-images/Pause.svg';
import { ReactComponent as VolumeIcon } from '../../../assets/mobile-images/UnMute.svg';
import { ReactComponent as VolumeMutedIcon } from '../../../assets/mobile-images/Mute.svg';
import { ReactComponent as FullScreenIcon } from '../../../assets/mobile-images/Fullscreen.svg';
import { ReactComponent as ExitFullScreenIcon } from '../../../assets/mobile-images/ExitFullScreen.svg';
import { ReactComponent as PlayNext } from '../../../assets/mobile-images/Skip.svg';
import clsx from 'clsx';
import { CircularProgress } from '@mui/material';
import { useLoadSubtitle } from '../../library/hooks/useLoadSubtitle';

const VideoPlayer = ({ url, width, height, autoPlay, isIphone, isIpad, playNextVideo, poster, imgUrl, showPiPInProcess, isPiP, disableNextVideoBtn , captionUrl }) => {
  const videoRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const containerRef = useRef(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isBuffering, setIsBuffering] = useState(true);
  const [subtitleUrl, setSubtitleUrl] = useState<string | null>(null);

  const { data: subtitle, error, isLoading: isSubtitleLoading } = useLoadSubtitle(captionUrl);

useEffect(() => {
  if (!isSubtitleLoading && subtitle) {
    const subtitleBlob = new Blob([subtitle], { type: 'text/vtt' });
    const objectUrl = URL.createObjectURL(subtitleBlob);
    setSubtitleUrl(objectUrl);
  }

  return () => {
    URL.revokeObjectURL(subtitleUrl);
  };
}, [isSubtitleLoading, subtitle]);  

  const togglePlay = () => {
    if (videoRef.current.paused) {
      videoRef.current.play()
      setIsBuffering(false);
      setIsPlaying(true);
    } else {
      videoRef.current.pause();
      setIsPlaying(false);
    }
  };

  const updateTime = () => {
    setCurrentTime(videoRef.current.currentTime);
    setDuration(videoRef.current.duration);
  };

  const handleSeek = (e) => {
    const seekTime = (e.target.value / 100) * duration;
    videoRef.current.currentTime = seekTime;
    setCurrentTime(seekTime);
  };

  const handleVolumeChange = (e) => {
    const vol = parseFloat(e.target.value);
    setVolume(vol);
    videoRef.current.volume = vol;
    if (vol === 0) {
      setIsMuted(true);
    } else {
      videoRef.current.muted = false;
      setIsMuted(false);
    }
  };

  const toggleMute = () => {
    const newMutedState = !isMuted;
    setIsMuted(newMutedState);
    videoRef.current.muted = newMutedState;
    if (newMutedState) {
      setVolume(0);
    } else {
      setVolume(videoRef.current.volume);
    }
  };

  const formatTime = (time) => {
    if (isNaN(time)) return '00:00';

    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = Math.floor(time % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
    setCurrentTime(0);
  };
  
  const toggleFullscreen = () => {
    const video = videoRef.current;
    const container = containerRef.current;
    if (!isFullscreen) {
      if(isIpad){
        if (video.requestFullscreen) {
          video.requestFullscreen();
        } else if (video.mozRequestFullScreen) {
          video.mozRequestFullScreen();
        } else if (video.webkitEnterFullscreen) {
          video.webkitEnterFullscreen();
        } else if (video.msRequestFullscreen) {
          video.msRequestFullscreen();
        }
      }
      else{
        if (container.requestFullscreen) {
          container.requestFullscreen();
        } else if (container.mozRequestFullScreen) {
          container.mozRequestFullScreen();
        } else if (container.webkitRequestFullscreen) {
          container.webkitRequestFullscreen();
        } else if (container.msRequestFullscreen) {
          container.msRequestFullscreen();
        }
      }
      setIsFullscreen(true);
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) { 
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) { 
        document.msExitFullscreen();
      }
      setIsFullscreen(false);
    }
  };

  useEffect(() => {
    if(url) {
      setIsPlaying(autoPlay);
      setIsBuffering(true);
    }
  }, [url, autoPlay]);

  useEffect(()=>{
    const videoElement = videoRef.current;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    videoElement.addEventListener('play', handlePlay);
    videoElement.addEventListener('pause', handlePause);

    const handleEnterPictureInPicture = () => {
      showPiPInProcess(true);
    }
    const handleLeavePictureInPicture = () => {
      showPiPInProcess(false);
    }
    videoElement.addEventListener('enterpictureinpicture', handleEnterPictureInPicture);
    videoElement.addEventListener('leavepictureinpicture', handleLeavePictureInPicture);

    return () => {
      videoElement.removeEventListener('play', handlePlay);
      videoElement.removeEventListener('pause', handlePause);
      videoElement.removeEventListener('enterpictureinpicture', handleEnterPictureInPicture);
      videoElement.removeEventListener('leavepictureinpicture', handleLeavePictureInPicture);
    };

  },[videoRef?.current])


  const handleOnPlaying = () => {
    setIsBuffering(false);
  }

  const handleOnWaiting = () => {
    setIsBuffering(true);
  }

  const progress = (currentTime / duration) * 100 || 0;

  return (
    <div className={clsx(styles['videoPlayerMain'], ((isIphone || (isIpad && isPiP))) ? 'noSubTitleStyleIos' : (isIphone || isIpad) ? 'subTitleStyleIos' : '', 
      (!(isIphone || isIpad)) && 'subTitleStyle')}>
      <div className={clsx(styles['custom-video-player'], isFullscreen && styles['fullScreenHeight'])} ref={containerRef}>
        {isBuffering && 
        <div className={styles.videoLoading}> 
          <CircularProgress classes={{
              circle:styles.colorLoader
           }} />
         </div>}
        <video
          ref={videoRef}
          controls={false}
          onTimeUpdate={updateTime}
          onLoadedMetadata={updateTime}
          className={styles.video}
          onEnded={handleEnded}
          autoPlay={autoPlay}
          width={width}
          height={height}
          src={url}
          poster={poster}
          onPlaying={handleOnPlaying}
          onWaiting={handleOnWaiting}
        >
         {(subtitleUrl && !isPiP) && 
          <track 
          kind="subtitles" 
          src={subtitleUrl} 
          srcLang="en" 
          label="English" 
          default />
        }
          Your browser does not support the video tag.
        </video>
          <div className={clsx(styles['overlay'], {[styles['noOverlay']] : isPlaying })} onClick={togglePlay}>
            {(isIphone || (isIpad && isPiP)) && <img src={imgUrl} className={styles.thumbnailImg}/>}
            <span className={styles['VideoPlayIcon']} >
            {!isBuffering && ((!isPiP ) && ((!videoRef?.current?.paused) ? <VideoPauseIcon /> : <span className={styles.playIconVideo}><VideoPlayIcon /></span>))}
            </span>
          </div>
        {!((isIphone || isIpad) && isPiP) &&(
          <div className={styles['controls']}>
            <div className={styles['seek-container']}>
              <input
                type="range"
                min="0"
                max="100"
                value={progress}
                className={styles['seek-bar']}
                onChange={handleSeek}
                style={{ '--progress': `${progress}%` }}
              />
            </div>
            <div className={styles['action-container']}>
            <div className={styles['leftbar-action']}>
              <button onClick={togglePlay}>
                {(!videoRef?.current?.paused) ? <VideoPauseIcon /> : <VideoPlayControlIcon />}
              </button>
              <button className={styles.playNextBtn} onClick={playNextVideo} disabled={disableNextVideoBtn}>
                <PlayNext />
              </button>
              <div className={styles['time-display']}>
                <span className={styles['current-time']}>{formatTime(currentTime)}</span>
                <span>/</span>
                <span className={styles['duration']}>{formatTime(duration)}</span>
              </div>
              
              {!(isIphone || isIpad)  && <>
                <button className={styles['volume-icon']} onClick={toggleMute}>
                  {isMuted ? <VolumeMutedIcon /> : <VolumeIcon />}
                </button>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={volume}
                  onChange={handleVolumeChange}
                  className={styles['volume-bar']}
                  style={{
                    background: `linear-gradient(to right, #fff ${volume * 100}%, rgba(156, 163, 175, 0.6) ${volume * 100}%)`
                  }}
                /> 
              </>}
            </div>
            {!isIphone && <button className="fullscreen-button" onClick={toggleFullscreen}>
             {!isPiP && (((isIphone || isIpad) || !isFullscreen) ? <FullScreenIcon/> : <span className={styles.exitFullscreenIcon}><ExitFullScreenIcon/></span>)}
            </button>}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoPlayer;
