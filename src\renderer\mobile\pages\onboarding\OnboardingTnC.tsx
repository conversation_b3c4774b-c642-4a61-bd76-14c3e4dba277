//@ts-nocheck
import styles from './onboardingTnC.module.scss'
import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { fileType, routes } from '../../library/common';
import { mobileDiaglogConst, useGlobalStore } from '@bryzos/giss-ui-library';
import { IonContent, IonPage, useIonLoading, useIonRouter } from '@ionic/react';
import axios from 'axios';
import { Auth } from 'aws-amplify';
import { getDeviceId } from '../../library/helper';
import { Device } from '@capacitor/device';
import useDialogStore from '../../components/Dialog/DialogStore';

function OnboardingTnc() {
    const router = useIonRouter();
    const [tncCheckbox, setTncCheckBox] = useState(false);
    const [tncCheckboxDisable, setTncCheckBoxDisable] = useState(false);
    const [tncData, setTncData] = useState<any>({});
    const data = router.routeInfo.routeOptions?.state;
    const { setShowLoader, setSignupUser, appVersion, systemVersion } = useGlobalStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();

    useEffect(() => {
        const handleTncData = async () => {
            try {
                const responseData = await axios.get(import.meta.env.VITE_API_SERVICE + '/reference-data/homepage');
                responseData.data.ref_bryzos_terms_conditions.forEach((termsAndCondition: any) => {
                    if (data.userType === termsAndCondition.type) {
                        setTncData(termsAndCondition)
                    }
                })
                setShowLoader(false);
            } catch (error) {
                console.error('onboarding tnc error', error)
                setShowLoader(false);
            }
        }
        setShowLoader(true);
        handleTncData();
    }, [])
    const handleOnboardingSubmit = async () => {
        try {
            setShowLoader(true);
            setTncCheckBoxDisable(false)
            if (data) {
                const user = await Auth.signUp({
                    username: data.emailAddress,
                    password: data.password,
                    email: data.emailAddress,
                    attributes: {
                        [import.meta.env.VITE_SIGNUP_PREPEND_ATTRIBUTE_KEY+'firstname']: data.firstName,
                        [import.meta.env.VITE_SIGNUP_PREPEND_ATTRIBUTE_KEY+'lastname']: data.lastName,
                        [import.meta.env.VITE_SIGNUP_PREPEND_ATTRIBUTE_KEY+'type']: data.userType
                    }
                });
                const payloadData = {
                    "data": {
                        "username": data.emailAddress
                    }
                };
                const confirmSignupRes = await axios.post(import.meta.env.VITE_API_SERVICE + '/user/confirm-signup', payloadData);
                if(confirmSignupRes.data.data?.error_message){
                    showCommonDialog(confirmSignupRes.data.data.error_message,[{name: mobileDiaglogConst.ok, action: resetDialogStore}]);
                    return;
                }
                const userData = {
                    email: data.emailAddress,
                    password: data.password
                }
                const payload = {
                    data: {
                        user_type: data.userType,
                        company_name: data.companyName,
                        first_name: data.firstName,
                        last_name: data.lastName,
                        email_id: data.emailAddress,
                        bryzos_terms_condtion_id: tncData.id,
                        accepted_terms_and_condition: tncData.terms_conditions_version,
                        zip_code: data.zipCode,
                        client_company: data.companyEntity,
                        last_login_app_version: import.meta.env.VITE_REACT_APP_VERSION,
                        device_id: await getDeviceId(),
                        ui_version: appVersion,
                        os_version: (await Device.getInfo()).osVersion,
                        cognito_user_name: user.userSub,
                    }
                }
                const responseData = await axios.post(import.meta.env.VITE_API_SERVICE + '/user/signup', payload);
                if (responseData.data.data.error_message) {
                    router.push("/onboarding-detail",{animate:true,direction:'forward'}, undefined, undefined, { state: { data, errorMessage: responseData.data.data.error_message, tncCheckbox, tncData } })
                    // navigate(routes.onboardingDetails, { state: { data, errorMessage: responseData.data.error_message, tncCheckbox, tncData } })
                    setShowLoader(false);
                } else { 
                    const isUserApproved = responseData.data?.data?.is_approved ?? false;
                    if(isUserApproved) setSignupUser(userData);
                    router.push("/onboarding-thank-you",{animate:true,direction:'forward'}, undefined, {isUserApproved});
                    // navigate(routes.onboardingThankYou);
                    setShowLoader(false);
                }
            } else {
                router.push("/onboarding-detail",{animate:true,direction:'forward'});
                // navigate(routes.onboardingDetails);
                setShowLoader(false);
            }
        } catch (error) {
            setShowLoader(false);
            console.error('onBoarding error', error)
            setTncCheckBoxDisable(true)
        }

    }
    const [tnc, setTnc] = useState('');
    const FetchHtml = async () => {
        const response = await fetch(tncData.cloudfront_url+"?ver="+new Date().getTime());
        return await response.text(); // Returns it as Promise
    };
    const SetHtml = async () => {
        await FetchHtml().then((text) => {
            setTnc(text);
            setShowLoader(false);
        });
    };
    useEffect(() => {
        if (Object.keys(tncData).length !== 0) {
            SetHtml(true)
        }
    }, [tncData]);

    return (
        <IonPage>
            <IonContent>
                <div className={styles.onboardingMobTnCMain}>
                    <div className={styles.tnCInnerContent}>
                        <div className={styles.onboardingLogo}>
                            <img src='/onboardingLogo.png' />
                        </div>
                        <div className={clsx(styles.trial, tncCheckbox && styles.onboardingTnCBody)}>
                            <div className={styles.tnCPage}>
                                <div className={styles.onboardingTncBox}>
                                    <div className={styles.tncScrollClass}>
                                        <h2 className={styles.termsofUseV1}>Bryzos Instant Pricing Desktop Widget Terms of Use ({tncData?.terms_conditions_version?.toUpperCase()})</h2>
                                        <div className='tncMobileOnboarding'>
                                            <div dangerouslySetInnerHTML={{ __html: tnc }}></div>
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <div className={clsx(styles.checkingThisbox, 'onboardingChk')}>
                                <label className='containerChk'>
                                    <input type='checkbox' onChange={(e) => { setTncCheckBox(e.target.checked); setTncCheckBoxDisable(e.target.checked) }} checked={tncCheckbox} />
                                    <span className='checkmark' />
                                    <span className='lblChk'>
                                        By checking this box I am confirming on behalf of myself, and the company which I represent that I have read, understand and agree to the above terms and conditions.
                                    </span>
                                </label>
                            </div>
                        </div>

                    </div>

                    <div className={clsx(tncCheckbox && styles.onboardingTnCBody)}><button className={styles.GetStartedbtn} disabled={!tncCheckboxDisable} onClick={handleOnboardingSubmit}>Next</button></div>

                </div>
            </IonContent>
        </IonPage>
    );
}
export default OnboardingTnc;
