// @ts-nocheck
import { IonPage, IonContent, useIonRouter, useIonViewWillEnter, useIonLoading, useIonViewWillLeave, useIonViewDidEnter } from '@ionic/react';
import styles from '../profile/Profile.module.scss';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { companyInfoSchema } from '../SettingSchema';
import { useEffect, useState } from 'react';
import { Autocomplete, TextField } from '@mui/material';
import { useImmer } from 'use-immer';
import useGetCompanyLists from '../../../../library/hooks/useGetCompanyLists';
import { CustomMenu } from '../../../../components/CustomMenu';
import { sellerSettingPayLoadFormatter, checkStateZipValidation, formatPhoneNumberWithCountryCode, saveUserSetting, emojiRemoverRegex } from '../../../../library/helper';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import axios from 'axios';
import clsx from 'clsx';
import useSellerSettingStore, { CompanyDetails, Profile } from '../SellerSettingStore';
import useDialogStore from '../../../../components/Dialog/DialogStore';
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import { mobileDiaglogConst, sellerSettingConst } from '../../../../library/common';
import useSnackbarStore from '../../../../library/component/Snackbar/snackbarStore';

const SellerCompanyInfo = () => {

    const {
        register,
        handleSubmit,
        getValues,
        clearErrors,
        setValue,
        reset,
        watch,
        setError,
        control,
        formState: { errors, dirtyFields, isDirty, isValid } } = useForm({
            resolver: yupResolver(companyInfoSchema)
        });
    const [states, setStates] = useState([]);
    const [companyNameValue, setCompanyNameValue] = useImmer(null);
    const [companyNameInput, setCompanyNameInput] = useState("");
    const [yourCompanyValue, setYourCompanyValue] = useImmer(null);
    const [yourCompanyInput, setYourCompanyInput] = useState("");
    const [disableYourCompany, setDisableYourCompany] = useState(true);
    const [isCompanyNameExists, setIsCompanyNameExists] = useState(false);
    const [yourCompanyList, setYourCompanyList] = useState([]);
    const [isSaveDisable, setIsSaveDisable] = useState(true);
    const [validationInProgress, setValidationInProgress] = useState(false);
    const {userData, setShowLoader,referenceData} = useGlobalStore();
    const { data: companyListsData, isLoading: isCompanyListsDataLoading, } = useGetCompanyLists();
    const { companyInfo, setCompanyInfo, sellerSetting, setSellerSettingInfo } = useSellerSettingStore();
    const router = useIonRouter()
    const { showCommonDialog, resetDialogStore } = useDialogStore();

    useEffect(() => {
        if (isDirty)
            setIsSaveDisable(false);

            // const handleBackButton = (ev: BackButtonEvent) => {
            //     ev.detail.register(10, async () => {
            //         backToSetting();
            //     });
            // };
    
            // document.addEventListener('ionBackButton', handleBackButton);
            // return () => {
            //     document.removeEventListener('ionBackButton', handleBackButton);
            // };
    }, [isDirty])
    
    useIonViewWillEnter(() => {
        if (companyInfo) {
            setValue('companyName', companyInfo.company_name);
            setValue('yourCompany', companyInfo.client_company);
            setYourCompanyInput(companyInfo.client_company ?? "")
            setIsCompanyNameExists(companyInfo.company_name != null);
            setDisableYourCompany(companyInfo.company_name === null)
            setValue('companyAddressLine', companyInfo.company_address_line1);
            setValue('companyCity', companyInfo.company_address_city);
            setValue('companyState', companyInfo.company_address_state_id);
            setValue('companyZipCode', companyInfo.company_address_zip);
            clearErrors();
        }
    }, [companyInfo])

    useIonViewDidEnter(() => {
        const t = setTimeout(() => {
            let element;
            if (!companyInfo?.company_name) {
                element = document.getElementById("companyNameAutocomplete");
                if (element) {
                    element.focus();
                }
            } else {
                element = document.getElementById("yourCompanyAutocomplete");
                if (element) {
                    element.focus();
                }
            }

        }, 300);

        return () => {
            clearTimeout(t);
        }
    });

    useIonViewWillLeave(()=>{
        setIsSaveDisable(true);
        reset();
        resetDialogStore();
    })

    useEffect(()=>{
        if (referenceData) setStates(referenceData.ref_states);
    },[referenceData])

    useEffect(() => {
        if (isDirty) {
            handleStateZipValidation('companyZipCode', 'companyState')
        }
    }, [watch('companyZipCode'), watch('companyState')])

    useEffect(() => {
        if (watch("companyName") !== null) {
            const companyData = companyListsData?.find((companyData) => companyData.company_name === watch("companyName"))
            setYourCompanyList(companyData?.client_company ?? [])
        }
    }, [watch("companyName")])

    const showStateZipError = ()=> {
        setError("companyZipCode", { message: "The zip code and state code do not match" });
        setError("companyState", { message: "The zip code and state code do not match" });
    }

    const handleStateZipValidation = async (zipCode, stateCode) => {
        console.log(zipCode,stateCode);
        setValidationInProgress(true)
        const checkStateZipResponse = await checkStateZipValidation(getValues(zipCode), getValues(stateCode));
        if (checkStateZipResponse?.data.data === true) {
            clearErrors(["companyZipCode","companyState"]);
        } else {
            showStateZipError();
        }
        setValidationInProgress(false)
    };

    const onSubmit = async(data) => {
        console.log("DATA",data)
        setShowLoader(true)
        const companyDetail : CompanyDetails = {
            company_name: data.companyName, 
            client_company: data.yourCompany, 
            company_address_line1: data.companyAddressLine, 
            company_address_city: data.companyCity,
            company_address_state_id: data.companyState,
            company_address_zip: data.companyZipCode
        }   
        // const _sellerSetting = {...sellerSetting, ...companyDetail};
        // console.log("_sellerSetting",_sellerSetting)
        // const payload: any = sellerSettingPayLoadFormatter(_sellerSetting);
        try{
            await saveUserSetting(sellerSettingConst.apiRoutesForSave.companyInfo, companyDetail, userData);
            // setSellerSettingInfo(_sellerSetting);
            setCompanyInfo(companyDetail);
            router.push('/seller-setting',{animate:true,direction:'forward'});
            setShowLoader(false);
        }catch(err){
            setShowLoader(false);
        }
    }

    const handleFormSubmit = () => {
        console.log('check errorss', errors)
        if (Object.keys(errors).length === 0 && !validationInProgress) {
            handleSubmit(onSubmit)();
        } else {
            return;
        }
    }

    const routeBackToSetting = ()=>{
        router.push('/seller-setting',{animate:true,direction:'forward'});
        resetDialogStore();
    }

    const backToSetting = () => {
        if(isDirty)
        showCommonDialog(mobileDiaglogConst.unsavedChangesMsg,[{name: mobileDiaglogConst.stay, action: resetDialogStore},{name: mobileDiaglogConst.goBackToSetting, action: routeBackToSetting}]);
        else
        routeBackToSetting();
    }    

    return (
        <IonPage>
            <IonContent>
                <div className={styles.profile}>
                    <h2 className={styles.heading}><BackBtnArrowIcon onClick={backToSetting}/><span> Company Information</span> </h2>
                    <div className={styles.settingsContent}>
                        <div className={styles.profileComponent}>
                            <label>Main Company</label>
                             {isCompanyNameExists ?
                                <input readOnly className={styles.inputField} value={getValues("companyName")}/>
                                :
                                <Controller
                                    name="companyName"
                                    control={control}
                                    render={({ field: { ...rest } }) => (
                                        <Autocomplete 
                                            value={companyNameValue}
                                            onChange={(event, value) => {
                                                setCompanyNameValue(value);
                                                if (value?.company_name) {
                                                    setDisableYourCompany(false)
                                                } else {
                                                    setDisableYourCompany(true)
                                                    setYourCompanyList([])
                                                    setYourCompanyInput("")
                                                }
                                                rest.onChange(value?.company_name ?? null);
                                            }}
                                            inputValue={companyNameInput}
                                            className={'companySelectDropdown'}
                                            onInputChange={(event, newInputValue) => {
                                                setCompanyNameInput(newInputValue);
                                                setValue("companyName", getValues("companyName"), { shouldDirty: true });
                                            }}
                                            classes={{
                                                root: styles.autoCompleteDesc,
                                                popper: styles.autocompleteDescPanel,
                                                paper: styles.autocompleteDescInnerPanel,
                                                listbox: styles.listAutoComletePanel,
                                            }}
                                            id="companyNameAutocomplete"
                                            disablePortal={false}
                                            options={companyListsData?.length ? companyListsData : []}
                                            sx={{ width: '100%' }}
                                            renderInput={(params) => <TextField className={styles.companyInput} {...params} placeholder="Select company name" />}
                                            getOptionLabel={(item) => {
                                                return item?.company_name ?? "";
                                            }}
                                        />
                                    )}
                                />
                            }
                        </div>
                        <div>
                            <label> Your Company </label>
                            <Controller
                                name="yourCompany"
                                control={control}
                                render={({ field: { ...rest } }) => (
                                    <Autocomplete
                                        freeSolo
                                        disabled={disableYourCompany}
                                        value={yourCompanyValue}
                                        onChange={(event, value) => {
                                            setYourCompanyValue(value);
                                            rest.onChange(value ?? null);
                                        }}
                                        inputValue={yourCompanyInput}
                                        className={'companySelectDropdown'}
                                        onInputChange={(event, newInputValue) => {
                                            setYourCompanyInput(newInputValue);
                                            rest.onChange(newInputValue)
                                        }}
                                        classes={{
                                            root: styles.autoCompleteDesc,
                                            popper: styles.autocompleteDescPanel,
                                            paper: styles.autocompleteDescInnerPanel,
                                            listbox: styles.listAutoComletePanel,
                                        }}
                                        id="yourCompanyAutocomplete"
                                        disablePortal={false}
                                        options={yourCompanyList?.length ? yourCompanyList : []}
                                        sx={{ width: '100%' }}
                                        renderInput={(params) => <TextField className={styles.companyInput} {...params} placeholder="Enter company name" />}
                                        getOptionLabel={(item) => {
                                            return item ?? "";
                                        }}
                                    />
                                )}
                            />

                        </div>
                        <div className={styles.profileComponent}>
                            <label>Company Address</label>
                            <input type="text" className={clsx(styles.inputField, errors?.companyAddressLine?.message && styles.errorMsg)} {...register("companyAddressLine")} placeholder="Address"
                                onBlur={(e) => {
                                    e.target.value = e.target.value.trim();
                                    register("companyAddressLine").onBlur(e);
                                }} />
                        </div>
                        <div className={styles.profileComponent}>
                            <label>City </label>
                            <input type="text" className={clsx(styles.inputField, errors?.companyCity?.message && styles.errorMsg)} {...register("companyCity")} placeholder="City"
                                onBlur={(e) => {
                                    e.target.value = e.target.value.trim();
                                    register("companyCity").onBlur(e);
                                }} />
                        </div>
                        <div className={styles.stateDropdown}>
                        <div className={styles.grid1}>
                            <label>State </label>
                            <span className={(errors?.companyState?.message && 'errorMsgDropdown')}>
                            <CustomMenu
                                control={control}
                                name={'companyState'}
                                placeholder={'State'}
                                MenuProps={{
                                    classes: {
                                        paper: styles.Dropdownpaper,
                                    },
                                }}
                                items={states?.map(x => ({ title: x.code, value: x.id }))}
                                className={'selectDropdown'}

                            />
                            </span>
                        </div>
                        <div className={clsx(styles.profileComponent,styles.grid1)}>
                            <label>Zip Code</label>
                            <input type='tel' className={clsx(styles.inputField, errors?.companyZipCode?.message && styles.errorMsg)} {...register("companyZipCode")} onChange={(e) => {
                                register("companyZipCode").onChange(e)
                                const zipCode = e.target.value.replace(/\D/g, '');
                                setValue('companyZipCode', zipCode);
                            }} maxLength="5" placeholder='Zip Code' />
                        </div>
                        </div>
                    </div>
                    <div className={styles.btnSection}>
                        <button onClick={() => handleFormSubmit()} disabled={isSaveDisable}>Save</button>
                    </div>
                </div>
            </IonContent>
        </IonPage>
    )
}

export default SellerCompanyInfo;