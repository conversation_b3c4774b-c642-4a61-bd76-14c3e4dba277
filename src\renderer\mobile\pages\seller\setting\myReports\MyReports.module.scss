.myReports{
    padding: 0px 16px 16px 16px;
    display: flex;
    flex-direction: column;
    height: calc(100% - 118px);
    .heading{
        font-family: Noto Sans Display;
        font-size: 16px;
        font-weight: normal;
        line-height: 1.4;
        text-align: center;
        color: #fff;
        padding: 12px 0px;
        margin-top: 0px;
        border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
        display: flex;
        justify-content: center;
        align-items: center;

        span {
            margin: 0 auto;
        }
    }
    button{
        opacity: 0.8;
        font-family: Noto Sans Display;
        font-size: 14px;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        padding-bottom: 16px;
        display: flex;
        gap: 12px;
        svg{
            margin-top: 4px;
        }
    }
}
.ErrorDialog {
    .dialogContent{
        border-radius: 16px;
        box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.5);
        background-color: rgba(255, 255, 255, 0.8);
    } 
    .okForErrorBtn{
        height: 48px;
        font-family: <PERSON>o Sans Display;
        font-size: 14px;
        line-height: 1.4;
        text-align: center;
        color: #000;
        font-weight: 600;
        border-top: solid 0.5px rgba(0, 0, 0, 0.1);
    }
    .containOfError{
        font-family: Noto Sans Display;
        font-size: 18px;
        line-height: 1.4;
        text-align: center;
        color: #000;
        padding: 20px;
    }
}