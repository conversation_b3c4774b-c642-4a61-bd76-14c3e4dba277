// @ts-nocheck
import styles from './ToastSnackbar.module.scss'
import useSnackbarStore from "./snackbarStore";
import { getChannelWindow } from "../../helper";
import { channelWindowList } from "../../../common";
import clsx from 'clsx';
import { ReactComponent as CloseIcon } from '../../assets/images/Icon_Close.svg';
import { v4 as uuidv4 } from 'uuid';

const ToastSnackbar = () => {
    const {openSnackbar, snackbarMessage, snackbarActions, snackbarSeverity, snackbarCloseHandler} = useSnackbarStore();
    const channelWindow = Object.keys(getChannelWindow()).length ? getChannelWindow() : channelWindowList ;

    let backgroundColor;
    if (snackbarSeverity === 'warning') {
        backgroundColor = 'warning';
    } 
    else if (snackbarSeverity === 'alert') {
        backgroundColor = 'alert';
    } 
    else if (snackbarSeverity === 'success'){
        backgroundColor='green'
    }

    if(!openSnackbar) return<></>

    return (
        <div className={clsx(styles.snackbarContainer, openSnackbar ? styles.show : styles.hide, `${backgroundColor}`)}>
            <div className={styles.content} dangerouslySetInnerHTML={{ __html: snackbarMessage }}/>
            {
                snackbarActions?.map((action)=>{
                    return <button key={uuidv4()} className={styles.actionBtn} onClick={action.handler}> {action.name} </button>
                })
            }
            {
                snackbarCloseHandler ? <button className={styles.closeBtn} onClick={snackbarCloseHandler}> <CloseIcon /> </button> : <></>
            }
        </div>
    );
};


export default ToastSnackbar;