// @ts-nocheck
import { useEffect, useRef, useState } from 'react';
import { IonPage, IonContent, useIonViewWillEnter, useIonRouter, useIonViewWillLeave, useIonLoading } from '@ionic/react';
import styles from './instantPurchasing.module.scss';
import InstantPurchasingSteps1 from './instantPurchasingSteps/instantPurchasingSteps1';
import InstantPurchasingSteps2 from './instantPurchasingSteps/instantPurchasingSteps2';
import InstantPurchasingSteps3 from './instantPurchasingSteps/instantPurchasingSteps3';
import { v4 as uuidv4 } from 'uuid';
import OrderConfirmation from '../../../components/OrderConfirmation/OrderConfirmation';
import useBuyerSettingStore, { BNPLModel, BuyerSetting, Payment } from '../setting/BuyerSettingStore';
import { InstantPurchasingWrapper, useGlobalStore, useSaveUserActivity, ueGetBuyingPreference, useGetDeliveryDate } from '@bryzos/giss-ui-library';
import { ReactComponent as StepsArrowIcon } from '../../../../assets/mobile-images/Steps_Arrow_Icon.svg';
import clsx from 'clsx';

function InstantPurchasingStepper({
    control,
    register,
    handleSubmit,
    getValues,
    setValue,
    setError,
    watch,
    clearErrors,
    trigger,
    reset,
    errors,
    isValid,
    resetPOInProgressData,
    setDefaultPurchaseOrderInfo,
    handleStateZipValidation,
    quantitySizeValidator,
    updateLineItem,
    pricePerUnitChangeHandler,
    calculateMaterialTotalPrice,
    removeLineItem,
    buyingPreference,
    setBuyingPreference,
    calculateTotalPricing,
    calculateDepositAmount,
    paymentMethodChangeHandler,
    saveUserLineActivity,
    saveCreatePOUserActivity,
    setDefaultPaymentOption,
    paymentMethods
}) {
    const router = useIonRouter();
    const userData = useGlobalStore(state => state.userData);
    const setCreatePoData = useGlobalStore(state => state.setCreatePoData);
    const createPoData = useGlobalStore(state => state.createPoData);
    const setShowLoader = useGlobalStore(state => state.setShowLoader);
    const setCreatePoSessionId = useGlobalStore(state => state.setCreatePoSessionId);
    const { refreshTheApp, setRefreshTheApp, discountData } = useGlobalStore();
    const { setPaymentInfo, paymentInfo} = useBuyerSettingStore();
    const [selectedInstantPricingStep, setSelectedInstantPricingStep] = useState(1);
    const [todayDate, setTodayDate] = useState('');
    const [sessionId, setSessionId] = useState('');
    const [disableCloseAnalytic, setDisableCloseAnalytic] = useState(true);
    const [deliveryStateFocusEnable, setDeliveryStateFocusEnable] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const [sendInvoicesToEmailData, SetSendInvoicesToEmailData] = useState('');
    const [orderConfirmationData, setOrderConfirmationData] = useState(null);
    const [resaleCertificateData, setResaleCertificateData] = useState([]);
    const analyticRef = useRef();
    analyticRef.current = disableCloseAnalytic;
    const { hideHeader, setHideHeader } = useGlobalStore();
    const [maxStepsEnabled, setMaxStepsEnabled] = useState(1);
    const [deliveryDates, setDeliveryDates] = useState([]);
    const [deliveryDateMap, setDeliveryDateMap] = useState({});
    const [disableDeliveryDate, setDisableDeliveryDate] = useState(false);
    const getBuyingPreference = ueGetBuyingPreference();
    const logUserActivity = useSaveUserActivity();
    const {referenceData} = useGlobalStore()
    const getDeliveryDate = useGetDeliveryDate();

    useEffect(()=>{
        instantPurchansingInit();
    },[referenceData,createPoData])

    useIonViewWillLeave(() => {
        setShowLoader(false);
        if (!discountData) {
            setHideHeader(false);
            setSelectedInstantPricingStep(1)
            setMaxStepsEnabled(1)
            reset();
        }
    }, [])
    
    useEffect(()=>{
        if (!discountData) {
            setSelectedInstantPricingStep(1)
            setMaxStepsEnabled(1)
            reset();
            setRefreshTheApp(false)
        }
    },[refreshTheApp])

    useIonViewWillLeave(()=>{
        if (analyticRef.current && sessionId) {
            const payload = {
                "data": {
                    "session_id": sessionId,
                    "close_status": "CANCEL"
                }
            }
            logUserActivity.mutateAsync({url:import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close' ,payload})
                .then(() => setCreatePoSessionId(null))
                .catch(err => console.error(err))
        }
    },[sessionId])

    useEffect(() => {
        if (selectedInstantPricingStep > maxStepsEnabled) setMaxStepsEnabled(selectedInstantPricingStep);
    }, [selectedInstantPricingStep])

    const instantPurchansingInit = () => {
        setSelectedInstantPricingStep(1);
        setHideHeader(false);
        setUserDetailForPurchase();
        startCreatePoSession();
    }

    const setUserDetailForPurchase = async () => {
        try {
            setShowLoader(true);
            const deliveryListData = await getDeliveryDateData();
            const res = await getBuyingPreference.mutateAsync();
            const buyingPreference: BuyerSetting = res.data.data;
            if (!paymentInfo) {
                const bnpl: BNPLModel = buyingPreference.bnpl_settings ?? null;
                const ach: BNPLModel = buyingPreference.ach_credit ?? null;
                const paymentInfoData: Payment = bnpl || ach ? { bnpl, ach } : null;
                paymentInfoData.default_payment_method = buyingPreference.default_payment_method;
                setPaymentInfo(paymentInfoData);
            }
            setBuyingPreference(buyingPreference);
            if (buyingPreference.resale_certificate) setResaleCertificateData(buyingPreference.resale_certificate);
            const deliveryReceivingHours = buyingPreference.user_delivery_receiving_availability_details;
            if (deliveryReceivingHours?.length !== 0) {
                setValue('recevingHours', deliveryReceivingHours)
            }
            const deliveryDaysAddValue = buyingPreference.delivery_days_add_value ?? referenceData?.ref_delivery_date[0]?.days_to_add;
            setValue('delivery_date_offset', deliveryDaysAddValue);
            let disableDeliveryDates = true;
            deliveryListData.forEach(deliveryDateObj => {
                if (deliveryDateObj.days_to_add === deliveryDaysAddValue) {
                    const deliveryDate = !deliveryDateObj.disabled ? deliveryDateObj.value : null;
                    setValue('delivery_date', deliveryDate);
                }
                if (!deliveryDateObj.disabled && disableDeliveryDates) {
                    disableDeliveryDates = false;
                }
            });
            setDisableDeliveryDate(disableDeliveryDates)
            if (createPoData) {
                resetPOInProgressData(createPoData, buyingPreference, paymentMethods);
            } else {
                SetSendInvoicesToEmailData(buyingPreference.send_invoices_to);
                setDefaultPurchaseOrderInfo(buyingPreference, paymentMethods);
            }
            setTodayDate(new Date());
            setShowLoader(false)
        } catch (err) {
            setErrorMessage("Something went wrong. Please try again in sometime");
            setShowLoader(false)
            console.error(err)
        }
    }

    const startCreatePoSession = () => {
        const sessionId = uuidv4();
        setSessionId(sessionId);
        setCreatePoSessionId(sessionId)
        const payload = {
            "data": {
                "session_id": sessionId
            }
        }
        logUserActivity.mutateAsync({url:import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close' ,payload}).catch(err => console.error(err));
    }

    useEffect(() => {
        handleStateZipValidation();
    }, [watch('shipping_details.zip'), watch('shipping_details.state_id')])

    const goToStep = (step: number) => {
        if (step <= maxStepsEnabled)
            setSelectedInstantPricingStep(step);
    }

    const saveUserActivity = (checkOutPoNumber, checkOutError) => {
        saveCreatePOUserActivity(sessionId, checkOutPoNumber, checkOutError);
    }

    const getDeliveryDateData = async () => {
        let deliveryDateList = []; 
        const res = await getDeliveryDate.mutateAsync();
        if(res?.data?.data){
            deliveryDateList = res.data.data
        }
        const optionMap = deliveryDateList.reduce((acc, option) => {
            acc[option.value] = option;
            return acc;
        }, {});
        setDeliveryDateMap(optionMap)
        setDeliveryDates(deliveryDateList);
        return deliveryDateList
    }
   
    return (
        <>
            <div className={`${styles.tabPanel} ${hideHeader || selectedInstantPricingStep > 3 ? 'hidden' : ''}`}>
                <span className={selectedInstantPricingStep === 1 ? styles.purchaseStepstext : styles.purchaseStepstextHidden}>Create a Purchase in 3 Steps</span>
                <div className={styles.tabs} slot="top">

                    <div className={clsx(styles.tabsBtn, selectedInstantPricingStep === 1 && styles.tabSelected, maxStepsEnabled>=1 && styles.activeTab)} tab="steps1"
                        onClick={() => { goToStep(1) }}
                    >
                        <div className={styles.stepsContent}>STEP 1<span>Header<br />Info</span></div>
                    </div>
                    <span className={styles.stepsArrow}><StepsArrowIcon /></span>
                    <div className={clsx(styles.tabsBtn, selectedInstantPricingStep === 2 && styles.tabSelected, maxStepsEnabled>=2 && styles.activeTab)} tab="steps2"
                        onClick={() => { goToStep(2) }}
                    >
                        <div className={styles.stepsContent}>STEP 2<span>Add<br />Lines</span></div>
                    </div>
                    <span className={styles.stepsArrow}><StepsArrowIcon /></span>
                    <div className={clsx(styles.tabsBtn, selectedInstantPricingStep === 3 && styles.tabSelected, maxStepsEnabled>=3 && styles.activeTab)} tab="steps3"
                        onClick={() => { goToStep(3) }}
                    >
                        <div className={styles.stepsContent}>STEP 3<span>Purchase<br />Items</span></div>
                    </div>
                </div>
            </div>
            <div className={clsx(styles.instantPurchasingMain,selectedInstantPricingStep === 4 ? styles.orderConfirmationStep : '')}>
                <div className={styles.tabContent}>
                    {selectedInstantPricingStep === 1 ?
                        <InstantPurchasingSteps1
                            register={register}
                            control={control}
                            setValue={setValue}
                            getValues={getValues}
                            watch={watch}
                            setDeliveryStateFocusEnable={setDeliveryStateFocusEnable}
                            errors={errors}
                            setErrorMessage={setErrorMessage}
                            setSelectedInstantPricingStep={setSelectedInstantPricingStep}
                            setMaxStepsEnabled={setMaxStepsEnabled}
                            isValid={isValid}
                            saveUserActivity={saveUserActivity}
                            deliveryDates={deliveryDates}
                            disableDeliveryDate={disableDeliveryDate}
                            deliveryDateMap={deliveryDateMap}
                        />
                        : selectedInstantPricingStep === 2 ?
                            <InstantPurchasingSteps2
                                control={control}
                                register={register}
                                setValue={setValue}
                                setError={setError}
                                watch={watch}
                                getValues={getValues}
                                userData={userData}
                                errors={errors}
                                trigger={trigger}
                                clearErrors={clearErrors}
                                isValid={isValid}
                                quantitySizeValidator={quantitySizeValidator}
                                updateLineItem={updateLineItem}
                                pricePerUnitChangeHandler={pricePerUnitChangeHandler}
                                calculateMaterialTotalPrice={calculateMaterialTotalPrice}
                                hideHeader={hideHeader}
                                setHideHeader={setHideHeader}
                                removeLineItem={removeLineItem}
                                sessionId={sessionId}
                                setSelectedInstantPricingStep={setSelectedInstantPricingStep}
                                setMaxStepsEnabled={setMaxStepsEnabled}
                                saveUserLineActivity={saveUserLineActivity}
                            />
                            : selectedInstantPricingStep === 3 ?
                                <InstantPurchasingSteps3
                                    reset={reset}
                                    watch={watch}
                                    getValues={getValues}
                                    control={control}
                                    handleSubmit={handleSubmit}
                                    setValue={setValue}
                                    setTodayDate={setTodayDate}
                                    setErrorMessage={setErrorMessage}
                                    setOrderConfirmationData={setOrderConfirmationData}
                                    setSelectedInstantPricingStep={setSelectedInstantPricingStep}
                                    setDisableCloseAnalytic={setDisableCloseAnalytic}
                                    setCreatePoData={setCreatePoData}
                                    paymentMethods={paymentMethods}
                                    todayDate={todayDate}
                                    sendInvoicesToEmailData={sendInvoicesToEmailData}
                                    setHideHeader={setHideHeader}
                                    removeLineItem={removeLineItem}
                                    sessionId={sessionId}
                                    saveUserActivity={saveUserActivity}
                                    isValid={isValid}
                                    errors={errors}
                                    resaleCertificateData={resaleCertificateData}
                                    userData={userData}
                                    router={router}
                                    buyingPreference={buyingPreference}
                                    setUserDetailForPurchase={setUserDetailForPurchase}
                                    calculateTotalPricing={calculateTotalPricing}
                                    calculateDepositAmount={calculateDepositAmount}
                                    paymentMethodChangeHandler={paymentMethodChangeHandler}
                                    setBuyingPreference={setBuyingPreference}
                                    setDefaultPaymentOption={setDefaultPaymentOption}
                                    referenceData={referenceData}
                                />
                                : selectedInstantPricingStep === 4 &&
                                <OrderConfirmation
                                    poNumber={orderConfirmationData?.poNumber}
                                    jobNumber={orderConfirmationData?.jobNumber}
                                    sendInvoicesTo={orderConfirmationData?.sendInvoicesTo}
                                    selectedOptionPayment={orderConfirmationData?.selectedOptionPayment}
                                    setSelectedInstantPricingStep={setSelectedInstantPricingStep}
                                    instantPurchansingInit={instantPurchansingInit}
                                    setHideHeader={setHideHeader}
                                />
                    }
                </div>
            </div>
        </>
    );
}

function InstantPurchasing() {
    const router = useIonRouter();

    const navigateToSettings = () => {
        router.push("/setting",{animate:true});
    }
    return (
        <IonPage>
            <IonContent>
                <InstantPurchasingWrapper
                navigateToSettings = {navigateToSettings}>
                    < InstantPurchasingStepper />
                </InstantPurchasingWrapper>
            </IonContent>
        </IonPage>
    )
}
export default InstantPurchasing;[]