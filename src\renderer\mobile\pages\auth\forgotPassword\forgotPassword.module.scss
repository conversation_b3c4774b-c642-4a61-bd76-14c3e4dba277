.resetPassword {
  border-radius: 10px 0px 0px 10px;
  padding: 90px 24px 0px 24px;
  display: flex;
  align-items: center;
  flex-direction: column;
  height: 100%;

  .resetPassword1{
    justify-content: center;
  }

  .resetPasswordContent{
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: flex-start;
  }

  .bryzosLogo{
    margin-bottom: 40px;
 }

  .titleText {
    font-family: Noto Sans Display;
    font-size: 18px;
    font-weight: 300;
    line-height: 1.11;
    letter-spacing: -0.5px;
    text-align: center;
    color: #fff;
    margin-top: 0px;
    margin-bottom:20px;
 }


  .emailDiv {
    width: 100%;
    position: relative;
    margin-top: 20px;
    .showPassBtn{
      display: flex;
      position: absolute;
      top: 50%;
      right: 10px;
      transform: translateY(-50%)
    }

    input {
      width: 100%;
      height: 40px;
      font-family: <PERSON><PERSON> Sans Display;
      font-size: 14px;
      font-weight: 300;
      line-height: 1.4;
      color: #fff;
      padding: 10px 12px 10px 12px;
      border-radius: 4px;
      -webkit-backdrop-filter: blur(71.7px);
      backdrop-filter: blur(71.7px);
      border: solid 0.5px rgba(255, 255, 255, 0.7);
      background-color: transparent;
      &::placeholder{
        color: #fff;
      }
      &:focus{
        outline: none;
      }
    }

    .errorText {
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.6;
      text-align: left;
      color: #f00;
    }

    .errorInput{
      border: 1px solid #ff0000;
    }
  
  }
  .sendOtpBtn {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-top: 16px;
    gap: 12px;

    &.loginBtnBtm{
      margin-top: auto;
    }

    .pressBtn {
      font-family: Noto Sans Display;
      font-size: 16px;
      font-weight: normal;
      line-height: 1.4;
      text-align: center;
      background: transparent;
      border: none;
      cursor: pointer;
      color: rgb(112, 255, 0);
      &[disabled]{
          color: rgba(255, 255, 255, 0.5);
          cursor: not-allowed;
      }
    }

    .loginBtn {
      font-family: Noto Sans Display;
      font-size: 16px;
      font-weight: normal;
      line-height: 1.4;
      text-align: center;
      background: transparent;
      border: none;
      cursor: pointer;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 16px;
    }

    .resendOtp {
      font-family: Noto Sans Display;
      font-size: 14px;
      line-height: 1.6;
      text-align: left;
      color: #fff;
      background: transparent;
      border: none;
      cursor: pointer;
    }
  }
}


.passwordErrorContainer {
  position: relative;
  width: 100%;

  .errorBorder {
    position: absolute;
    right: -13px;
    top: 39px;
    border-radius: 0px 3px 3px 0px;
    border: solid 1px #f00;
    width: 13px;
    height: 63px;
    border-left: 0;
    display: flex;
    align-items: center;

      svg {
          position: relative;
          right: -6px;
          width: 12px;
          height: 12px;
      }
  }
}