/// <reference types="@capacitor/local-notifications" />

import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'bryzos.extended.pricing.widget',
  appName: 'Bryzos',
  webDir: 'build',
  server: {
    androidScheme: 'https'
  },
  plugins: {
    FileDownloadPlugin:{},
    LocalNotifications: {
      // smallIcon: "ic_stat_icon_config_sample",
      // iconColor: "#488AFF",
      // sound: "beep.wav",
    },
    CapacitorCookies: {
      enabled: true,
    },
   
    CapacitorUpdater: {
      autoUpdate: false,
      directUpdate: false,
      allowModifyUrl: true,
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    }
  },
};

export default config;
