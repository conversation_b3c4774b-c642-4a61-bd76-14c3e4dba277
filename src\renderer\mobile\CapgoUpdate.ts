import { axios, raygunKeys } from "@bryzos/giss-ui-library";
import { CapacitorUpdater } from "@capgo/capacitor-updater";
import packageData from "../../../package.json";
import { dispatchRaygunError } from "./library/helper";
import { capgoUpdateErrorMessages, splashScreenConst } from "./library/common";
import { useSplashScreenStore } from "./library/stores/splashScreenStore";

export const downloadAndUpdateBundle = async () => {
    const store = useSplashScreenStore.getState()
    try {
        const latest = await CapacitorUpdater.getLatest();
        if (latest.version === packageData.version) {
            store.setUpdatesFound(false)
            return;
        }
        CapacitorUpdater.addListener('download', (downloadEvent) => {
            console.log(downloadEvent.percent)
            store.setCapgoUpdateDisplayText(splashScreenConst.downloadingUpdates)
            store.setCapgoUpdateProgess(downloadEvent.percent)
            console.log(JSON.stringify(downloadEvent.bundle))
        })
        if (latest?.url) {
            const data = await CapacitorUpdater.download({
                'url': latest.url,
                'version': latest.version,
                'sessionKey': latest.sessionKey,
                'checksum': latest.checksum,
            });
            if (data) {
                await CapacitorUpdater.set({ id: data.id });
            }
        }else{
            store.setCapgoUpdateDisplayText(splashScreenConst.noUpdatesFound)
        }
    } catch (error) {
        if(!error?.message?.toLowerCase().includes(capgoUpdateErrorMessages.noUpdateNeeded.toLowerCase())){
            dispatchRaygunError(error, [raygunKeys.capgoUpdate.tag]);
        }
    } finally {
        store.setUpdatesFound(false)
    }
}

const oldFlow = async() => {
    CapacitorUpdater.notifyAppReady();
    const getChannel = await CapacitorUpdater.getChannel();
    if (getChannel.channel !== import.meta.env.VITE_CAPGO_CHANNEL) {
        await CapacitorUpdater.setChannel({ channel: import.meta.env.VITE_CAPGO_CHANNEL, triggerAutoUpdate: import.meta.env.VITE_ENVIRONMENT === "prod" });
    }
    if (import.meta.env.VITE_ENVIRONMENT === "prod") {
        return;
    }
    await downloadAndUpdateBundle();
}

export const liveUpdate = async () => {
    const store = useSplashScreenStore.getState()
    try {
        store.setUpdatesFound(true)
        store.setCapgoUpdateDisplayText(splashScreenConst.checkingForUpdates)
        let capgoVersion = await CapacitorUpdater.getPluginVersion();
        if (capgoVersion.version.startsWith('5')) {
            oldFlow();
            store.setUpdatesFound(false)
            return;
        }
        let urlInfo = await axios.get(import.meta.env.VITE_API_SERVICE + '/external-affairs/mobile/selectedReleaseUrl');
        if (
            typeof urlInfo.data.data === "object" &&
            "error_message" in urlInfo.data.data
        ) {
            store.setUpdatesFound(false)
            throw new Error(urlInfo.data.data.error_message);
        }
        CapacitorUpdater.setUpdateUrl({
            url: urlInfo.data.data.url
        })
        CapacitorUpdater.notifyAppReady();
        if (urlInfo.data.data.is_capgo === true) {
            const getChannel = await CapacitorUpdater.getChannel();
            if (getChannel.channel !== import.meta.env.VITE_CAPGO_CHANNEL) {
                await CapacitorUpdater.setChannel({ channel: import.meta.env.VITE_CAPGO_CHANNEL, triggerAutoUpdate: false });
            }
        }
        await downloadAndUpdateBundle();
    } catch (error) {
        if(!error?.message?.toLowerCase().includes(capgoUpdateErrorMessages.noUpdateNeeded.toLowerCase())){
            dispatchRaygunError(error, [raygunKeys.capgoUpdate.tag]);
        }
    } finally {
        store.setUpdatesFound(false)
    }
}