
import { Auth } from "aws-amplify";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useEffect, useState } from "react";
import styles from './forgotPassword.module.scss';
import { IonContent, IonPage, useIonRouter, useIonViewWillLeave } from "@ionic/react";
import { OTPInputComponent } from "../../../components/OTPInput";
import { forgotPasswordConst, mobileRoutes, snackbarSeverityType, useGlobalStore, usePostMigrateToPassword, useGlobalSignoutApi,useGetDeviceIdApproval } from "@bryzos/giss-ui-library";
import { commomKeys } from "../../../library/common";
import useSnackbarStore from "../../../library/component/Snackbar/snackbarStore";
import { getDeviceId } from "../../../library/helper";
import { clsx } from "clsx";
import { ReactComponent as <PERSON><PERSON><PERSON>s<PERSON><PERSON> } from '../../../../assets/mobile-images/B_Logo.svg';
import { ReactComponent as CrossLogo } from '../../../../assets/mobile-images/crossBtn.svg';
import { ReactComponent as ErrorEmailIcon } from '../../../../assets/mobile-images/errorEmail.svg';
import { ReactComponent as ShowPassIcon } from '../../../../assets/mobile-images/show-pass.svg';
import { ReactComponent as HidePassIcon } from '../../../../assets/mobile-images/hide-pass.svg';
function ForgotPassword() {
  const [showPasswordInput, setShowPasswordInput] = useState(false);
  const [showError, setShowError] = useState(false);
  const[showOtp, setShowOtp] = useState(false);
  const[otp, setOtp] = useState('');
  const [email, setEmail] = useState('');
  const router = useIonRouter()
  return (
    <IonPage>
      <IonContent>
      <div className={styles.resetPassword}>
      <div className={styles.resetPasswordContent}>
          <div className={styles.bryzosLogo}>
                <BryzosLogo />
              </div>
              <h1 className={styles.titleText}>Forgot Password</h1>
        {(!otp) ?
          <EmailOtpComponent
            showPasswordInput={showPasswordInput}
            showError={showError}
            showOtp={showOtp}
            otp={otp}
            setShowPasswordInput={setShowPasswordInput}
            setShowError={setShowError}
            setShowOtp={setShowOtp}
            setOtp={setOtp}
            setEmail={setEmail}
            email={email}/> :
            <ConfirmPassword
            otp={otp}
            setOtp={setOtp}
            email={email} />
        }
        </div>
         <div className={clsx(styles.sendOtpBtn, styles.loginBtnBtm)}>
                   <button className={styles.loginBtn} onClick={() => router.push('/login',{animate:true,direction:'forward'})}>Login</button>
            </div>
            </div>
      </IonContent>
    </IonPage>
  )
}

const EmailOtpComponent = ({showPasswordInput, showError, showOtp, setShowPasswordInput, setShowError, setShowOtp, setOtp, setEmail, email}) => {
    const checkDeviceIdApproval = useGetDeviceIdApproval();
    const {
      register,
      watch,
      handleSubmit,
      setError,
      clearErrors,
      formState: { errors, isValid, submitCount },
    } = useForm({
      resolver: yupResolver(
        yup
          .object({
            email: yup
              .string()
              .matches(
                /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                {
                  message: "Please enter valid email",
                }
              )
              .required("Email is Required"),
              otp: yup.array()
              .of(yup.number().typeError('').required('req').integer('integer')).test('len', 'otp must be 6 digits', val => val?.filter(v => !isNaN(v)).length === 6),
 
          })
          .required()
      ),
      mode: "onChange",
      defaultValues:{
        email: email
      }
    });
    const {showToastSnackbar, resetSnackbarStore} = useSnackbarStore();
    const [disableSendOtp,setDisableSendOtp] = useState(false)

    useEffect(()=>{
      setShowOtp(false);
    },[])

    const handleKeydown =  (event) =>  {
      if (event.key === 'Enter') {
          setShowError(true);
          sendOtpHandler()
      }
      else{
        setShowError(false);
      }
    }

    const sendOtpHandler = async () => {
      setDisableSendOtp(true)
      if(!errors.email){
        const email = watch('email');
        checkDeviceIdApproval.mutateAsync(email).then(async(res)=>{
          if(!res.data.data.status && res.data.data.message){
            onEmailError(res.data.data.message)
            setDisableSendOtp(false)
          }else if(res.data?.data?.status){
            handleForgotPassword(email);
          }else{
            onEmailError(forgotPasswordConst.emailApprovalError)
            setDisableSendOtp(false)
          }
        })
        .catch((e)=>{
          onEmailError(commomKeys.errorContent)
          setDisableSendOtp(false)
        })
      }
      else{
        setShowError(true);
        setDisableSendOtp(false)
      }
    };

    const handleForgotPassword = async (email: string) => {
      try {
        await Auth.forgotPassword(email);
        showToastSnackbar(forgotPasswordConst.otpSent, snackbarSeverityType.success, null, resetSnackbarStore,'', 3000);
        setShowOtp(true);
        setEmail(email);
      } catch (error: any) {
        onEmailError(error.message)
      }
    }

    const onEmailError = (message: string) => {
      setShowOtp(false);
      setShowError(true);
      setError("email", { message })
    }

    const handleFocus = () => {
      setShowPasswordInput(true);
    };

    const toggleRenderer = () => {
      if(!errors?.email){
        const otp = watch('otp').join('');
        if(otp.length === 6){
          setOtp(otp);
          clearErrors();
        }
        else
        setError('otp', {message: 'Incorrect OTP'})
      }
      else{
        setShowError(true);
      }
    }
   
    return (
        
          <div className={clsx(styles.emailDiv, styles.forgetEmail)}>
            <input
              type="email"
              name="email"
              {...register("email")}
              placeholder="Enter Email"
            />
             {showOtp && <div className={clsx(styles.emailDiv,styles.forgetOtp)}>
              {!watch("otp") && !showPasswordInput ? (
                <input
                  id="inputPlaceholder"
                  placeholder="Enter OTP"
                  value=""
                  onFocus={handleFocus}
                  readOnly
                />
              ) : (
                <OTPInputComponent
                  register={register}
                 className="passwordBox"
                  autoFocus
                  inputType={"tel"}
                  registerName={"otp"}
                  disableOnPaste = {true}
                />
              )}
            <p className={clsx(styles.errorText,styles.errorForgetPass)}>{errors.otp?.message}</p>
          </div>}
            {(showError && errors.email) ? 
                <p className={styles.errorText}>{errors.email.message}</p> 
              : 
                showOtp ?
                  <div className={styles.sendOtpBtn}>
                    <button className={styles.pressBtn} onClick={()=>{toggleRenderer()}} disabled={!isValid}>Next</button>
                    <button className={styles.resendOtp} onClick={()=>{sendOtpHandler()}}>(Resend OTP)</button>
                  </div>
                :
                 (watch('email')?.length > 0) &&
                 <div className={styles.sendOtpBtn}>
                   <button className={styles.pressBtn} onClick={()=>{sendOtpHandler()}} disabled={disableSendOtp}>Send OTP</button>
                 </div>
                 
            }
          </div>
      
    );
}

const ConfirmPassword = ({otp, setOtp, email})=>{
  const changePasswordConfirmation = usePostMigrateToPassword();
  const router = useIonRouter();
  const {showToastSnackbar, resetSnackbarStore} = useSnackbarStore();
  const globalSignout = useGlobalSignoutApi();
  const { setShowLoader } = useGlobalStore();
  const [passwordVisibility, setPasswordVisibility] = useState({
    password1: true,
    password2: true,
  });
  const {
    register,
    watch,
    handleSubmit,
    setError,
    setValue,
    clearErrors,
    getValues,
    reset,
    formState: { errors, isValid, submitCount, dirtyFields },
  } = useForm({
    resolver: yupResolver(
      yup
        .object({
            newPassword: yup.string().required("Password is required")
            .test('len', 'Password must be 6 digits', function (value) {
              const { createError } = this;
              if (this.parent.newPassword && value) {
                return value.length >= 6 || createError({ message: "Password must be 6 digits" });
              }
              return true;
            }),
            confirmPassword: yup.string().test("isRequired", "Password does not match!", function (value) {
              const password = this.parent.newPassword;
              if (password.trim() === value.trim()) return true;
              return false;
          })
        })
        .required()
    ),
    mode: "onBlur",
    defaultValues: {
      newPassword: '',
      confirmPassword: ''
    }
  });

  const setNewPasswordHandler = async (data) => {
    try {
      const newPassword = data.newPassword;
      const confirmPassword = data.confirmPassword;
      if(newPassword === confirmPassword){
        setShowLoader(true);
        const res = await Auth.forgotPasswordSubmit(email, otp, confirmPassword);
        if(res === 'SUCCESS'){
          await changePasswordConfirmation.mutateAsync(email);
          const device_id:string | null = await getDeviceId();
          await globalSignout.mutateAsync({data:{email_id: email,device_id}});
          setShowLoader(false);
          showToastSnackbar(forgotPasswordConst.passwordChanged, snackbarSeverityType.success, null, resetSnackbarStore,'',3000); 
          router.push(mobileRoutes.loginPage, { animate: true, direction: 'forward' })
        }
      }else{
        setError("confirmPassword", { message: forgotPasswordConst.passwordMismatch })
        return
      }
    } catch (error: any) {
      let content = commomKeys.errorContent;
      if(error.message === forgotPasswordConst.invalidVerificationCode)
        content = forgotPasswordConst.invalidVerificationCode;
      else if(error.message === forgotPasswordConst.attemptExceeded)
        content = forgotPasswordConst.attemptExceeded;
      showToastSnackbar(content, snackbarSeverityType.alert, null, resetSnackbarStore,'',3000); 
      setOtp('');
      setShowLoader(false);
      setValue('newPassword', '')
      setValue('confirmPassword', '')
    }
  };

  const handlePasswordBlur = () => {
    const password = getValues('newPassword')?.trim();
    const confirmPassword = getValues('confirmPassword')?.trim();
    if(password.length<6 && dirtyFields.newPassword){
      setError("newPassword", { message: "Password must be 6 digits" });
      return;
    }
    if(password?.length && confirmPassword?.length){
      if (password === confirmPassword) {
        clearErrors(["newPassword", "confirmPassword"]);
      } else {
        setError("confirmPassword", { message: "Password does not match!" });
      }
    }
  }

  const togglePasswordVisibility = (field) => {
    setPasswordVisibility((prevState) => ({
        ...prevState,
        [field]: !prevState[field],
    }));
  };

  return (
    <div className={styles.passwordErrorContainer}>
     <div className={clsx(styles.emailDiv)}>
      <input type={passwordVisibility.password1 ? 'password' : 'text'} {...register("newPassword")} placeholder='Enter new password'
        onChange={(e) => {
          e.target.value = e.target.value.trim();
          register('newPassword').onChange(e);
        }}
        onBlur={(e) => {
          register("newPassword").onBlur(e);
          handlePasswordBlur()
          }}
          className={clsx((errors?.newPassword || errors?.confirmPassword) && styles.errorInput)}
      />
       <button className={styles.showPassBtn} onClick={() => togglePasswordVisibility('password1')}>
              {passwordVisibility.password1 ? <ShowPassIcon /> : <HidePassIcon />}
        </button>
      </div>
      <div className={clsx(styles.emailDiv)}>
        <input type={passwordVisibility.password2 ? 'password' : 'text'} {...register("confirmPassword")}  placeholder='Confirm password'  
          onChange={(e) => {
            e.target.value = e.target.value.trim();
            register('confirmPassword').onChange(e);
          }}
          onBlur={(e) => {
            register("confirmPassword").onBlur(e);
            handlePasswordBlur()
            }}
            className={clsx((errors?.newPassword || errors?.confirmPassword) && styles.errorInput)}
        />
         <button className={styles.showPassBtn} onClick={() => togglePasswordVisibility('password2')}>
              {passwordVisibility.password2 ? <ShowPassIcon /> : <HidePassIcon />}
        </button>
      </div>
      {(errors?.newPassword || errors?.confirmPassword )  && 
        <span className='errorText2 errorTextForget'><CrossLogo />{errors?.newPassword?.message || errors?.confirmPassword?.message}</span>
        }
      <div className={styles.sendOtpBtn}>
          <button className={clsx(styles.pressBtn)} onClick={()=>{handleSubmit(setNewPasswordHandler)()}} disabled={(Object.keys(errors).length > 0 || !isValid)}>Save</button>
      </div>
      {(errors?.newPassword || errors?.confirmPassword) &&
                            <div className={styles.errorBorder}>
                                <ErrorEmailIcon />
                            </div>
           }
    </div>
  );
} 

export default ForgotPassword;