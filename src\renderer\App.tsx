// @ts-nocheck
import React, { useContext, useEffect, useState } from 'react';
import {UserContext} from './UserContext';
import './App.scss';
import { Routes, Route, useNavigate, useLocation} from 'react-router-dom';
import Login from './pages';
import Home from './pages/home';
import Tnc from './pages/tnc';
import Success from './pages/success';
import CreatePo from './pages/buyer/createPo';
import BuyerSetting from './pages/buyer/buyerSetting';
import SellerSetting from './pages/seller/sellerSetting';
import OrderConfirmation from './pages/buyer/orderConfirmation';
import OrderConfirmationSeller from './pages/seller/orderConfirmation';
import AcceptOrder from './pages/seller/acceptOrder';
import Dispute from './pages/dispute/dispute';
import Order from './pages/dispute/order';
import {  
  channelWindowList,
    commomKeys,
    purchaseOrder,
    routes,
    snackbarMessageContent,
    snackbarSeverityType,
    userRole
} from '../common';
import { useStore } from './helper/store';
import { Amplify, Auth } from 'aws-amplify';
import axios from 'axios';
import useCognitoUser from './hooks/useCognitoUser';
import { createSocket, getSocketConnection, resetConnection } from './helper/socket';
import { VERSION_NUMBER, getChannelWindow, getNumericVersion, navigatePage, setChannelWindow, setNavigate } from './helper';
import Header from './pages/header';
import Loader from './Loader/Loader';
import rg4js from "raygun4js";
import ForgotPassword from './pages/forgotPassword';
import { useWindowEvent } from '@mantine/hooks';
import UpdatePopup from './component/UpdatePopup/updatePopup';
import MatPopup from './component/MatPopup/MatPopup';
import OnboardingWelcome from './pages/Onboarding/onboardingWelcome';
import OnboardingTnc from './pages/Onboarding/onboardingTnC';
import OnboardingDetails from './pages/Onboarding/onboardingDetails';
import OnboardingThankYou from './pages/Onboarding/onboardingThankyou';
import useGetForbiddenTooltips from './hooks/useGetForbiddenTooltips';
import ErrorBoundary from './component/Error/ErrorBoundary';
import ToastSnackbar from './component/Snackbar/ToastSnackbar';
import useSnackbarStore from './component/Snackbar/snackbarStore';

rg4js("apiKey", import.meta.env.VITE_RAYGUN_KEY);
rg4js("enableCrashReporting", true);
rg4js('setVersion', VERSION_NUMBER);
rg4js('ignore', 'web_request_timing');
rg4js('options', {
  ignore3rdPartyErrors: true
});

if (!window.electron) {
    window.electron = {
        send: () => { },
        sendSync: () => { },
        receive: ()=>{},
        handleZoom: ()=>{},
        isWeb:true
    }
}

Amplify.configure({
  Auth: {
    region: import.meta.env.VITE_AWS_COGNITO_REGION,
    userPoolId: import.meta.env.VITE_AWS_COGNITO_USER_POOL_ID,
    userPoolWebClientId: import.meta.env
      .VITE_AWS_COGNITO_USER_POOL_WEB_CLIENT_ID,
    cookieStorage: {
      domain: import.meta.env.VITE_AWS_COGNITO_DOMAIN,
      path: import.meta.env.VITE_AWS_COGNITO_PATH,
      expires: Number(import.meta.env.VITE_AWS_COGNITO_EXPIRES),
      sameSite: import.meta.env.VITE_AWS_COGNITO_SAME_SITE,
      secure: Boolean(import.meta.env.VITE_AWS_COGNITO_SECURE),
    }
  },
  Analytics: { disabled: Boolean(import.meta.env.VITE_AWS_COGNITO_ANALYTICS_DISABLED) },
});

let headerConfig = null;
let axiosReqHeader = null;
let currentUser = null;
// const maxRetries = 1;

const addTokenToRequestHeader = async(request) => {
  const user = await Auth.currentSession();
  const accessToken = user.getAccessToken();
  const jwt = accessToken.getJwtToken();
  if (request.headers.skipInterceptor !== "true") {
    request.headers.AccessToken = jwt;
  }
  return request;
}

const electronVersion = window.electron.sendSync({ channel: 'electronVersion' });
if(getNumericVersion(electronVersion)  >= getNumericVersion(commomKeys.minorElectronVersion)  ){
  const listChannelWindow = window.electron.sendSync({ channel: 'setChannelWindows' });
  setChannelWindow(listChannelWindow);
}

const addAxiosInterceptor = () => {
    axiosReqHeader = axios.interceptors.request.use(async (request) => {
    request = await addTokenToRequestHeader(request);
    headerConfig  = request.headers;
    // if(!request.retryCount)
    // request.retryCount = 0;
    return request;
  });
}
const initializeAxiosResponseInterceptor = (setForceLogout)=>{
  axios.interceptors.response.use(response => response, async (error) => {
    console.log("error",error);
    if(error.response && error.response.status === 401){
      let originalRequest = error.config;
      // console.log("originalRequest", originalRequest.retryCount);
      // if(originalRequest.retryCount >= maxRetries){
      //   setForceLogout(true);
      //   return error;
      // }
      // else{
        // originalRequest.retryCount += 1;
        // console.log("originalRequest",originalRequest.retryCount);
        try{
        const user = await Auth.currentAuthenticatedUser({ bypassCache: true });
        console.log("user",user);
        return axios(originalRequest);
        }catch(err){
          setForceLogout(true);
          return error;
        }
        // await addTokenToRequestHeader(originalRequest);
        
      // }
    }
    else
    return error;
  })
}

const removeAxiosInterceptor = () => {
  axios.interceptors.request.eject(axiosReqHeader);
  axiosReqHeader = null;
}
let systemVersion;

function App() {
    const [user, setUser] = useState({'data': null, "zip": null });
    const [openMatPopup, setOpenMatPopup] = useState(false);

    const navigate = useNavigate();
    const location = useLocation();

    const setSellerSettingsData = useStore(state => state.setSellerSettingsData);
    const forbiddenTooltips = useStore(state => state.forbiddenTooltips);

    // const { setSocket } = useStore()

    const {
      data: cognitoUser,
      isLoading: isCognitoUserLoading,
      // refetch: refetchCognitoUser,
      // error: cognitoUserError,
    } = useCognitoUser();

    const {
      refetch: getForbiddenTooltips,
      data: forbiddenTooltipsData,
    } = useGetForbiddenTooltips();

    const userContextValue =  { user, setUser, forbiddenTooltips  };

    const [socket, setSocket] = useState(null);
    // const socketContextValue =  { socket, setSocket };
    // const [connectStatus, setConnectStatus] = useState("Connect");

    // Store handlers
    // const poToBeAccepted = useStore(state => state.poToBeAccepted);
    const setPOCart = useStore(state => state.setPOCart);
    const addPipeLinePO = useStore(state => state.addPipeLinePO);
    const addNewPoToClaimToCart = useStore(state => state.addNewPoToClaimToCart);
    const removeFromPOCart = useStore(state => state.removeFromPOCart);
    const setErrorPopupDetail = useStore(state => state.setErrorPopupDetail);
    const resetHeaderConfig = useStore(state => state.resetHeaderConfig);
    const [heartBeat, setHeartBeat] = useState();
    const showLoader = useStore(state => state.showLoader);
    const setShowLoader = useStore(state => state.setShowLoader);
    const disableBryzosNavigation = useStore(state => state.disableBryzosNavigation);
    const setDisableBryzosNavigation = useStore(state => state.setDisableBryzosNavigation);
    const channelWindow = Object.keys(getChannelWindow()).length ? getChannelWindow() : channelWindowList ;
    const [forceLogout, setForceLogout] = useState(false);
    const [appVersion, setAppVersion] = useState();
    const setForbiddenTooltips = useStore(state => state.setForbiddenTooltips);
    const setNavigationStateForNotification = useStore(state => state.setNavigationStateForNotification);
    const purchaseOrdersList = useStore(state => state.ordersCart);
    const {viewedOrdersListForBadgeCount, setViewedOrdersListForBadgeCount,backdropOverlay,setBackdropOverlay} = useStore();
    const {showToastSnackbar, resetSnackbarStore, setSnackbarOpen} = useSnackbarStore();
    const [pusherId, setPusherId] = useState(0);
 
    useEffect(() => {
      const _forbiddenTooltipsData = Array.isArray(forbiddenTooltipsData) ? forbiddenTooltipsData : [];
      setForbiddenTooltips(_forbiddenTooltipsData);
  }, [forbiddenTooltipsData]);

  useEffect(() => {
    initializeAxiosResponseInterceptor(setForceLogout);
    if(channelWindow?.systemVersion){
       systemVersion = window.electron.sendSync({ channel: channelWindow.systemVersion, data: null})
    }
    if(window.electron.handleZoom)
    window.electron.handleZoom();
   
    window.electron.receive('data-channel',(...d)=>{
      console.log('data-channel', d)
    })

    if(channelWindow.handleURI){
      window.electron.receive(channelWindow.handleURI,(uri) => {
        const [protocol, path] = uri.split('//');      
        if(path){
            const isMail = path.split("/")[0] === "mail";
          if(isMail){
            openAppUsingLinkHandler(path);
          }else{
            notificationUriHandler(path);
          }
        }
      })
    }
    if(channelWindow.markAsReadNotification){
      window.electron.receive(channelWindow.markAsReadNotification,(notificationList) => {
        notificationList = JSON.parse(notificationList);
        readNotificationAsRead(notificationList);
      });
    }
    if(channelWindow.forceLogout) {
      window.electron.receive(channelWindow.forceLogout,() => {
        if(currentUser)
        setForceLogout(true);
      });
    }
    window.electron.receive(channelWindow.productReferenceChanged,() => {
      showToastSnackbar(snackbarMessageContent.productReferenceDataChanged, snackbarSeverityType.warning, [{name: commomKeys.refresh, handler: handleSnackbarClose}], resetSnackbarStore);
    });
    window.electron.receive(channelWindow.getAccessToken,(channelId) => {
      console.log("channelName",channelId);
      setPusherId(channelId);
    })
    setNavigate(navigate)
    // Here, you can call your authentication endpoint to get the user's credentials
    // const userCredentials = { username: 'example', password: 'password' };

    // Once you have the user's credentials, you can use them to connect to the Socket.io server
    // const socket = io('http://localhost:3100', {
    //   auth: {
    //     ...userCredentials
    //   }
    // });
 
    // // Listen for 'message' events from the server
    // socket.on('message', (data) => {
    //     console.log('data', data)
    // //   setMessages((messages) => [...messages, data]);
    // });

    // // Save the socket object in state
    // setSocket(socket);

    // // Clean up the socket object on unmount
    return () => {
      socket?.disconnect();
      setSocket(null);
      if(heartBeat){
        clearInterval(heartBeat);
        setHeartBeat(null);
      }
    };
  }, []);

  useEffect(() => {
    let badgeCount = 0;
    if(viewedOrdersListForBadgeCount){
      const viewedOrderSet = new Set([...viewedOrdersListForBadgeCount]);
      badgeCount = purchaseOrdersList.reduce((count, order) => {
        if(!viewedOrderSet.has(order.buyer_po_number) && order.claimed_by === purchaseOrder.readyToClaim) count++;
        return count;
      }, 0);
    }
    if(channelWindow.badgeCountHandler)
    window.electron.send({ channel: channelWindow.badgeCountHandler, data:{type: 'set', count:badgeCount }});
  }, [purchaseOrdersList, viewedOrdersListForBadgeCount]);

  useEffect(() => {
    if(channelWindow?.sticky){
      const electronVersion = window.electron.sendSync({ channel: channelWindow.sticky, data: "isNewVersionCheck" });
      setOpenMatPopup(!window.electron.isWeb && electronVersion!=="Not 1st app release");
      if(electronVersion === "Not 1st app release") setAppVersion(window.electron.sendSync({ channel: channelWindow.electronVersion }));
    }
  }, [location.pathname]);

  //use this effect for cleanup after logging off
  useEffect(() => { 
    if(resetHeaderConfig){
      removeAxiosInterceptor();
      headerConfig=null;
      currentUser=null;
      if(heartBeat){
        clearInterval(heartBeat);
        setHeartBeat(null);
      }
      setForceLogout(false);
    }
  }, [resetHeaderConfig]);

  useEffect(() => {
    if(pusherId && channelWindow.refreshPrivateChannel && cognitoUser){
      window.electron.send({ channel: channelWindow.refreshPrivateChannel, data:{ channelId: pusherId, accessToken: cognitoUser.signInUserSession.accessToken.jwtToken}});
      setPusherId(null);
    }
  },[pusherId])

  const notificationUriHandler = (path) => {
      //const [protocol, path] = uri.split('//');     
      let [userId, notificationId, routePath, stateUrl] = path.split('/');
      if((+userId) === (+currentUser.id)){
        if(routePath)
        routePath = `/${routePath}`;
        const state = {};
        if(stateUrl){
          const stateUrlArray = stateUrl.split(',');
          for(const element of stateUrlArray){
            const [key, value] = element.split('=');
            state[key] = value;
          };
        }
        let isValidRoutePath = false;
        for(const routeKey in routes){
          if(!isValidRoutePath && routes[routeKey] === routePath) isValidRoutePath=true;
        }
        if(isValidRoutePath){
          setNavigationStateForNotification(state);
          navigatePage(location.pathname, {path:routePath})
        }
        // readNotificationAsRead([{notificationId}], false);
      }
      else{
        console.log(`Notication for User: ${userId} but found currentUser:${currentUser.id}`);
      }
  }

  const readNotificationAsRead = (notificationList) => {
    const payload = {data:{notification_id: []}};
    for(const notification of notificationList){
      payload.data.notification_id.push(notification.notificationId);
    }
    if(currentUser){
      axios.post(import.meta.env.VITE_API_NOTIFICATION_SERVICE + '/notification/markAsRead', payload)
      .then(res => {
        if(channelWindow?.isMarkAsReadNotification)
        window.electron.send({ channel: channelWindow?.isMarkAsReadNotification, data:true })
      })
      .catch(err => {
        if(channelWindow?.isMarkAsReadNotification)
        window.electron.send({ channel: channelWindow?.isMarkAsReadNotification, data:false })
      });
    }
  }

  const openAppUsingLinkHandler = (path) => {
    if(path.indexOf("mail/create-po") >= 0){
      if(currentUser?.type === userRole.buyerUser)
      navigate("/create-po");

    }else if(path.indexOf("mail/buyer-setting") >= 0){
      if(currentUser?.type === userRole.buyerUser)
      navigate("/buyer-setting");
    }else{

    }
  }

  const handleSnackbarClose = async (event, reason) => {
    setSnackbarOpen(false);
    try {
        await axios.get(import.meta.env.VITE_API_SERVICE + '/reference-data/homepage');
        resetSnackbarStore();
        if(channelWindow.refreshApp)
        window.electron.send({ channel: channelWindow.refreshApp });
    } catch (error) {
        setSnackbarOpen(true);
    }
  };

  const showOverlayToaster = (message, severity, buttons, closeHandler, showOverlay)=>{
    showToastSnackbar(message, severity, buttons, closeHandler);
    if(showOverlay)
    setBackdropOverlay(true);
  }

  const socketConnectionErrorHandler = (message) => {
    showOverlayToaster(message, snackbarSeverityType.alert, [{name:commomKeys.tryAgain, handler: handleSnackbarClose}], null, true);
  }
  const removeSocketDisconnectToaster = ()=>{
    resetSnackbarStore();
    setBackdropOverlay(false);
  }

  async function createSocketConnection(userData){
    const extraHeaders= {
      "gissToken": import.meta.env.VITE_WEB_SOCKET_GISS_TOKEN,
      "email": userData.email_id,
      "accessToken":cognitoUser.signInUserSession.accessToken.jwtToken
    };
    const newSocket = createSocket(import.meta.env.VITE_WEB_SOCKET_SERVER,extraHeaders, socketConnectionErrorHandler, removeSocketDisconnectToaster);
    // const newSocket = io(process.env.WEB_SOCKET_SERVER,{
    //   transports: ["polling", "websocket"],
    //     transportOptions: {
    //       polling: {
    //         extraHeaders,
    //       },
    //     },
    //   }
    // );
    
    // newSocket.on("connect", () => {
    //   console.log("connected to the server");
    // });

    // newSocket.on("disconnect", () => {
    //   console.log("connected to the server");
    // });
      
    newSocket.on('poAccepted', (data) => {
      console.log('---poAccepted---');
      console.log(data.email_id);
      let isOrderConfirmed = data.email_id === userData.email_id;
      console.log(userData.email_id);
      removeFromPOCart(data.buyer_po_number, isOrderConfirmed);
      // removeOrderFromBadgeCount(data.buyer_po_number);
    });
    newSocket.on('authenticationUserStatus', (data) => {
      console.log('---authenticationUserStatus---');
      console.log(data.po_data);
      setPOCart(data.po_data);
    });
    newSocket.on('newPoAdded', (data) => {
      console.log('---newPoAdded---');
      console.log(data);
      addPipeLinePO(data);
    });
    newSocket.on('error', (data) => {
      console.log('---error---');
      console.log(data);
      setErrorPopupDetail(data)
    });
    newSocket.on('newPoToClaim', (data) => {
      console.log('---newPoToClaim---');
      console.log(data);
      addNewPoToClaimToCart(data);
      // addOrderToListForBadgeCount(data.buyer_po_number);
    });
    newSocket.on('accessTokenInvalid', (data) => {
      console.log('---accessTokenInvalid---');
      socketConnectionErrorHandler(snackbarMessageContent.socketAuthExpiryMessage);
      // setForceLogout(true);
    });
    const hb = setInterval(() => {
      const date = new Date();
      console.log('---Heartbeat('+date.getHours()+':'+date.getMinutes()+':'+date.getSeconds()+')---');
      newSocket.emit('heartbeat', {
        "socketId": newSocket.id,
        ...extraHeaders
      });
    }, 45 * 1000);

    newSocket.on('reconnect', () => {
      console.log("----reconnect-----");
      newSocket?.disconnect();
      console.log("currentUser",currentUser);
      if(hb){
        clearInterval(hb);
      }
      resetConnection();
      createSocketConnection(userData);
      // getSocketConnection().connect();
    });
    
    setHeartBeat(hb);
    setSocket(newSocket);
  }

  const clearSocketEvents = () => {
    if(socket){
      socket.off('poAccepted');
      socket.off('authenticationUserStatus');
      socket.off('newPoAdded');
      socket.off('error');
      socket.off('newPoToClaim');
      socket.off('forceLogout');
      socket.off('reconnect');
    }
    // clear all other events that you want to remove
  }

  function getRefenceData(user){
    axios.get(import.meta.env.VITE_API_SERVICE + '/reference-data').then(response => {
      const referenceData = response.data;
      getAllProductsData(user, referenceData)
    })
  }

  const getProductMapping = (productList) => {
    const productMap = {};
    try {
    productList.forEach((product) => {
      productMap[product.id] = product;
    });
    return productMap;
    } catch (e) {}
  }

  function getAllProductsData(user, referenceData){
    axios.get(import.meta.env.VITE_API_SERVICE + '/reference-data/getAllProducts').then(response => {
        const currentTandC = user.current_tnc_version;
        const acceptedTandC = user.accepted_terms_and_condition;
        const getAllProducts = response.data.data;

        userContextValue.setUser({ "data": user, "referenceData": referenceData, "allProductsData" : getAllProducts, "productMapping": getProductMapping(getAllProducts) });
        if (location.pathname !== routes.newUpdate) {
          if (currentTandC !== acceptedTandC || currentTandC === null || acceptedTandC === null) {
            navigate(routes.TnCPage, {state : { isViewMode: false, navigateTo: routes.homePage }});
          } else {
            setDisableBryzosNavigation(false);
              navigate(routes.homePage);
          }
        };
    })
  }

  function clickOnEnterSession(emailData) {
    let payload = {
      data:  { "email_id": emailData }
    }
    if(channelWindow?.systemVersion){
      
      payload.data.os_version = systemVersion;
      payload.data.last_login_app_version = appVersion;
    }
    axios.post(import.meta.env.VITE_API_SERVICE + '/user/login', payload).then(response => {
      let userDetail = response.data.data;
      const userExists = userDetail.id;
      if (userExists) {
        // navigate to next page
        if(getNumericVersion(appVersion) >= getNumericVersion(commomKeys.electronWindowsStoreAvailableVersion) && channelWindow?.windowStore)
        window.electron.send({ channel: channelWindow.windowStore, data: 'set'})
        getRefenceData(userDetail);
        if(userDetail.type === userRole.sellerUser){
          clearSocketEvents();
          createSocketConnection(userDetail);
        }
        currentUser = userDetail;
        if(channelWindow?.pusher)
        window.electron.send({ channel: channelWindow.pusher, data:{user: userDetail, pusherId:userDetail.pusher_id, emailId: emailData, accessToken: cognitoUser.signInUserSession.accessToken.jwtToken} });
        axios.get(import.meta.env.VITE_API_NOTIFICATION_SERVICE + '/notification/getAllUnreadNotification ', payload)
        .then(res => {
            if(res.data.data){
              const _notificationList = res.data.data.notificationList;
              if( channelWindow?.showNotification && _notificationList && Array.isArray(_notificationList) )
              window.electron.send({ channel: channelWindow.showNotification, data:_notificationList });
              setUserJsonData(res.data.data);
            }
        })
        .catch(err => console.error(err));
      } 
    })
    .catch(error => { 
        setShowLoader(false);
    });

}

const setUserJsonData = (jsonData) => {
  const badgeCountReadyToClaimList = jsonData.viewedOrdersListForBadgeCount
  if( badgeCountReadyToClaimList ) {
    setViewedOrdersListForBadgeCount(JSON.parse(badgeCountReadyToClaimList));
  }
}

useEffect(() => {
  if (userContextValue?.user?.data?.id && userRole.sellerUser === userContextValue?.user?.data?.type) {
    axios
      .get(import.meta.env.VITE_API_SERVICE + "/user/sellingPreference", {
        headers: {
          UserId: userContextValue.user.data.id,
        },
      })
      .then((response) => {
        if (response.data && response.data.data) {
          if (
            typeof response.data.data === "object" &&
            "err_message" in response.data.data
          ) {
            setSellerSettingsData(null);
          } else {
            const sellerSettingData = response.data.data;
            if(sellerSettingData?.company_name &&
              sellerSettingData?.company_address_line1 && 
              sellerSettingData?.company_address_city && 
              sellerSettingData?.company_address_state_id && 
              sellerSettingData?.company_address_zip && 
              sellerSettingData?.first_name && 
              sellerSettingData?.last_name && 
              sellerSettingData?.email_id && 
              sellerSettingData?.phone ){
              setSellerSettingsData(true);
            }else{
              setSellerSettingsData(null)
            }
          }
        } else {
          setSellerSettingsData(null);
        }
      })
      .catch(() => {
        setSellerSettingsData(null);
      });
  }
}, [userContextValue?.user?.data]);

useEffect(() => {
  if(!isCognitoUserLoading && cognitoUser && location.pathname !== routes.newUpdate){
    addAxiosInterceptor();
    getForbiddenTooltips();
    clickOnEnterSession(cognitoUser.attributes.email)
    cognitoUser.getUserAttributes((err, cognitoUserAttributes) => {
      if (cognitoUserAttributes?.length) {
        try {
          let user = { firstName: null, lastName: null, email: null };
          cognitoUserAttributes.forEach(cognitoUserAttribute => {
            const obj = cognitoUserAttribute.toJSON();
            if (obj?.Name === "custom:firstname") {
              user.firstName = obj.Value;
            } else if (obj?.Name === "custom:lastname") {
              user.lastName = obj.Value;
            } else if (obj?.Name === "email") {
              user.email = obj.Value;
            }
          });
          rg4js('setUser', {
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
          });
            // throw new Error("Dummy error for testing raygun setup ")
        } catch (err) {
            // rg4js('send', {
            //   error: err,
            //   tags: [commonRaygunError]
            // })
        }
      }
    });
}
}, [cognitoUser, isCognitoUserLoading])

useEffect(()=>{
  if(!isCognitoUserLoading && !cognitoUser && location.pathname !== routes.newUpdate && appVersion){
    let showLoginPage = true;
    if(getNumericVersion(appVersion) >= getNumericVersion(commomKeys.electronWindowsStoreAvailableVersion) && channelWindow?.windowStore)
      showLoginPage = window.electron.sendSync({ channel: 'windowStore', data: 'check'})
    if(!showLoginPage){
      navigate(routes.onboardingWelcome)
    }else{
      navigate(routes.loginPage)
    }
  }
},[cognitoUser,  isCognitoUserLoading, appVersion])

const openUpdateLink = ()=>{
  window.open(import.meta.env.bryzosWebUrl);
  setTimeout(()=>{
    if(channelWindow?.close)
    window.electron.send({ channel: channelWindow.close })
  },0);
}

if(isCognitoUserLoading ||
  (location.pathname !== routes.onboardingWelcome 
    && location.pathname !== routes.onboardingDetails 
    && location.pathname !== routes.onboardingTnc
    && location.pathname !== routes.onboardingThankYou
    && location.pathname !== routes.loginPage 
    && location.pathname !== routes.newUpdate 
    && !userContextValue.user?.data)){
  return <>
  </>
  }

return ( 
    <UserContext.Provider value={userContextValue} >
      {backdropOverlay && 
        <>
          <div className='backdropOverlay'></div>
        </>
      }
      <div className={`widgetCanvas blurBg ${getNumericVersion(appVersion) < getNumericVersion(commomKeys.minorElectronVersion)  ? 'bgBackgroundColor' : ''} ${window.electron.isWeb ? 'webBackground' : ''} ${backdropOverlay ? 'isbackdropOverlay' : ''}` }>
      <ErrorBoundary>
        {location.pathname !== routes.onboardingWelcome && 
          <Header disableBryzosNavigation={disableBryzosNavigation} forceLogout={forceLogout} setForceLogout={setForceLogout} appVersion={appVersion}/>
      }
        <div className='bgImg'>
        {location.pathname !== routes.newUpdate &&
          <ToastSnackbar />
        }
          {
            showLoader &&
            <div className={`loaderContent ${userContextValue.user.data !== null ? 'loaderMain' : 'loginLoader'}`}>
              <Loader />
            </div>
          }
          <div className={` ${location.pathname !== routes.loginPage ? 'headerPanel commonHeader' : `loginBody ${window.electron.isWeb ? 'webLoginHeight' : ''}`} ${showLoader && 'loaderRunning'}`}>
            <Routes>
                <Route exact path={routes.onboardingWelcome} element={<OnboardingWelcome />} />
                <Route path={routes.onboardingDetails} element={<OnboardingDetails />} />
                <Route path={routes.onboardingTnc} element={<OnboardingTnc />} />
                <Route path={routes.onboardingThankYou} element={<OnboardingThankYou />} />
                <Route path={routes.loginPage} element={<Login />} />
                <Route exact path={routes.forgotPassword} element={<ForgotPassword />} />
                <Route path={routes.homePage} element={<Home />} />
                <Route path={routes.TnCPage} element={<Tnc />} />
                <Route path={routes.successPage} element={<Success />} />
                <Route path={routes.buyerSettingPage} element={<BuyerSetting />} />
                <Route path={routes.sellerSettingPage} element={<SellerSetting />} />
                <Route path={routes.createPoPage} element={<CreatePo />} />
                <Route path={routes.orderConfirmationPage} element={<OrderConfirmation />} />
                <Route path={routes.acceptOrderPage} element={<AcceptOrder />} />
                <Route path={routes.disputePage} element={<Dispute />} />
                <Route path={routes.orderPage} element={<Order />} />
                <Route path={routes.newUpdate} element={<UpdatePopup />} />
              </Routes>
          </div>
        </div>
        <MatPopup open={openMatPopup}
          popupClass={{
            root: 'UpdateButton',
            paper: 'dialogContent'
          }}>
          <div className='excitingNews'>
            <label>Exciting News! </label>
            <p>We've added auto-updates to the app. Please uninstall your current app, then download the latest version from <span onClick={openUpdateLink}>here</span>. After this one-time manual update, all future updates will be automatic. Thanks for your support!</p>
          </div>
        </MatPopup>
      </ErrorBoundary>
    </div>
    </UserContext.Provider>
);
}
  
export default App;