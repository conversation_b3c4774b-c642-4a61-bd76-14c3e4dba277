import{bP as f,bQ as b}from"./vendor-96deee04.js";/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const _=n=>{if(f===void 0)return;let o=0,t,s,a;const R=n.getBoolean("animated",!0)&&n.getBoolean("rippleEffect",!0),d=new WeakMap,u=()=>{a&&clearTimeout(a),a=void 0,t&&(m(!1),t=void 0)},h=e=>{t||e.button===2||p(g(e),e)},w=e=>{p(void 0,e)},p=(e,i)=>{if(e&&e===t)return;a&&clearTimeout(a),a=void 0;const{x:c,y:r}=b(i);if(t){if(d.has(t))throw new Error("internal error");t.classList.contains(l)||v(t,c,r),m(!0)}if(e){const A=d.get(e);A&&(clearTimeout(A),d.delete(e)),e.classList.remove(l);const T=()=>{v(e,c,r),a=void 0};L(e)?T():a=setTimeout(T,S)}t=e},v=(e,i,c)=>{if(o=Date.now(),e.classList.add(l),!R)return;const r=C(e);r!==null&&(E(),s=r.addRipple(i,c))},E=()=>{s!==void 0&&(s.then(e=>e()),s=void 0)},m=e=>{E();const i=t;if(!i)return;const c=D-Date.now()+o;if(e&&c>0&&!L(i)){const r=setTimeout(()=>{i.classList.remove(l),d.delete(i)},D);d.set(i,r)}else i.classList.remove(l)};f.addEventListener("ionGestureCaptured",u),f.addEventListener("pointerdown",h,!0),f.addEventListener("pointerup",w,!0),f.addEventListener("pointercancel",u,!0)},g=n=>{if(n.composedPath!==void 0){const o=n.composedPath();for(let t=0;t<o.length-2;t++){const s=o[t];if(!(s instanceof ShadowRoot)&&s.classList.contains("ion-activatable"))return s}}else return n.target.closest(".ion-activatable")},L=n=>n.classList.contains("ion-activatable-instant"),C=n=>{if(n.shadowRoot){const o=n.shadowRoot.querySelector("ion-ripple-effect");if(o)return o}return n.querySelector("ion-ripple-effect")},l="ion-activated",S=100,D=150;export{_ as startTapClick};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
