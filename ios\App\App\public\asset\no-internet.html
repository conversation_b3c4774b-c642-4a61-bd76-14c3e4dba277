<!DOCTYPE html>
<html>

<head>
  <title>No Internet Access</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      -webkit-user-select: none;
      /* Safari */
      -ms-user-select: none;
      /* IE 10 and IE 11 */
      user-select: none;
      /* Standard syntax */
      font-family: 'Noto Sans', sans-serif;
    }

    html,
    body,
    .container {
      height: 100%;
    }

    .container {
      display: flex;
      flex-direction: column;
      padding-top: 20vw;
      align-items: center;
    }

    .no_internet_img {
      width: 20vw;
      height: 20vw;
      margin: 0 0 4vw 0;
    }

    .try_again_btn {
      padding: 1.5vw 2.5vw;
      border-radius: 4px;
      background-color: #70ff00;
      border: none;
      outline: none;
      font-size: 2vw;
      cursor: pointer;
    }

    .content {
      font-size: 2vw;
      font-weight: 300;
      margin: 0 0 4vw 0;
      color: #fff;
      opacity: 0.7;
    }

    .header {
      font-size: 4vw;
      color: #fff;
      margin: 0;
      /* -webkit-app-region: drag; */
    }

    .no-internet-btn {
      position: absolute;
      top: 1.3vw;
      right: 3vw;
    }

    #minimize-app-button {
      margin-right: 2vw;
    }

    .no-internet-btn button {
      background-color: transparent;
      border: 0px;
      box-shadow: none;
      display: inline-block;
      transition: all 0.1s;
      cursor: pointer;
      width: 4vw;
      height: 4vw;
    }

    .no-internet-btn button img {
      width: 4vw;
      height: 4vw;
    }

    .no-internet-btn button:hover {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
    }

    .drag-panel {
      -webkit-app-region: drag;
      width: 80%;
      height: 5vw;
      display: inline-flex;
      position: absolute;
      left: 1px;
      top: 1px;
    }
  </style>
</head>

<body>
  <span class="drag-panel"></span>
  <span class="no-internet-btn">
    <button id="minimize-app-button">
      <image src="./Minimize_App.svg">
    </button>
    <button id="close-button">
      <image src="./Icon_Close.svg">
    </button>
  </span>
  <div class="container">
    <img class="no_internet_img" src="./No_Internet_Connection.svg" alt="No Internet Connection" />
    <p class="header">No Internet Connection</p>
    <p class="content">Please check your internet connection</p>
    <button class="try_again_btn" id="try-again-button">Try Again</button>
  </div>
  <script>
    const tryAgainButton = document.getElementById('try-again-button');
    tryAgainButton.addEventListener('click', () => {
      // Notify the main process to reload the window
      console.log("window.electron", window.electron);
      if (window.electron)
        window.electron.send({ channel: 'reload-window' });
    });

    const minimizeAppButton = document.getElementById('minimize-app-button');
    minimizeAppButton.addEventListener('click', () => {
      if (window.electron)
        window.electron.send({ channel: 'windowMinimize' });
    });

    const closeButton = document.getElementById('close-button');
    closeButton.addEventListener('click', () => {
      if (window.electron)
        window.electron.send({ channel: 'windowClose' });
    });

  </script>
</body>

</html>