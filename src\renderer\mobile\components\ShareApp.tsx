// @ts-nocheck
import styles from './ShareApp.module.scss'
import React, { useEffect, useState } from 'react';
import axios from 'axios';

import { validateEmailWithSuffix } from '../library/helper';
import { mobileDiaglogConst, useGlobalStore } from '@bryzos/giss-ui-library';
import { ReactComponent as CheckIcon } from '../../assets/mobile-images/check_icon.svg';
import useDialogStore from './Dialog/DialogStore';

type Props = {
  singleProduct: any | undefined;
  isDialogTypeShareApp?: true | undefined;
  selectedProducts: any | undefined;
  onCancel: () => void;
  selectedOption: string;
  sessionId: string;
  dataOpticsApi1: (payload: any) => void;
  shareProductPricing: (emailTo: string, emailContent: string) => Promise<void> | undefined;
}
const ShareApp: React.FC<Props> = ({isDialogTypeShareApp, shareProductPricing, ...props}) => {

    const [emailData, setEmailData] = useState('');
    const [emailError, setEmailError] = useState(' ');
    const [textarea, setTextarea] = useState('');
    const [isSendInProgress, setIsSendInProgress]= useState(false);
    const userData = useGlobalStore(state => state.userData);
    const [isMailSent,setIsMailSent] = useState(false);
    const [shareProductsLength, setShareProductsLength] = useState<string>('');
    const { showCommonDialog, resetDialogStore } = useDialogStore();

    useEffect(()=>{
      let products = [];
      if (props.singleProduct?.length) {
        products = props.singleProduct;
      } else if (props.selectedProducts?.length) {
        products = props.selectedProducts;
      }

      if (products?.length) {
        setShareProductsLength(products.length === 1 ? `${products.length} Price` : `${products.length} Prices`)
      }
    },[props.singleProduct, props.selectedProducts])

    const handleValidation = (event) => {
        setEmailData(event.target.value);
        if (event.target.value.length !== 0) {
            setEmailError(validateEmailWithSuffix(event.target.value));
        }
    }

  const handleSubmitData = async () => {
    if (emailData.trim() === '') {
      setEmailError('Please enter email address')
    } else if (emailData.trim() !== '' && emailError === '') {
      setIsSendInProgress(true);
      try {
        if(emailData.trim().toLowerCase() === userData.data.email_id.toLowerCase()){
          setEmailError('To and from email cannot be same.');
          setIsSendInProgress(false);
        }else{
          let data = ""
          if (isDialogTypeShareApp) {
            data = await onShareApp();
          } else if (shareProductPricing) {
            data = await shareProductPricing(emailData, textarea.trim())
          }
          if (
            typeof data?.data?.data === "object" &&
            "error_message" in data?.data?.data
          ) { 
            showCommonDialog(data.data.data.error_message,[{name: mobileDiaglogConst.ok, action: resetDialogStore}]);
          } else{
            setIsMailSent(true);
          }
          setIsSendInProgress(false);
          setTextarea('');
          setEmailData('');
        }
      } catch (error) {
        setIsSendInProgress(false);
      }
    }
  }

  const onShareApp = async () => {
    const payload = {
      data: {
        "user_id": userData.data.id,
        "from_email": userData.data.email_id,
        "to_email": emailData,
        "email_content": textarea.trim().length === 0 ? null : textarea.trim()
      }
    }
    return axios.post(import.meta.env.VITE_API_SERVICE + '/user/shareWidgetRequest', payload);
  }

    return (
      <div className={styles.shareAppContent}>
          {!isMailSent ?
          <>
              <div className={styles.shareAppTitle}>
                  {!isDialogTypeShareApp ? 'Share selected prices with a friend or coworker:' : 'Share this app with a friend or coworker:'}
              </div>
              <div className={styles.AppShareInput}>
                  <label>Email</label>
                  <input type="email" value={emailData} onChange={event => {setEmailData(event.target.value); handleValidation(event);}} onBlur={(event) => handleValidation(event)} placeholder='<EMAIL>'></input>
                  {emailError.length !== 0 &&
                  <p className={styles.errorText1}>{emailError}</p>
                  }
              </div>
              
              <div className={styles.AppShareInput}>
                  <label>Message</label>
                  <textarea className='smsTextShare  ' name='share-content' value={textarea} onChange={(event) => { setTextarea(event.target.value) }} placeholder={!isDialogTypeShareApp ? `The ${shareProductsLength} you have selected will be shared with your friend. Write your friend a note so they know it is not spam email.` : 'Write your friend a note so they know it is not a spam email.'}>
                  </textarea>
              </div>
              <div className={styles.btnSectionGrid}>
                  <button className={styles.cancelBtn} onClick={props.onCancel}>Cancel</button>
                  <button className={styles.sendBtn} onClick={handleSubmitData} disabled={!!emailError || isSendInProgress}>Send</button>
              </div>
          </>
          :
          <div className={styles.submitPopupSection}>
              <span><CheckIcon/></span>
              <p className={styles.invitationSentSuccessfully}>Invitation Sent Successfully</p>
              <button className={styles.returnSearchBtn}>Return to Search</button>
              <button className={styles.doneBtn} onClick={props.onCancel}>Done</button>
          </div> 
        }
      </div>
    )
}

export default ShareApp