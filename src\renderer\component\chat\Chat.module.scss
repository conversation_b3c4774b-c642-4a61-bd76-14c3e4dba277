* {
    box-sizing: border-box;
}



.chatMain {
    height: calc(100% - 30px);
    width: 100%;
    box-sizing: border-box;
    display: block;
    padding: 10px 0px;
    text-align: left;
    background: transparent;
}


$primary-color: #4CAF50; // Change this to your preferred color

.chatContainer {
    max-width: 100%;
    margin: 0px auto;
    display: flex;
    flex-direction: column;
    height: calc(100% - 44px);
}

.innerChat{
    display: flex;
    align-items: flex-end;
    height: calc(100% - 105px);
    width: 100%;
    flex: 1;
    overflow-y: auto;
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background:
            #9da2b2;
        border-radius: 50px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }
}

.chat {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    margin-top: auto;
}


.inputBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f9f9f9; // Change as needed
    padding: 10px;
    position: relative;
    bottom: 0;
    border-radius: 4px;
    z-index: 999;
    width: calc(100% - 20px);
    margin: 0 auto;
}

.inputBox input {
    flex-grow: 1;
    margin-right: 10px;
    padding: 4px 8px;
    &:focus{
        outline: #9da2b2;
    }
}

.inputBox button {
    padding: 8px 15px;
    height: 38px;
    background-color: $primary-color;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.scrollDownImage {
    position: absolute;
    right: 24px;
    bottom: 180px;
    width: 40px;
    height: 40px;
    background-color: #f9f9f9;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 1;
    svg {
      cursor: pointer;
      width: 40px;
      height: 40px;
      position: relative;
      top: 5px;
    }
  }