import { useQuery } from '@tanstack/react-query';
import { Auth } from 'aws-amplify'; 
import { cognitoCookiePrefix, reactQueryKeys } from '../common';
import { CognitoUser } from 'amazon-cognito-identity-js';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import { useIonRouter } from '@ionic/react';
import { getUserAppData, removeAutoCred, stringifyError } from '../helper';
import useSaveLoginActivityAnalytics from './useSaveLoginActivityAnalytics';
 
const getUser = (): Promise<CognitoUser>  => {
  return Auth.currentAuthenticatedUser().then((res) => {
    return res;
  });
};

const useCognitoUser = (isUserLoggedIn: boolean) => {
  const router = useIonRouter()
  const saveLoginActivityAnalytics = useSaveLoginActivityAnalytics()
  
  return useQuery([reactQueryKeys.cognitoUser], getUser, {
    retry: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    enabled: !!isUserLoggedIn,
    onError(err) {
      const store:any = useGlobalStore.getState();
      router.push('/login', { animate: true, direction: 'forward' })
      const data= getUserAppData()
      const payload = {
        ...data, reason:{error: stringifyError(err), isManualLogin:store.isManualLogin}, 
      }
      const isCookiePresent = document.cookie.split("; ").some((cookie)=>{
        const [name] = cookie.split("=");
        return name.startsWith(cognitoCookiePrefix);
      });
      if(err?.message === "Password attempts exceeded" || err === "Password attempts exceeded"){
        removeAutoCred()
      }
      if(isCookiePresent) saveLoginActivityAnalytics.mutate(payload);
      store.setShowLoader(false)
      return err;
    },
  });
};

export default useCognitoUser;
