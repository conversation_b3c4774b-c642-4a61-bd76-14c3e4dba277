// @ts-nocheck
import { create } from 'zustand';
import { purchaseOrder, routes } from '../../common';

const setPoCart = (orders) => {
  const readyToClaim = [];
  const pipelineOrder =[];
  if(orders?.length>0){
    orders.forEach(order => {
      if(order.claimed_by === purchaseOrder.readyToClaim)readyToClaim.push(order);
      else pipelineOrder.push(order);
    });
    return readyToClaim.concat(pipelineOrder);
  }
  return [];
}


// const updateToReadyState = (orders,po_number) => {
//   return orders.map((order) => {
//     if(order.po_number === po_number){
//       return {...order, claimed_by: purchaseOrder.readyToClaim};
//     }
//     return order;
//   });
// }

const addNewPoToClaim = (orders, orderDetail) => {
  const po_number = orderDetail.buyer_po_number;
  console.log(po_number);
  console.log(orderDetail);
  const index = orders.findIndex(order => order.buyer_po_number === po_number);
  const orderToBeUpdated = orders[index];
  orderToBeUpdated.claimed_by = purchaseOrder.readyToClaim;
  return [orderToBeUpdated , ...orders.slice(0,index), ...orders.slice(index+1,orders.length)];
}

const removeFromCart = (state,po_number, isOrderConfirmed) => {
  const orderData = {};
  orderData.ordersCart = state.ordersCart.filter((order) => order.buyer_po_number !== po_number );
  if(state.poToBeAccepted === po_number && isOrderConfirmed){
    orderData.navigatePageTo = routes.orderConfirmationPageSeller;
  }
  return orderData;
}

// const addToCart = (orders, incomingOrderDetail) => {
//   const incomingOrder = incomingOrderDetail.order;
//   if(incomingOrderDetail.type === "buyerCheckout"){
//     const index = orders.findIndex(order => order.claimed_by === purchaseOrder.pending);
//     return [...orders.slice(0,index), incomingOrder, ...orders.slice(index,orders.length)];
//   }
//   else{
//     return [incomingOrder, ...orders];
//   }
// }

const changePoAcceptedValue = (po_number) => {
  console.log("store = " +po_number);
  return {poToBeAccepted: po_number};
}

const addToPipeLine = (orders, incomingOrderDetail) => {
  const incomingOrder = incomingOrderDetail.po_data;
  let index = 0;
  while(orders.length && index < orders.length && orders[index].claimed_by === purchaseOrder.readyToClaim){
    index++;
  }
  return [...orders.slice(0,index), incomingOrder, ...orders.slice(index,orders.length)];
}

const updateFilteredPoToClaimFromPending = (filteredOrders, orderDetail) => {
  const po_number = orderDetail.buyer_po_number;
  const index = filteredOrders.findIndex(order => order.buyer_po_number === po_number);
  const orderToBeUpdated = filteredOrders[index];
  if(!orderToBeUpdated)return filteredOrders;
  orderToBeUpdated.claimed_by = purchaseOrder.readyToClaim;
  return [...filteredOrders];
}

// const addOrderBadgeList = (state, po_number) => {
//   const orderNumberSet = new Set();
//   for(const orderNumber of state.ordersListForBadgeCount){
//     orderNumberSet.add(orderNumber);
//   }
//   orderNumberSet.add(po_number);
//   console.log("newPoToClaimset",orderNumberSet);
//   return [...orderNumberSet];
// }

// const removeBadgeCount = (state, poNumber) => {
//   if(state.ordersListForBadgeCount.length > 0)
//   return state.ordersListForBadgeCount.filter(orderPoNumber => orderPoNumber !== poNumber);
//   return [];
// }

const initialOrderStoreState = {
  ordersCart: [],
  poToBeAccepted: null,
  showAlertForNewPo: false,
  filterPoStoreBy: '',
  navigatePageTo: '',
  orderToBeShownInOrderAccept: null,
  errorPopupDetail: null,
  filteredPoList:[],
  searchByProductOrZipValue:'',
}

const commonStore = {
  showLoader: false,
  sellerSettingsData: null,
  disableBryzosNavigation: true,
  enableShareWidget: false,
  createPoSessionId: null,
  searchSessionId: null,
  navigationStateForNotification:null,
  forbiddenTooltips: [],
  viewedOrdersListForBadgeCount: [],
  resetHeaderConfig:false,
  createPoData: null,
  backNavigation: -1,
  backdropOverlay:false,
  userData: {},
  hideHeader: false
}


export const useStore = create((set, get) => ({
  ...commonStore,
  setShowLoader: (_show) => set( {showLoader: _show} ),
  setSellerSettingsData: (_sellerSettingsData) => set( {sellerSettingsData:_sellerSettingsData }),
  ...initialOrderStoreState,
  setPOCart: (orders) => set(state => ({ ordersCart: setPoCart(orders), showAlertForNewPo: (orders && orders.length > 0) ? true: false })),
  addPipeLinePO: (orderDetail) => set(state => ({ ordersCart:  addToPipeLine(state.ordersCart,orderDetail), showAlertForNewPo: true})),
  addNewPoToClaimToCart: (orderDetail) => set(state => ({ ordersCart: addNewPoToClaim(state.ordersCart, orderDetail), filteredPoList: updateFilteredPoToClaimFromPending(state.filteredPoList, orderDetail), showAlertForNewPo:true })),
  removeFromPOCart: (po_number,isOrderConfirmed) => set(state => (removeFromCart(state,po_number,isOrderConfirmed))),
  changePoToBeAccepted: (po_number) => set(state => (changePoAcceptedValue(po_number))),
  setShowAlertForNewPo: (showAlert) => set(state => ({ showAlertForNewPo: showAlert})),
  setFilterPoStoreBy: (filterBy) => set(state => ({ filterPoStoreBy: filterBy})),
  setNavigatePageTo: (navigateTo) => set(state => ({ navigatePageTo: navigateTo})),
  setOrderToBeShownInOrderAccept: (order) => set(state => ({ orderToBeShownInOrderAccept: order})),
  setErrorPopupDetail: (error) => set(state => ({ errorPopupDetail: error})),
  setFilteredPoList: (poList) => set( state => ( { filteredPoList: poList } )),
  setSearchByProductOrZipValue: (searchValue) => set( state => ( { searchByProductOrZipValue: searchValue } )),
  setResetHeaderConfig: (shouldResetConfig) => set( state => ( { resetHeaderConfig: shouldResetConfig } )),
  resetFiltersInPurchaseOrders: () => set( state => ({filterPoStoreBy: '', searchByProductOrZipValue:''})),
  setCreatePoData: (_inProgressPoData) => set( {createPoData:_inProgressPoData }),
  setBackNavigation: (_backNavigation) => set( {backNavigation:_backNavigation }),
  resetOrderStore: () => set(state => ({
    ...initialOrderStoreState
  })),
  setDisableBryzosNavigation: (isDisable)=>set({disableBryzosNavigation:isDisable}),
  setEnableShareWidget: (data) => set({ enableShareWidget: data}),
  setCreatePoSessionId: (data) => set({ createPoSessionId: data}),
  setSearchSessionId: (data) => set({ searchSessionId: data}),
  setNavigationStateForNotification: (data) => set({navigationStateForNotification: data}),
  setForbiddenTooltips: (data) => set({forbiddenTooltips: data}),
  setViewedOrdersListForBadgeCount: (data) => {set({viewedOrdersListForBadgeCount: data})},
  setBackdropOverlay: (data) => {set({backdropOverlay: data})},
  setUserData: (data) => {set({userData: data})},
  setHideHeader: (hideHeader) => {set({userData: hideHeader})},
  resetCommonStore: () => set(state => ({
    ...commonStore
  })),

}));
  