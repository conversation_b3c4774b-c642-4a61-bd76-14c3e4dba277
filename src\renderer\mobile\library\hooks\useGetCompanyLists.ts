import { useQuery } from '@tanstack/react-query';
import { reactQueryKeys } from '../common';
import axios from 'axios';
import useDialogStore from '../../components/Dialog/DialogStore';
import { commomKeys, mobileDiaglogConst } from '@bryzos/giss-ui-library';

const useGetCompanyLists = (isEnabled = true) => {
  const { showCommonDialog, resetDialogStore } = useDialogStore();
  return useQuery([reactQueryKeys.getCompanyLists], async () => {
    try {
      let url = `${import.meta.env.VITE_API_SERVICE}/user/company`;

      const response = axios.get(url);
      const responseData = await response;

      if (responseData.data && responseData.data?.data) {
        return responseData.data.data;
      } else {
        return null;
      }
    } catch (error: any) {
      showCommonDialog(commomKeys.errorContent,[{name: mobileDiaglogConst.ok, action: resetDialogStore}]);
    }
  },
  {
    enabled:isEnabled
  });
};

export default useGetCompanyLists;
