// @ts-nocheck
import clsx from 'clsx';
import styles from './YourOrderListing.module.scss';
import { useEffect, useState } from 'react';
import axios from 'axios';
import { IonContent, IonPage, useIonLoading, useIonRouter, useIonViewWillEnter, } from '@ionic/react';
import { formatToTwoDecimalPlaces } from '../../../../library/helper';
import { convertUtcToCtTimeUsingDayjs, dateTimeFormat, useGlobalStore } from '@bryzos/giss-ui-library';
const YourOrderListing = () => {
  const router = useIonRouter();
  const [ordersList, setOrdersList] = useState([]);
  const setShowLoader = useGlobalStore(state => state.setShowLoader);
  
  useIonViewWillEnter(() => {
    setShowLoader(true);
    axios.get(import.meta.env.VITE_API_ORDER_SERVICE + '/seller/getClaimedOrders').then((response) => {
        setOrdersList(response.data.data);
        setShowLoader(false);
      })
      .catch((error) => {
        console.error(error);
        setShowLoader(false);
        // setApiFailureDialog(true)
      });
  }, []);
  const navigateToOrderPreview = (i: number) => {
    const data: any = { poIndex: i, ordersList: ordersList};
    router.push("/your-order-preview" ,{animate:true,direction:'forward'},undefined,data)
  };
  const inLinePoHeader = (order:any) => {
    const distinctHeaderSet = new Set();
      order.items?.forEach((item) => distinctHeaderSet.add(item.shape));
      let poHeader='';
      let loop=0;
      distinctHeaderSet.forEach((item) => {
          poHeader += item;
          if(loop < distinctHeaderSet.size-1) poHeader += ', ';
          loop++;
      })
      return poHeader;
  };
  return (
    <IonPage>
      <IonContent>
        <>
          <div className={styles.yourOrder}>
            <div className={styles.firstDiv}>
              <div className={styles.heading}>Your Orders</div>
            </div>
            
            <div className={styles.secondDiv}>
            {ordersList?.map((order: any, index: any) => {
              return (
                <button className={styles.yourOrderList} key={index} id='yourOrderListElement' onClick={($event) => navigateToOrderPreview(index)}>
                  <div className={styles.buyerPoName}>PO# {order.internal_po_number} ({order.seller_po_number})</div>
                  <div className={styles.yourorderDetails}>
                    <div className={styles.yourOrderLine}>
                        <div className={styles.fistLine}>{inLinePoHeader(order)}</div>
                        <div className={styles.secondLine}>
                            <div className={styles.fontWeight500}>Total Pounds :</div>
                            <div>{formatToTwoDecimalPlaces(order.total_weight)}</div>
                        </div>
                       
                    </div>
                    <div className={styles.yourOrderLine}>
                        <div className={styles.fistLine}><span className={styles.fontWeight500}>Purchase Date :</span> {convertUtcToCtTimeUsingDayjs(order.claimed_date, dateTimeFormat.dateSeparateWithSlashAndDayTwoDigit)}</div>
                        <div className={styles.secondLine}>
                            <div className={styles.fontWeight500}>Material Total :</div>
                            <div>$ {formatToTwoDecimalPlaces(order.seller_po_price)}</div>
                        </div>
                        
                    </div>
                  </div>
                </button>
              );
            })}
            </div>
          </div>
        </>
      </IonContent>
    </IonPage>
  );
};
export default YourOrderListing;
