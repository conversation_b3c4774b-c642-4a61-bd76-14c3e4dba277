// @ts-nocheck
import React, { useEffect, useRef, useState } from 'react'
import styles from './header.module.scss'
import { ReactComponent as BLogo } from '../../assets/mobile-images/B_With_Shadow.svg';
import { ReactComponent as Setting } from '../../assets/mobile-images/Setting.svg';
import { ReactComponent as SettingActive } from '../../assets/mobile-images/settings-active.svg';
import { ReactComponent as CloseIcon } from '../../assets/mobile-images/close_Popup.svg';
import { ReactComponent as ChatIcon } from '../../assets/images/chat.svg';
import { ReactComponent as LogoutIcon } from '../../assets/mobile-images/Logout.svg';

import { IonModal, useIonRouter } from '@ionic/react';
import ShareApp from '../components/ShareApp';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import { appEnvironment, settingBasedRoutes, userRole, mobileRoutes } from '../library/common';
import { useLocation } from 'react-router-dom';
import clsx from 'clsx';
const Header = () => {
    const router = useIonRouter()
    const location = useLocation();
    const modal = useRef<HTMLIonModalElement>(null);
    const [openShareAppDialog, setOpenShareAppDialog] = useState(false);
    const {userData,hideHeader, showChatIcon,setGlobalLogout} = useGlobalStore();
    const env = import.meta.env.VITE_ENVIRONMENT_UI;
    const envTitle = env.charAt(0).toUpperCase() + env.slice(1);
    const envClass = env + '_env';
    const routeData = router.routeInfo.routeOptions;
    useEffect(() => {
        setOpenShareAppDialog(false);
    },[location.pathname])

    const routeToSetting = () => {
        if (userData.data.type === userRole.buyerUser)
            router.push('/setting',{animate:true,direction:'forward'})
        else
            router.push('/seller-setting',{animate:true,direction:'forward'})
    }

    const handleShareDialogBox = () =>{
        setOpenShareAppDialog(true)
    }

    return (
        <>
            <div className={clsx(styles.topHeaderBG, styles[envClass], hideHeader && styles.hidden)}>
                <div className={styles.topTitle}>
                    <p  onClick={() => handleShareDialogBox()}>Share This App 1</p>
                    <div className={styles.badgeContainer}> {env !== appEnvironment.prod && <span className={clsx(styles.badge)}>{envTitle}  &nbsp; v{import.meta.env.VITE_REACT_APP_VERSION}</span> }</div>
                    <p>Patent Pending</p>
                </div>
                <div className={styles.searchHeader}>
                    <span><BLogo /></span>
                    { router.routeInfo.pathname !== mobileRoutes.changePassword &&
                        <>
                            {userData.data?.type === userRole.buyerUser && <span className={clsx(styles.headerMenu, router.routeInfo.pathname === '/instantpurchasing' && styles.activeMenu)} onClick={() => router.push('/instantpurchasing',{animate:true,direction:'forward'})}>Instant<br />Purchasing</span>}
                            {userData.data?.type === userRole.sellerUser && <span className={clsx(styles.headerMenu, router.routeInfo.pathname === '/order-listing' && styles.activeMenu)} onClick={() => router.push('/order-listing',{animate:true,direction:'forward'})}>Available<br />Orders</span>}
                            {userData.data?.type === userRole.sellerUser &&<span className={clsx(styles.headerMenu, (router.routeInfo.pathname === '/your-order-listing' || router.routeInfo.pathname === '/your-order-preview') && styles.activeMenu)} onClick={() => router.push('/your-order-listing',{animate:true,direction:'forward'})}>Order<br />History</span>}
                            <span className={clsx(styles.headerMenu, router.routeInfo.pathname === '/search' && styles.activeMenu)} onClick={() => router.push('/search',{animate:true,direction:'forward'})}>Steel<br />Prices</span>
                            {showChatIcon && <span className={clsx(styles.chatIcon, styles.chatIconActive, styles.headerMenu, router.routeInfo.pathname === '/chat' && styles.activeMenu)} onClick={() => router.push('/chat', { animate: true, direction: 'forward' })}><ChatIcon /></span>}
                            {<span className={clsx(styles.headerMenu, router.routeInfo.pathname === '/video-library' && styles.activeMenu)} onClick={() => router.push('/video-library',{animate:true,direction:'forward'})}>Video<br/>Library</span>}
                            {settingBasedRoutes.BUYER.has(router.routeInfo.pathname) || settingBasedRoutes.SELLER.has(router.routeInfo.pathname) ?
                                <span onClick={routeToSetting} className={styles.activeMenu}><SettingActive /></span>
                            :
                                <span onClick={routeToSetting} ><Setting /></span>
                            }
                        </>
                    }
                     { (router.routeInfo.pathname === mobileRoutes.changePassword && routeData?.isLogin) &&
                         <div className={styles.logoutBtn} onClick={() => setGlobalLogout(mobileRoutes.loginPage)}>
                           <LogoutIcon/>Logout
                         </div>
                    }
                </div>
                <IonModal className={'shareAppPopup'} ref={modal} isOpen={openShareAppDialog} backdropDismiss={false}>
                    <button className={styles.closePopupBtn} onClick={() => setOpenShareAppDialog(false)}><CloseIcon /></button>
                    <ShareApp isDialogTypeShareApp={true} onCancel={() => setOpenShareAppDialog(false)}/>
                </IonModal>
            </div>            
        </>

    )
}

export default Header