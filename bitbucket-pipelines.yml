pipelines:
  custom:
    AWS-CapGo-Release-1-qa:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
             # start deplyoment notification
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"QA\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload and Tag
          deployment: QA
          image: node:20
          script:
            - node -v
            - npm -v
             # start deplyoment
            - apt-get update && apt-get install -y awscli jq
            - npm i @capgo/cli@7.11.4
            - npm i
            - npm run ionic:build:qa
            - echo "build created successfully"
            # Set Necessary Variables
            - S3_BUCKET="s3://mobile-updates/qa/latest"
            - S3_BACKUP_BUCKET="s3://mobile-updates/qa/backup"
            - LOCAL_PATH="./build/*"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - echo "$package_version"
            # Create Zip
            - apt-get install -y zip jq
            - zip -r $package_version.zip ./build/*
            - echo "zip created successfully"
            # Deploy Backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/
            - echo "Backup successfully"
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ --recursive
            - aws s3 cp  $package_version.zip $S3_BUCKET/ 
            - aws s3 cp buildversion1.ver $S3_BUCKET/
            - echo "upload S3 successfully"
            #run capgo upload command.
            - npx @capgo/cli@7.11.4 bundle upload $PROJECT_NAME --apikey $CAPGO_API_KEY --channel $CAPGO_QA_CHANNEL --tus
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - NEW_TAG="qa-$(date +'%d-%b-%y')-($package_version)";
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "Tag created successfully $NEW_TAG"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"QA\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"QA\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi
    AWS-CapGo-Release-2-Demo:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
             # start deplyoment notification
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"Demo\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload and Tag
          deployment: Demo
          image: node:20
          script:
            - node -v
            - npm -v
             # start deplyoment
            - apt-get update && apt-get install -y awscli jq
            - npm i @capgo/cli@4.27.9
            - npm i
            - npm run ionic:build:demo
            - echo "build created successfully"
            # Set Necessary Variables
            - S3_BUCKET="s3://mobile-updates/demo/latest"
            - S3_BACKUP_BUCKET="s3://mobile-updates/demo/backup"
            - LOCAL_PATH="./build/*"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - echo "$package_version"
            # Create Zip
            - apt-get install -y zip jq
            - zip -r $package_version.zip ./build/*
            - echo "zip created successfully"
            # Deploy Backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/
            - echo "Backup successfully"
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ --recursive
            - aws s3 cp  $package_version.zip $S3_BUCKET/ 
            - aws s3 cp buildversion1.ver $S3_BUCKET/
            - echo "upload S3 successfully"
            #run capgo upload command.
            - npx @capgo/cli@4.27.9 bundle upload $PROJECT_NAME --apikey $CAPGO_API_KEY --channel $CAPGO_DEMO_CHANNEL --tus
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - NEW_TAG="demo-$(date +'%d-%b-%y')-($package_version)";
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "Tag created successfully $NEW_TAG"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi
    AWS-CapGo-Release-3-Prod:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
             # start deplyoment notification
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"Production\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload and Tag
          deployment: Production
          image: node:20
          script:
            - node -v
            - npm -v
             # start deplyoment
            - apt-get update && apt-get install -y awscli jq
            - npm i @capgo/cli@4.27.9
            - npm i
            - npm run ionic:build:prod
            - echo "build created successfully"
            # Set Necessary Variables
            - S3_BUCKET="s3://mobile-updates/production/latest"
            - S3_BACKUP_BUCKET="s3://mobile-updates/production/backup"
            - LOCAL_PATH="./build/*"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - echo "$package_version"
            # Create Zip
            - apt-get install -y zip jq
            - zip -r $package_version.zip ./build/*
            - echo "zip created successfully"
            # Deploy Backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/
            - echo "Backup successfully"
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ --recursive
            - aws s3 cp  $package_version.zip $S3_BUCKET/ 
            - aws s3 cp buildversion1.ver $S3_BUCKET/
            - echo "upload S3 successfully"
            #run capgo upload command.
            - npx @capgo/cli@4.27.9 bundle upload $PROJECT_NAME --apikey $CAPGO_API_KEY --channel $CAPGO_PROD_CHANNEL --tus
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - NEW_TAG="prod-$(date +'%d-%b-%y')-($package_version)";
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "Tag created successfully $NEW_TAG"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi
    CapGo-Release-1-qa:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
             # start deplyoment notification
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"QA\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload and Tag
          deployment: QA
          image: node:20
          script:
            - node -v
            - npm -v
             # start deplyoment
            - apt-get update && apt-get install -y awscli jq
            - npm i @capgo/cli@7.11.4
            - npm i
            - npm run ionic:build:qa
            - echo "build created successfully"
            #run capgo upload command.
            - npx @capgo/cli@7.11.4 bundle upload $PROJECT_NAME --apikey $CAPGO_API_KEY --channel $CAPGO_QA_CHANNEL --tus
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - NEW_TAG="qa-$(date +'%d-%b-%y')-($package_version)";
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "Tag created successfully $NEW_TAG"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"QA\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"QA\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi
    CapGo-Release-2-Demo:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
             # start deplyoment notification
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"Demo\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload and Tag
          deployment: Demo
          image: node:20
          script:
            - node -v
            - npm -v
             # start deplyoment
            - apt-get update && apt-get install -y awscli jq
            - npm i @capgo/cli@4.27.9
            - npm i
            - npm run ionic:build:demo
            - echo "build created successfully"
            #run capgo upload command.
            - npx @capgo/cli@4.27.9 bundle upload $PROJECT_NAME --apikey $CAPGO_API_KEY --channel $CAPGO_DEMO_CHANNEL --tus
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - NEW_TAG="demo-$(date +'%d-%b-%y')-($package_version)";
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "Tag created successfully $NEW_TAG"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi
    CapGo-Release-3-Prod:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
             # start deplyoment notification
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"Production\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload and Tag
          deployment: Production
          image: node:20
          script:
            - node -v
            - npm -v
             # start deplyoment
            - apt-get update && apt-get install -y awscli jq
            - npm i @capgo/cli@4.27.9
            - npm i
            - npm run ionic:build:prod
            - echo "build created successfully" 
            #run capgo upload command.
            - npx @capgo/cli@4.27.9 bundle upload $PROJECT_NAME --apikey $CAPGO_API_KEY --channel $CAPGO_PROD_CHANNEL --tus
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - NEW_TAG="prod-$(date +'%d-%b-%y')-($package_version)";
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "Tag created successfully $NEW_TAG"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi  
    AWS-Release-1-qa:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
             # start deplyoment notification
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"QA\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload and Tag
          deployment: QA
          image: node:20
          script:
            - node -v
            - npm -v
             # start deplyoment
            - apt-get update && apt-get install -y awscli jq
            - npm i @capgo/cli@4.27.9
            - npm i
            - npm run ionic:build:qa
            - echo "build created successfully"
            # Set Necessary Variables
            - S3_BUCKET="s3://mobile-updates/qa/latest"
            - S3_BACKUP_BUCKET="s3://mobile-updates/qa/backup"
            - LOCAL_PATH="./build/*"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - echo "$package_version"
            # Create Zip
            - apt-get install -y zip jq
            - zip -r $package_version.zip ./build/*
            - echo "zip created successfully"
            # Deploy Backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/
            - echo "Backup successfully"
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ --recursive
            - aws s3 cp  $package_version.zip $S3_BUCKET/ 
            - aws s3 cp buildversion1.ver $S3_BUCKET/
            - echo "upload S3 successfully"
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - NEW_TAG="qa-$(date +'%d-%b-%y')-($package_version)";
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "Tag created successfully $NEW_TAG"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"QA\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"QA\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi   
    AWS-Release-2-Demo:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
             # start deplyoment notification
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"Demo\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload and Tag
          deployment: Demo
          image: node:20
          script:
            - node -v
            - npm -v
             # start deplyoment
            - apt-get update && apt-get install -y awscli jq
            - npm i @capgo/cli@4.27.9
            - npm i
            - npm run ionic:build:demo
            - echo "build created successfully"
            # Set Necessary Variables
            - S3_BUCKET="s3://mobile-updates/demo/latest"
            - S3_BACKUP_BUCKET="s3://mobile-updates/demo/backup"
            - LOCAL_PATH="./build/*"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - echo "$package_version"
            # Create Zip
            - apt-get install -y zip jq
            - zip -r $package_version.zip ./build/*
            - echo "zip created successfully"
            # Deploy Backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/
            - echo "Backup successfully"
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ --recursive
            - aws s3 cp  $package_version.zip $S3_BUCKET/ 
            - aws s3 cp buildversion1.ver $S3_BUCKET/
            - echo "upload S3 successfully"
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - NEW_TAG="demo-$(date +'%d-%b-%y')-($package_version)";
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "Tag created successfully $NEW_TAG"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi
    AWS-Release-3-Prod:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
             # start deplyoment notification
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"Production\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload and Tag
          deployment: Production
          image: node:20
          script:
            - node -v
            - npm -v
             # start deplyoment
            - apt-get update && apt-get install -y awscli jq
            - npm i @capgo/cli@4.27.9
            - npm i
            - npm run ionic:build:prod
            - echo "build created successfully"
            # Set Necessary Variables
            - S3_BUCKET="s3://mobile-updates/production/latest"
            - S3_BACKUP_BUCKET="s3://mobile-updates/production/backup"
            - LOCAL_PATH="./build/*"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - echo "$package_version"
            # Create Zip
            - apt-get install -y zip jq
            - zip -r $package_version.zip ./build/*
            - echo "zip created successfully"
            # Deploy Backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/
            - echo "Backup successfully"
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ --recursive
            - aws s3 cp  $package_version.zip $S3_BUCKET/ 
            - aws s3 cp buildversion1.ver $S3_BUCKET/
            - echo "upload S3 successfully"
            # Extract the version from package.json
            - package_version=$(node -p "require('./package.json').version")
            - NEW_TAG="prod-$(date +'%d-%b-%y')-($package_version)";
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "Tag created successfully $NEW_TAG"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi   