.stepsContentMain {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.instantPurchasingForm {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-direction: column;
    position: relative;
    padding: 16px;
    height: calc(100% - 60px);
    overflow: auto;


    .inputPurchasing {
        width: 100%;

        input {
            width: 100%;
            height: 40px;
            margin-bottom: 10px;
            display: flex;
            padding: 10px 8px 10px 12px;
            border-radius: 4px;
            background-color: rgba(0, 0, 0, 0.2);
            text-align: center;
            box-shadow: none;
            border: 0px;
            font-family: Noto Sans Display;
            font-weight: 300;
            font-size: 14px;
            color: #fff;

            &::placeholder {
                color: rgba(255, 255, 255, 0.5);
            }

            &:focus {
                border: solid 0.5px #fff;
                background-color: rgba(0, 0, 0, 0.5);
                outline: none;
            }
        }
    }

    .stateZipcode {
        display: flex;
        gap: 10px;
        width: 100%;
        margin-bottom: 10px;

        input {
            width: 100%;
            height: 40px;
            display: flex;
            padding: 10px 8px 10px 12px;
            border-radius: 4px;
            background-color: rgba(0, 0, 0, 0.2);
            box-shadow: none;
            text-align: center;
            border: 0px;
            font-family: Noto Sans Display;
            font-weight: 300;
            color: #fff;
            font-size: 14px;

            &:focus {
                border: solid 0.5px #fff;
                background-color: rgba(0, 0, 0, 0.5);
                outline: none;
            }

            &::placeholder {
                color: rgba(255, 255, 255, 0.7);
                text-align: center;
            }
        }

        .errorMsg {
            border: 1px solid #ff0000;

            &:focus {
                border: 1px solid #ff0000;
            }
        }
    }

    .stateDropdown {
        display: flex;
        gap: 12px;

        .grid1 {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            width: auto;
            max-width: 50%;
        }
    }

}

// Dropdown Style
.Dropdownpaper.Dropdownpaper {
    padding: 6px 4px 6px 12px;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2);
    background-color: #636363;
    overflow: hidden;
    border-radius: 0px 0px 2px 2px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;

    &.deliveryDateDropdownList {
        ul {
            max-height: 260px;
            @media (max-height:750px) {
                max-height: 190px;
            }
        }
    }

    &.priceUnitDropdown {
        padding: 2px 3px;
        border-radius: 2px;

        ul {
            padding-right: 0px;
            padding-top: 2px;
            padding-bottom: 2px;

            li {
                font-size: 12px;
                line-height: 1;
                padding: 2px 4px;
                border-radius: 4px;
                min-height: 24px;
            }
        }
    }

    ul {
        overflow: auto;
        padding-right: 4px;
        padding-top: 0px;
        padding-bottom: 0px;
        max-height: 236px;

        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }


        li {
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            padding: 6px 12px;
            border-radius: 4px;
            min-height: 32px;


            &[aria-selected="true"] {
                background-color: #fff;
                color: #000;
            }
        }
    }
}

.selectPaymentDropdownPanel.selectPaymentDropdownPanel {
    left: 0;
    border-radius: 0px;
    height: 100px;
    padding: 10px;
    box-shadow: 0 -2px 6px 0 rgba(0, 0, 0, 0.3);
    background-color: rgba(255, 255, 255, 0.3);
    left: 0px !important;
    -webkit-backdrop-filter: blur(32px);
    backdrop-filter: blur(32px);

    ul {
        padding: 0px;

        li {
            font-family: Noto Sans Display;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            min-height: 40px;
            width: 100%;
            padding: 7px 12px 6.1px 12px;

            &[aria-selected="true"] {
                background-color: #c3c3c3;
                color: #000;
            }
        }
    }
}

.stepsBtmSection {
    width: 100%;
    height: 72px;
    padding: 12px 16px;
    backdrop-filter: blur(60px);
    background-image: linear-gradient(to bottom, #000 -20%, rgba(0, 0, 0, 0) 213%);
    margin-top: auto;
    display: flex;
    align-items: flex-start;

    .AddexpeditedDate {
        font-family: Noto Sans Display;
        font-size: 16px;
        font-weight: 300;
        line-height: 1.1;
        text-align: left;
        color: #fff;
        margin-top: 6px;
    }

    .proceedToStepBtn {
        font-family: Noto Sans Display;
        font-size: 16px;
        line-height: 1;
        text-align: right;
        margin-left: auto;
        color: #70ff00;

        &[disabled] {
            opacity: 0.5;
            color: #fff;
        }
    }
}

.step2Content {
    padding: 16px;
    height: calc(100% - 127px);
    overflow: auto;
    display: flex;
    flex-direction: column;

}

.lineNoHeader {
    display: flex;
    align-items: center;
    margin-bottom: 9px;
}

.btnDelLineNo {
    margin-left: auto;

    button {
        font-family: Noto Sans Display;
        font-size: 14px;
        color: #fff;
        height: 22px;
        display: flex;
        align-items: center;

        svg {
            width: 28px;
        }
    }

}

.stepsContent {
    width: 100%;
    height: 300px;
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 0px;
    }

    .stepsTitle {
        font-family: Noto Sans Display;
        font-size: 16px;
        color: #fff;
    }

    .stepsDescription {
        font-family: Noto Sans Display;
        font-weight: 300;
        font-size: 10px;
        color: #fff;
        margin-bottom: 4px;
        width: 50%;
    }

    .DescriptionsInput {
        display: flex;
        flex-direction: column;
        border-radius: 4px;
        font-family: Noto Sans Display;
        font-weight: 300;
        font-size: 14px;
        color: #fff;
        position: relative;
        margin-bottom: 8px;

        .liBody {
            font-weight: 300;
            font-size: 11px;

            p:first-child {
                font-weight: 600;
            }
        }

        .descriptionText {
            position: absolute;
            top: 8px;
            left: 8px;
            z-index: 99;
            color: rgb(255, 255, 255, 0.5);

            a {
                color: #70ff00;
                font-weight: 600;
            }
        }

        .descriptionInputProduct {
            background-color: rgba(0, 0, 0, 0.2);
            height: 80px;
            resize: none;
            width: 100%;
            border: 0px;
            border-radius: 4px 4px 0px 0px;
            padding: 8px;
            padding-top: 32px;
            color: rgb(255, 255, 255, 0.5);
        }

        input {
            background-color: rgba(0, 0, 0, 0.2);
            height: 36px;
            width: 100%;
            box-shadow: none;
            border: none;
            border-radius: 0px 0px 4px 4px;
            padding: 8px;

            &::placeholder {
                color: rgb(255, 255, 255, 0.5);
            }

            &:focus {
                border: solid 0.5px rgba(255, 255, 255, 0.7);
                background-color: rgba(0, 0, 0, 0.5);
                outline: none;
            }
        }
    }

    .selectedDescriptionInputProduct {
        width: 100%;
        height: 85px;
        padding: 6px 163px 6px 8px;
        background-color: rgba(0, 0, 0, 0.2);
        font-family: Noto Sans Display;
        font-size: 12px;
        font-weight: 300;
        line-height: 1.4;
        text-align: left;
        color: #fff;
    }
}

.qtyGrid {
    display: flex;
    grid-gap: 12px;
}

.priceDropMain.priceDropMain {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 0px 0px 0px 8px;
}

.errorInput {
    border: 0.5px solid #ff0000;
}

.qtyInput {
    width: 154px;
    height: 36px;
    display: flex;
    border-radius: 4px;
    position: relative;
    flex-grow: 1;

    .qtyError {
        position: absolute;
        color: #ff0000;
        font-weight: normal;
        font-size: 11px;
        bottom: -18px;
        width: 125%;
        font-family: Noto Sans Display;
        white-space: nowrap;
    }

    .inputPrice {
        display: flex;
        align-items: center;
        background-color: transparent;
        width: 105px;
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: 300;
        line-height: 1.4;
        text-align: left;
        color: #fff;
        flex-grow: 1;
    }

    input {
        background-color: rgba(0, 0, 0, 0.2);
        border: 0px;
        width: 105px;
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: 300;
        line-height: 1.4;
        text-align: left;
        color: #fff;
        padding: 0px 0px 0px 8px;
        border-radius: 4px 0px 0px 4px;
        flex-grow: 1;

        &:focus {
            outline: none;
        }
    }
}

.calcGrid {
    display: flex;
    width: 100%;
    margin-top: 16px;

    .grid1 {
        flex-grow: 1;
    }
}

.domesticMaterialGrid {
    display: flex;
    align-items: center;

    .domesticMaterialTex {
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: 300;
        line-height: 1.2;
        text-align: left;
        color: rgba(255, 255, 255, 0.8);
        margin-left: 8px;
    }

}

.extendedLbl {
    opacity: 0.5;
    font-family: Noto Sans Display;
    font-size: 10px;
    font-weight: 300;
    line-height: 1.4;
    text-align: right;
    color: #fff;
    margin-bottom: 0px;
}

.cartitemsPrice {
    font-family: Noto Sans Display;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.4;
    text-align: right;
    color: #fff;
}

.switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 23px;
    margin-bottom: 0px;

    input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.3);
        -webkit-transition: .3s;
        transition: .3s;
    }

    .slider:before {
        position: absolute;
        content: "";
        width: 19.2px;
        height: 19.2px;
        background-color: rgba(0, 0, 0, 0.4);
        left: 2px;
        bottom: 1.8px;
        -webkit-transition: .3s;
        transition: .3s;
    }

    input:checked+.slider {
        background-color: #70ff00;
    }

    input:checked+.slider:before {
        -webkit-transform: translateX(25px);
        transform: translateX(25px);
        background-color: rgba(0, 0, 0, 0.75);
    }

    .slider.round {
        border-radius: 34px;
    }

    .slider.round:before {
        border-radius: 500px;
    }
}

.totalAmountGrid {
    position: absolute;
    width: 100%;
    z-index: 99;
    bottom: 0;
    left: 0;
}

.totalPriceGrid {
    width: 100%;
    height: 55px;
    padding: 5px 16px;
    -webkit-backdrop-filter: blur(60px);
    backdrop-filter: blur(60px);
    background-image: linear-gradient(to bottom, #000 -20%, rgba(0, 0, 0, 0) 213%);
    display: flex;
    align-items: center;

    .unitTotal {
        font-family: Noto Sans Display;
        font-size: 16px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: rgba(255, 255, 255, 0.8);
    }

    .totalAmount {
        margin-left: auto;
        display: flex;
        flex-direction: column;
        font-family: Noto Sans Display;
        font-size: 16px;
        font-weight: normal;
        line-height: 1.4;
        letter-spacing: 2px;
        text-align: right;
        color: #fff;
    }
}

.addLineAndProceedBtn {
    backdrop-filter: blur(60px);
    padding: 12px 16px;
    background-image: linear-gradient(to bottom, #000 -20%, rgba(0, 0, 0, 0) 213%);
    height: 72px;
    display: flex;
    align-items: center;

    button {
        width: 50%;
        height: 100%;
    }

    .proceedToStepBtn {
        font-family: Noto Sans Display;
        font-size: 16px;
        line-height: 1;
        text-align: right;
        margin-left: auto;
        color: #70ff00;

        &[disabled] {
            opacity: 0.5;
            color: #fff;
        }
    }
}

.textLeft {
    text-align: left;
    opacity: 0.7;
    font-family: Noto Sans Display;
    font-size: 16px;
    font-weight: 300;
    line-height: 1.1;
    text-align: left;
    color: #fff;
    display: flex;
    align-items: center;
    gap: 8px
}

.textRight {
    font-family: Noto Sans Display;
    font-size: 16px;
    font-weight: normal;
    line-height: 1;
    text-align: right;
    color: #70ff00;
}

.lineGrid {
    width: 1px;
    height: 32px;
    opacity: 0.3;
    background-color: #fff;
}

.steps3Content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .content {
        flex: 1;
    }

    .tableGrid {
        width: 100%;
        display: inline-grid;
        grid-template-areas:
            "head-fixed"
            "body-scrollable"
            "foot-fixed";

        thead {
            grid-area: head-fixed;
        }

        tr {
            th {
                height: 21px;
                padding: 2px 3px 1.7px 3px;
                background-color: rgba(0, 0, 0, 0.6);
                border-top: solid 0.5px rgba(255, 255, 255, 0.3);
                font-family: Noto Sans Display;
                font-size: 12px;
                font-weight: 500;
                line-height: 1.4;
                text-align: center;
                color: #fff;

                &:nth-child(2) {
                    text-align: left;
                }

                &.lastTd {
                    text-align: right;
                    padding-right: 10px;
                }
            }
        }

        tbody {
            grid-area: body-scrollable;

            tr {
                &:nth-child(even) {
                    background-color: rgba(0, 0, 0, 0.15);

                    td {

                        &:nth-child(2),
                        &:nth-child(3) {
                            color: rgba(255, 255, 255, 0.67);
                        }
                    }
                }
            }

            tr {

                td {
                    font-family: Noto Sans Display;
                    padding: 10px 3px;
                    font-size: 12px;
                    font-weight: 300;
                    line-height: 1.2;
                    text-align: left;
                    color: #fff;
                    vertical-align: top;
                    height: 100%;

                    .tagLbl {
                        margin-top: 4px;
                        display: block;
                    }

                    .domesticMaterialLbl {
                        color: #70ff00;
                        margin-top: 2px;
                        display: block;
                        font-size: 10px;
                    }

                    &.qtyTd {
                        text-align: center;
                    }

                    &:first-child {
                        font-size: 16px;
                        font-weight: 500;
                        line-height: 1;
                        text-align: center;
                        color: rgba(255, 255, 255, 0.8);
                        vertical-align: text-top;
                    }

                    &.lastTd {
                        text-align: right;
                        padding-right: 10px;
                    }
                }
            }

        }

    }

    .addPoLineTable {
        table {

            thead,
            tbody {
                tr {

                    td,
                    th {
                        &:first-child {
                            width: 10%;
                        }

                        &:nth-child(2) {
                            width: 42%;
                        }

                        &:nth-child(3) {
                            width: 24%;
                            text-align: center;
                        }

                        &:nth-child(4) {
                            width: 24%;
                            text-align: right;
                            padding-right: 10px;
                        }
                    }
                }
            }
        }

        thead {
            width: 100%
        }

        tbody {
            display: block;
            overflow-y: auto;
            height: calc(100vh - 540px);
            min-height: 150px;

            tr {
                &:last-child {
                    td {
                        border-bottom: 0px;
                    }
                }
            }
        }

        thead,
        tbody tr,
        tfoot {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

    }
}


.totalAmt {
    padding: 11px 10px;
    grid-area: foot-fixed;
    width: 100%;

    tr:is(:first-child) {
        td {
            padding: 2px 0px;
            background-color: rgba(0, 0, 0, 0.4);

            &:first-child {
                text-align: right;
                span {
                    padding-right: 0px;
                }
            }

            &:last-child {
                span {
                    padding-right: 10px;
                }
            }


            &:nth-child(3) {
                text-align: center;
                width: 25px;
                span {
                    padding-right: 0px;
                }
            }

            div {
                display: block;
                padding: 2px 0px;
                border-top: 0.5px solid rgba(255, 255, 255, 0.3);
                border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
                background-color: rgba(0, 0, 0, 0.6);
                height: 20px;
            }

          

            border-top:0.5px solid rgba(255, 255, 255, 0.3);
            border-bottom:0.5px solid rgba(255, 255, 255, 0.3);

            &.materialTotal {
                flex-grow: 0;
                font-family: Noto Sans Display;
                font-size: 12px;
                font-weight: 300;
                line-height: 1.2;
                text-align: right;
                color: rgba(255, 255, 255, 0.8);
            }

            &.materialTotal1 {
                white-space: nowrap;
            }

            &:nth-child(3) {
                text-align: center;
            }
        }
    }

    tr {
        &:last-child {
            td {
                padding-bottom: 0px;
            }
        }

        td {
            padding: 4px 0px 0px 0px;
            font-family: Noto Sans Display;
            font-size: 12px;
            font-weight: 300;
            line-height: 1.2;
            text-align: right;
            color: rgba(255, 255, 255, 0.8);

            &:last-child{
                width: 60px;
            }
            
            &:nth-child(3){
                text-align: center;
            }
         
            .txtSign {
                display: block;
                text-align: center;
                padding: 0px 10px;
            }

            .txtNumber {
                font-weight: 600;
                padding-right: 10px;
            }

            .totalPurchase,
            .totalPurchaseSign,
            .totalPurchaseNumber {
                font-size: 20px;
                font-weight: 600;

            }

            .totalPurchaseSign {
                font-weight: 600;
            }

            .totalPurchaseNumber {
                font-weight: 600;
                padding-right: 10px;
                width: 80px;
            }
        }
    }
}

.step3BtmSection {
    .placeBtnOrderSection {
        width: 100%;
        height: 72px;
        flex-grow: 0;
        padding: 15px 12px 15px 12px;
        -webkit-backdrop-filter: blur(60px);
        backdrop-filter: blur(60px);
        background-image: linear-gradient(to bottom, #000 -18%, rgba(0, 0, 0, 0) 99%);
        font-size: 14px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;

        button {
            font-family: Noto Sans;
            font-size: 16px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: normal;
            text-align: left;
            color: #fff;
            display: flex;
            align-items: center;
            svg{
                margin-right: 3px;
            }
            &:disabled{
                color: rgba(255, 255, 255, 0.2);
            }
        }
    }
}

.searchProdInstantPrice {
    padding: 8px 16px 20px 16px;
}

.hide {
    display: none;
}

.uploadCertBtn{
    background: #70ff00;
    padding: 0px 4px;
    border-radius: 3px;
    border: 0.5px solid #70ff00;
    font-family: Noto Sans Display;
    font-size: 10px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: normal;
    text-align: left;
    color: #000;
    margin-right: 8px;
    height: 16px;
}
.purchaseOrderPop{
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 80px 16px 50px 16px;
    align-items: center;
    .deliverTO{
        margin-top: 40px;
    }
    .total{
        font-family: Noto Sans Display;
        font-size: 24px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: center;
        color: #fff;
    }
    .totalAmt1{
        font-family: Syncopate;
        font-size: 26px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: center;
        color: #fff;
    }
    .slideTo{
        margin-top: auto;
    }
    .slideBack{
        margin-top: 21px;
    }
}

.bnplStatusNoteContainer{
    padding: 6px 10px;
 .bnplStatusNote {
      padding: 5px 8px;
      border-radius: 6px;
      border: solid 1px #ff5d47;
      background-color: #ffefed;
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: left;
      color: #ff5d47;

      p {
          margin: 0;
          span{
            white-space: nowrap;
          }
      }
  }
}

