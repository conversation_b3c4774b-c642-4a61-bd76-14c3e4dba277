import { registerPlugin, Plugin } from "@capacitor/core";

// we can take advantage of TypeScript here!
interface NativePluginInterface extends Plugin {
  url:string,
  title:string,
  description:string,
  fileName: string,
  accessToken: string,
  mimeType: string,
};

// it's important that both Android and iOS plugins have the same name
export const FileDownloadPlugin = registerPlugin<NativePluginInterface>(
  "FileDownloadPlugin"
);