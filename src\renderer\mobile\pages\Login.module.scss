.appMain {
   height: 100%;

   .loginContainer {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
      padding: 90px 24px 00px 24px;
      position: relative;

      table {
         width: 100%;
      }

      .bryzosLogo {
         margin-bottom: 40px;
      }

      .inputBody {
         display: flex;
         flex-direction: column;
      }

      .welcomBryzosText {
         font-family: Noto Sans Display;
         font-size: 18px;
         font-weight: 300;
         line-height: 1.11;
         letter-spacing: -0.5px;
         text-align: center;
         color: #fff;
         margin-top: 0px;
         margin-bottom: 20px;
      }

      input {
         width: 100%;
         height: 40px;
         font-family: Noto Sans Display;
         font-size: 14px;
         font-weight: 300;
         line-height: 1.4;
         color: #fff;
         padding: 10px 0 10px 12px;
         border-radius: 4px;
         -webkit-backdrop-filter: blur(71.7px);
         backdrop-filter: blur(71.7px);
         border: solid 0.5px rgba(255, 255, 255, 0.7);
         margin-top: 20px;
         background-color: transparent;

         &:focus {
            outline: none;
            backdrop-filter: blur(71.7px);
            border: solid 0.5px rgba(255, 255, 255, 0.7);
            background-color: rgba(0, 0, 0, 0.5);
         }

         &::placeholder {
            color: rgba(255, 255, 255, 0.7);
         }
      }

      .loginBtnSection {
         margin-top: auto;
         text-align: center;
      }

      .loginBtn {
         font-family: Noto Sans Display;
         font-size: 16px;
         font-weight: normal;
         line-height: 1.4;
         letter-spacing: normal;
         text-align: center;
         color: rgb(112, 255, 0);
         margin-top: 20px;

         &:disabled {
            color: rgba(255, 255, 255, 0.7);
         }
      }
      
      .forgotBtn {
         font-family: Noto Sans Display;
         font-size: 16px;
         font-weight: normal;
         line-height: 1.4;
         letter-spacing: normal;
         text-align: center;
         color: #0091ff;
         margin-top: 20px;
      }

      .testContainer {
         margin-top: auto;
         display: flex;
         flex-direction: column;
         align-items: center;

         .bryzosText {
            margin-bottom: 8px;
         }

         .forgotCredentialsText {
            align-self: stretch;
            flex-grow: 0;
            font-family: Noto Sans Display;
            font-size: 14px;
            font-weight: normal;
            line-height: 2;
            text-align: center;
            color: rgba(255, 255, 255, 0.5);
         }
      }
   }
}

.badgeContainer {

   // position: absolute;
   // top: 30px;
   .badge {
      padding: 2px 16px;
      border-radius: 20px;
      font-family: Noto Sans;
      color: #fff;
   }
}

.dev_env,
.staging_env {
   .badge {
      background-image: linear-gradient(to right, #00c6ff, #0072ff);
   }
}

.qa_env {
   .badge {
      background-image: linear-gradient(to right, #006757, #299501);
   }
}

.demo_env {
   .badge {
      background-image: linear-gradient(to right, #ec008c, #fc6767);
   }
}

.prod_env {}