// @ts-nocheck
import { saveAs } from 'file-saver';
import rg4js from "raygun4js";
import { commomKeys, routes } from "../common";
import { Auth } from "aws-amplify";
import axios, { InternalAxiosRequestConfig } from "axios";
import { v4 as uuidv4 } from 'uuid';
import TrueVaultClient from 'truevault';
import { BuyerSetting, CompanyDetails, Profile } from './mobile/pages/buyer/setting/BuyerSettingStore';
import dayjs from 'dayjs';

export const VERSION_NUMBER = "*******";
export const commonRaygunError = "common-raygun-error";
export const trueVaultRaygunError = "trueVault-raygun-error";
export const unhandledError = "unhandled-error";

export const SEND_INVOICES_TO = "<EMAIL>";
export const SHIPPING_DOCS_TO = "<EMAIL>";

let channelWindow = {}
let navigate = null;

export const setChannelWindow = (channelWindowList: {}) => {
  channelWindow = channelWindowList
}

export const getChannelWindow = () => {
  return channelWindow;
}

export const setNavigate = (_navigate) => {
  if(navigate === null){
    navigate = _navigate;
  }
}

export const navigatePage = (currentPage, nextPage) => {
  if(currentPage === routes.orderConfirmationPageSeller 
    || currentPage === routes.acceptOrderPage
    || currentPage === routes.orderConfirmationPage
    ){
    navigate(nextPage.path,{ ...nextPage.state ,replace: true})
  }else{
    navigate(nextPage.path, nextPage.state)
  }
}
 
// Format a number as a currency value with a dollar sign
export function formatCurrency(number) {
  return Number(number).toLocaleString('en-US', {
    currency: 'USD',
    style: 'currency',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).replace("$", "$ ");
}

// Format a number with commas without dollar sign
export function formatNumber(number: number) {
  return number.toLocaleString('en-US');
}


export const formatToTwoDecimalPlaces = (
  value?: string
) => {
  if (value && +value) { 
    const decimal = ((+value) - Math.floor(+value)).toFixed(2);
    const precisionValue = decimal.substring(1, decimal.length);
    let formattedValue = parseInt(value).toLocaleString('en-US');
    return formattedValue + precisionValue;
  } else {
    return '0.00';
  }
};

export const formatPhoneNumberRemovingCountryCode = (phoneNo: string)=>{
  if(phoneNo)
  return formatPhoneNumber( phoneNo.replace(commomKeys.countryCode,'') );
  return phoneNo;
}

export const formatPhoneNumber = (phoneNo: string) => {
  const phoneNumber = phoneNo.replace(/\D/g, '');
  const phoneNumberLength = phoneNumber.length;
  if (phoneNumberLength < 4) {
    return phoneNumber;
  } else if (phoneNumberLength < 7) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
  } else {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
  }
};

export const formatPhoneNumberWithCountryCode = (phoneNo: string) => {
    return commomKeys.countryCode + phoneNo.replace(/[^\w\s]/gi, '').replace(/\s+/g, '');
};

export const addWorkingDaysToDate = (days: number) => {
  let date = new Date();
  let count = 0;

  while (count < days) {
    date.setDate(date.getDate() + 1);
    if (date.getDay() !== 0 && date.getDay() !== 6) {
      count++;
    }
  }
  return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear().toString().slice(2)}`;
};

export const formatEIN = (value: string) => {
  const ein = value.replace(/\D/g, '');
  const einLength = ein.length;
  if (einLength < 3) {
    return ein;
  } else {
    const firstPart = ein.slice(0, 2);
    const secondPart = ein.slice(2, 9);
    return `${firstPart}-${secondPart}`;
  }
};


export async function downloadFilesUsingFetch(url: RequestInfo | URL, fileName: string | undefined, type: any) {
  try {
    const response = await fetch(url);
    const responseData = await response.blob();

    if(responseData.type === 'application/json'){
      return false;
    }
    const blob = new Blob([responseData], { type: type });
    saveAs(blob, fileName);
    return true;
  } catch (error) {
    console.error('Error downloading the file:', error);
  }
}

export async function downloadFiles(url: string, fileName: string | undefined, type: any) {
  try {
    const response = await axios.get(url,{
      responseType: 'blob'
    });

    if(response.data.type === 'application/json'){
      return false;
    }
    const blob = new Blob([response.data], { type: type });
    saveAs(blob, fileName);
    return true;
  } catch (error) {
    console.error('Error downloading the file:', error);
  }
}

export const downloadFileWithAnyExtension = async (url: string) => {
  let index = url.length-1;
  for(  ;index >= 0 ;index-- ){
  if(url.charAt(index)==='/'){
      break;
  }
  }
  let fileName = url.substring(index+1, url.length);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

export const dispatchRaygunError = (error: unknown, tags: string | string[]) => {
  rg4js('send', {
    error: error,
    tags: tags
  })
}

export const formatCurrencyWithComma = (value: string) => {
  if (value) {
    return value.replace(/\B(?<!\.\d*)(?=(\d{3})+(?!\d))/g, ",");
  } else {
    return "";
  }
};

export const removeCommaFromCurrency = (value: string) => {
  if (value) {
    return value.replace(/\,/g, "");
  } else {
    return "";
  }
};

export const getFloatRemainder = (dividend: string | number | null, divisor: number) => {
  if (dividend && +divisor) {
    dividend = +dividend;
    divisor = +divisor;

    var dividendDecimalCount = (dividend.toString().split('.')[1] ?? '').length;
    var divisorDecimalCount = (divisor.toString().split('.')[1] ?? '').length;
    var decimalCount = dividendDecimalCount > divisorDecimalCount ? dividendDecimalCount : divisorDecimalCount;

    var dividendInt = parseInt(dividend.toFixed(decimalCount).replace('.', ''));
    var divisorInt = parseInt(divisor.toFixed(decimalCount).replace('.', ''));

    return (dividendInt % divisorInt) / Math.pow(10, decimalCount);
  } else {
    throw new Error("dividend or divisor value is invalid");
  }
}

export const get2DigitFormater = () => {
  return  new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
});
}

export const get4DigitFormater = () => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 4,
    maximumFractionDigits: 4,
});
}

export const format4DigitAmount = (value: string | number) => {
  const decimal4Formater = get4DigitFormater();
  const _value = +value ? +value : 0;
  return decimal4Formater.format(_value).replace("$", "");
};

export const format2DigitAmount = (value: string | number) => {
  const decimal2Formater = get2DigitFormater();
  const _value = +value ? +value : 0;
  return decimal2Formater.format(_value).replace("$", "");
};

export function getNumericVersion(version: string | undefined)
{
  if (version && typeof version === "string") {
    return Number(version.split(".").join(""));
  }
  return false; 
}

let axiosReqHeader: number = -1;

const addTokenToRequestHeader = async (request: InternalAxiosRequestConfig) => {
    const user = await Auth.currentSession();
    const accessToken = user.getAccessToken();
    const jwt = accessToken.getJwtToken();
    if (request.headers.skipInterceptor !== "true") {
      request.headers.AccessToken = jwt;
    }
    return request;
  }
  
export const addAxiosInterceptor = () => {
    axiosReqHeader = axios.interceptors.request.use(async (request) => {
        request = await addTokenToRequestHeader(request);
        return request;
    });
}

export const removeAxiosInterceptor = () => {
  if(axiosReqHeader){
    axios.interceptors.request.eject(axiosReqHeader);
    axiosReqHeader = -1;
  }
}

export const getQtyOptionsBasedOnProductType = (products:any, selectedProducts:any) => {
  let _selectedOption = null;
  let isAnyPipeProduct = false;
  let isAnyNonPipeProduct = false;

  selectedProducts.forEach((_obj:any) => {
      const currentProduct = products?.find(
          (_product:any) => _product.Product_ID === _obj.id
      );
      if (currentProduct?.Key2 === "Pipe") {
          isAnyPipeProduct = true;
      } else {
          isAnyNonPipeProduct = true;
      }
  });

  if (isAnyNonPipeProduct && isAnyPipeProduct) {
      _selectedOption = "cwt,ft";
  } else if (isAnyNonPipeProduct) {
      _selectedOption = "cwt";
  } else if (isAnyPipeProduct) {
      _selectedOption = "ft";
  }
  return _selectedOption;
}

export const getProductWithPrices = (product: any, userType: any, userRole: any) => {
  let productWithPrice = {};
    let userTypeTag = "Buyer_Pricing_";
    if (userType === userRole.sellerUser) userTypeTag = "Seller_Pricing_";
    else if (userType === userRole.neutralUser) userTypeTag = "Neutral_Pricing_";
    const cwt_price = product[`${userTypeTag}CWT`];
    const lb_price = product[`${userTypeTag}LB`];
    const ft_price = product[`${userTypeTag}Ft`];
    const lineSessionId = uuidv4();
    productWithPrice = {
      "id": product.Product_ID,
      "UI_Description": product.UI_Description,
      "cwt_price": cwt_price,
      "ft_price": ft_price,
      "lb_price": lb_price,
      "product_type_pipe": product.Key2 === "Pipe" ?? false,
      "line_session_id": lineSessionId,
    }
    return productWithPrice;
}

export const validateEmailWithSuffix = (email: string) => {

  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!emailRegex.test(email)) {
    return 'Invalid Email Format';
  } else {
    const suffixes = [".com", ".org", ".net", ".edu", ".co", ".co.in", ".site", ".in"];

    for (const suffix of suffixes) {
      if (email.endsWith(suffix))  return '';
    }
    return 'Please enter valid email address';
  }

};

export const prizeFormatter = (product, isUserChangedSelectedOption, selectedOption) => {
  const decimal2Formater = get2DigitFormater();
  const decimal4Formater = get4DigitFormater();

  let price = 0;
  if (!isUserChangedSelectedOption) {
      if (product.product_type_pipe && product.product_share_type === selectedOption) {
          price = +product.ft_price.replace("$", "");
          return decimal2Formater.format(price ? price : 0).replace("$", "");
      } else {
          price = +product.cwt_price.replace("$", "");
          return decimal2Formater.format(price).replace("$", "");
      }
  } else {
      price = +product[`${selectedOption}_price`].replace("$", "");
      if (selectedOption === "lb") {
          return decimal4Formater.format(price ? price : 0).replace("$", "");
      } else {
          return decimal2Formater.format(price ? price : 0).replace("$", "");
      }
  }
};

export const onbaoardingEmailValidator = (emailAddress, reEnterEmailAddress, setError, clearErrors)=>{
  const emailPattern = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

  if (emailPattern.test(emailAddress) && emailPattern.test(reEnterEmailAddress) && emailAddress === reEnterEmailAddress) {
      clearErrors(["emailAddress", "reEnterEmailAddress"]);
  } else {
      if (!emailPattern.test(emailAddress)) {
          setError("emailAddress", { message: "Email is not valid" });
      } else {
          setError("emailAddress", { message: "Email does not match!" });
      }
      if (!emailPattern.test(reEnterEmailAddress) && reEnterEmailAddress.length !== 0) {
          setError("reEnterEmailAddress", { message: "Email is not valid" });
      } else {
          setError("reEnterEmailAddress", { message: "Email does not match!" });
      }
  }
}

export const buyerSettingPayLoadFormatter = (data: BuyerSetting)=>{
  data.delivery_address = {
      "line1":data.delivery_address_line1,
      "city":data.delivery_address_city,
      "state_id":data.delivery_address_state_id,
      "zip":data.delivery_address_zip
  }
  data.address = {
      "line1":data.company_address_line1,
      "city":data.company_address_city,
      "state_id":data.company_address_state_id,
      "zip":data.company_address_zip
  }
  return {data};
}

export const sellerSettingPayLoadFormatter = (data: SellerSetting)=>{
  data.address = {
      "line1":data.company_address_line1,
      "city":data.company_address_city,
      "state_id":data.company_address_state_id,
      "zip":data.company_address_zip
  }
  return {data};
}

export const handleGetDeliveryDate = (deliveryDate, recevingHours) => {
  const date = dayjs(deliveryDate)
  const convertDate = date.format('dddd')
  let checkDeliveryDate = null;
  recevingHours?.forEach((recevingHour) => {
      if (recevingHour.is_user_available === 1 && convertDate.toLowerCase() === recevingHour.day.toLowerCase()) {
          checkDeliveryDate = deliveryDate
      }
  })
  return checkDeliveryDate;
}

export const handleDeliveryDateList = (deliveryDates:any[], receivingHours:ReceivingHours[]) => {
  return deliveryDates.map(x => {
      const addedWorkingDays = addWorkingDaysToDate(x.days_to_add);
      const isDeliveryDateDisable = handleGetDeliveryDate(addedWorkingDays, receivingHours);
      return { title: x.delivery_date_string, value: addedWorkingDays, disabled: !isDeliveryDateDisable }
  })
}


export const formatDollarPerUnit = (umUnit: string, umVal: any, i: any) => {
  const decimal2Formater = get2DigitFormater();
  const decimal4Formater = get4DigitFormater();

  if (umUnit === "Lb") {
    return decimal4Formater.format(umVal).replace("$", "");
  } else {
    return decimal2Formater.format(umVal).replace("$", "");
  }
}

export const getUnitPostScript = (unit: string) => {
  if (unit.toLowerCase() === 'cwt') {
      return "CWT"
  } else if (unit.toLowerCase() === 'ea') {
      return "Ea"
  } else if (unit.toLowerCase() === 'ft') {
      return "Ft"
  } else if (unit.toLowerCase() === 'lb') {
      return "LB"
  } else if (unit.toLowerCase() === 'net ton') {
      return "Net_Ton"
  }
}

/////CAN BE REMODIFIED
export const checkStateZipValidation = async (zipCode, stateCode, showStateZipError, clearErrors)=>{
  if (zipCode?.length > 4 && stateCode) {
      const payload = {
          data: {
              state_id: stateCode,
              zip_code: parseInt(zipCode),
          },
      };
      const checkStateZipResponse = await axios.post(
          import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
          payload
      );
      if (checkStateZipResponse.data.data === true) {
          clearErrors([stateCode, zipCode]);
      } else {
          showStateZipError();
      }
  }
}

export const submitNet30Details = async(user_id:any,payment_method:any,net_30_ein:any, dnb_number:any ,creditLine:any ,companyInfo: CompanyDetails| null) => {
  try{
    const requiredTrueVaultData = {
        company_name: companyInfo?.company_name,
        user_id,
        payment_method,
        net_30_ein,
        dnb_number
    }
    const documentIdFromTruevault= await getTruevaultData(requiredTrueVaultData);
    const payload = {
        "data": {
                "ein_number": net_30_ein.slice(-2).padStart(net_30_ein.length, 'x'),
                "duns_number": dnb_number,
                "agreed_terms": true,
                "desired_credit_limit": creditLine,
                "pgpm_mapping_id": 4,
                "reference_document_id": documentIdFromTruevault ?? ''
        }
    };

    const res = await axios.post(import.meta.env.VITE_API_SERVICE + '/user/mobile/buyer/saveBnplRequest', payload, {
        headers: {
            UserId: user_id
        }
    })
    if (res.status === 200) {
        return res;
    }
  }catch(err) {
      console.error(err);
      throw err;
  }
}

const getTruevaultData = async (net30PaymentDetail: any) => {
  try {
      const res = await axios.get(import.meta.env.VITE_API_PHP_SERVICE + '/api/v1/widget/truevault/get-access-token');
      const accessToken = res.data.data.access_token;
      let buyerPaymentData = {};
      buyerPaymentData = {
          "document": net30PaymentDetail
      }
      const client = new TrueVaultClient({ accessToken });
      try {
          const response = await client.createDocument(import.meta.env.VITE_TRUE_VAULT_ID_BUYER_VAULT_ID, null, buyerPaymentData);
          const documentIdFromTruevault = response.id;
          if (!documentIdFromTruevault) {
              dispatchRaygunError(new Error('TrueVault error: TruevoltObject = ' + JSON.stringify(response)), [trueVaultRaygunError]);
          }
          return documentIdFromTruevault;

      } catch (error) {
          console.error("Error creating document:", error);
          dispatchRaygunError(error, [trueVaultRaygunError]);
      }

  } catch (err) {
      throw err;
  }

}

export const saveUserSetting =  (apiRoutes:string, data:any, userData:any)=>{
   return axios.post(import.meta.env.VITE_API_SERVICE + apiRoutes, {data}, {
      headers: {
          UserId: userData.data.id
      }
  })
}

export const uploadBuyerDocumentInfoToS3 = async (file:any, userData:any, prefixUrl:any,bucket_name:any)=>{
  let index = file.name.length - 1;
  for (; index >= 0; index--) {
      if (file.name.charAt(index) === '.') {
          break;
      }
  }
  const ext = file.name.substring(index + 1, file.name.length);

  const objectKey = import.meta.env.VITE_ENVIRONMENT + '/' + userData.data.id + '/' + prefixUrl + '-' + uuidv4() + '.' + ext;

  const payload = {
      data: {
          bucket_name ,
          "object_key": objectKey,
          "expire_time": 300

      }
  }
  let certUrl = 'https://' + payload.data.bucket_name + ".s3.amazonaws.com/" + payload.data.object_key;
  console.log("Certurl",certUrl)
  try{
  const response = await axios.post(import.meta.env.VITE_API_SERVICE + '/user/get_signed_url', payload)
  const signedUrl = response.data.data;
  await axios.put(signedUrl, file)
  } catch(err){
    throw err;
  }
  return certUrl;
}