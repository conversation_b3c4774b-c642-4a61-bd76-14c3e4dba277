
import { useEffect, useRef, useState } from 'react';
import { FacebookShareButton, LinkedinShareButton, TwitterShareButton, WhatsappShareButton } from 'react-share';
import FacebookIcon from '../../../assets/mobile-images/FB.svg';
import LinkedinIcon from '../../../assets/mobile-images/linkedin.svg';
import TwitterIcon from '../../../assets/mobile-images/X.com.svg';
import WhatsappIcon from '../../../assets/mobile-images/WhatsApp.svg';
import EmailIcon from '../../../assets/mobile-images/Email.svg';
import CopyLinkIcon from '../../../assets/mobile-images/CopyLink.svg';
import PhoneIcon from '../../../assets/mobile-images/Phone.svg';
import { ReactComponent as CloseIcon } from '../../../assets/mobile-images/Close.svg';
import { IonModal } from '@ionic/react';
import { validateEmailWithSuffix, useSaveShareVideoAnalytic } from '@bryzos/giss-ui-library';
import { Capacitor } from '@capacitor/core';
import { videoLibraryShare } from '../../library/common';

const ShareVideoUrl = ({ openShare, shareVideoPopupClose, videoData = {}, isIOSDevice }) => {
  const phoneNumberRegex = /^\+?[1-9]{1,4}?[-.\s]?(\(?\d{1,5}\)?[-.\s]?)?(\d[-.\s]?){5,14}$/;
  const phoneNumberLimit = 15;
  const saveShareVideoAnalytic = useSaveShareVideoAnalytic();
  const [activeShare, setActiveButton] = useState("");
  const [showEmailInput, setShowEmailInput] = useState(false);
  const [email, setEmail] = useState("");
  const [emailError, setEmailError] = useState('');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [copied, setCopied] = useState(false);
  const [disableSendBtn, setDisableSendBtn] = useState(false);
  const modal = useRef<HTMLIonModalElement>(null);


  useEffect(() => {
    if (copied) {
      setShowSuccessMessage(true);
      setSuccessMessage("Copied Successfully")
    } else {
      setShowSuccessMessage(false);
      setSuccessMessage('')
    }
  }, [copied])

  const handleEmailChange = (event) => {
    setShowSuccessMessage(false);
    setDisableSendBtn(false);
    const inputData = event.target.value.trim();
    if (activeShare === 'email') {
      setEmail(inputData);
    } else {
      if(inputData.length <= phoneNumberLimit){
        setEmail(inputData);
      }
    }
  };

  const handleShareBtnClick = (buttonName) => {
    if (buttonName === "email" || buttonName === "sms") {
      setShowEmailInput(true);
    } else {
      setShowEmailInput(false);
    }
    setShowSuccessMessage(false);
    setSuccessMessage('');
    setEmail("");
    setEmailError('')
    setActiveButton(buttonName);
    
    if(!(buttonName === 'email' || buttonName === 'copy-link' || buttonName ==='sms')){
      sendShareAnalyticsData(buttonName);
    }
    
    if(isIOSDevice && buttonName === 'facebook'){
      if (Capacitor.isNativePlatform()) {
        window.open(videoLibraryShare.facebookAppUrl + encodeURIComponent(videoData.share_video_url),'_blank')
      } else {
        window.open(videoLibraryShare.facebookWebUrl + encodeURIComponent(videoData.share_video_url), '_blank')
      }
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 1300);
      })
      .catch(err => console.error('Failed to copy:', err));
  };

  const closeShareVideoPopup = () => {
    setEmail("")
    setActiveButton("");
    setShowSuccessMessage(false);
    setSuccessMessage('')
    shareVideoPopupClose();
    setShowEmailInput(false);
    setEmailError('')
  }

  const handleSendEmail = async () => {
    if(!handleValidation(email)){
      if (activeShare === 'email') {
        if (email === '') {
          setEmailError('Please enter email address')
        } else if (email !== '' && emailError === '') {
          setDisableSendBtn(true);
          try {
            sendShareAnalyticsData('email', email)
            setDisableSendBtn(false);
            setEmail('')
            setShowSuccessMessage(true);
            setSuccessMessage('Sent Successfully')
          } catch (err) {
            console.error(err);
            setEmail('')
            setDisableSendBtn(false)
            setEmailError(err.message)
          }
        }
      } else {
        openSMSApp(email);
      }
    }
  }

  const openSMSApp = (phoneNumber) => {
    // Construct the SMS URL
    const smsUrl = `sms:${phoneNumber}?body=${encodeURIComponent(videoData.share_video_url)}`;
    if (Capacitor.isNative) {
      sendShareAnalyticsData('sms', phoneNumber)
      window.location.href = smsUrl;
    }
  };

  const handleValidation = (inputData) => {
    let returnData = false;
    if (activeShare === 'email') {
      if (inputData?.length !== 0) {
        const emailErrorData = validateEmailWithSuffix(inputData);
        if(emailErrorData){
          returnData = true;
        }
        setEmailError(validateEmailWithSuffix(inputData));
        setDisableSendBtn(false);
      }
    } else {
      if(inputData?.length <= phoneNumberLimit){
        if(!phoneNumberRegex.test(inputData)){
          setDisableSendBtn(true);
          setEmailError('Please enter valid phone number');
          returnData = true;
        }else{
          setDisableSendBtn(false);
          setEmailError('');
          returnData = false;
        }
      }
    }
    return returnData;
  }

  const sendShareAnalyticsData = (shareVia: string, receiver:string|number|null=null) => {
    const payload = {
        "share_via":shareVia,
        "receiver":receiver,
        "video_id":videoData.video_id
    }
    saveShareVideoAnalytic.mutate(payload);
  }

  return (
    <IonModal
      className={'shareVideoMain'} ref={modal} isOpen={openShare} backdropDismiss={false}
    >
      <div className={'sharePopupContent'}>
        <span className='closeShareIcon' onClick={() => { closeShareVideoPopup() }}><CloseIcon /></span>
        <div className={'titlePopup'}>Share this video</div>
        <div className='iconSectionBox'>
          <LinkedinShareButton url={videoData.share_video_url} >
            <div className={activeShare === 'linkedIn' ? 'activeShareBtn' : 'shareBtn'} onClick={() => handleShareBtnClick("linkedIn")} >
              <img src={LinkedinIcon} alt="LinkedIn" />
              <div className='btnTxt'>Linkedin</div>
            </div>
          </LinkedinShareButton>
            <FacebookShareButton url={videoData.share_video_url}>
              <FacebookShareTemplate 
              activeShare={activeShare}
              handleShareBtnClick={handleShareBtnClick}/>
            </FacebookShareButton>

          <TwitterShareButton url={videoData.share_video_url}>
            <div className={activeShare === 'x' ? 'activeShareBtn' : 'shareBtn'} onClick={() => handleShareBtnClick("x")}>
              <img src={TwitterIcon} alt="Twitter" />
              <div className='btnTxt'>X</div>
            </div>
          </TwitterShareButton>

          <WhatsappShareButton url={videoData.share_video_url}>
            <div className={activeShare === 'whatsApp' ? 'activeShareBtn' : 'shareBtn'} onClick={() => handleShareBtnClick("whatsApp")}>
              <img src={WhatsappIcon} alt="WhatsApp" />
              <div className='btnTxt'>Whatsapp</div>
            </div>

          </WhatsappShareButton>

          <button className={activeShare === 'email' ? 'activeShareBtn' : 'shareBtn'} onClick={() => handleShareBtnClick("email")}>
            <img src={EmailIcon} alt="Email" />
            <div className='btnTxt'>Email</div>
          </button>
          <button className={activeShare === 'sms' ? 'activeShareBtn' : 'shareBtn'} onClick={() => handleShareBtnClick("sms")}>
            <img src={PhoneIcon} alt="Phone" />
            <div className='btnTxt'>Message</div>
          </button>

          <button className={activeShare === 'copy-link' ? 'activeShareBtn' : 'shareBtn'} onClick={() => handleShareBtnClick("copy-link")}>
            <img src={CopyLinkIcon} alt="Copy Link" onClick={() => { copyToClipboard(videoData.share_video_url) }} />
            <div className='btnTxt'>Copy link</div>
          </button>
        </div>
        {showEmailInput && (
          <div className='SendInputSection'>
            {activeShare === 'email' ? 
            (<input
              type='email'
              value={email}
              onChange={handleEmailChange}
              onBlur={(e)=>{handleValidation(e.target.value)}}
              placeholder={'Email Address'}
            />)
            :
            (<input
              type='tel'
              value={email}
              onChange={handleEmailChange}
              onBlur={(e)=>{handleValidation(e.target.value)}}
              placeholder={'Phone Number'}
            />)}
            <button disabled={!!(disableSendBtn || !email)} onClick={handleSendEmail}>Send</button>
          </div>
        )}
        {emailError.length !== 0 &&
          <p className='emailError'>{emailError}</p>
        }
        {showSuccessMessage &&
          <p className='successMessageStyle'>{successMessage}</p>
        }
      </div>

    </IonModal>
  )
}

const FacebookShareTemplate = (
  {activeShare, handleShareBtnClick}: {
    activeShare: string;
    handleShareBtnClick: Function;
    }) => {
      return (
        <button className={activeShare === 'facebook' ? 'activeShareBtn' : 'shareBtn'} onClick={() => handleShareBtnClick("facebook")}>
          <img src={FacebookIcon} alt="Facebook" />
          <div className='btnTxt'>Facebook</div>
        </button>
      )
  }

export default ShareVideoUrl;