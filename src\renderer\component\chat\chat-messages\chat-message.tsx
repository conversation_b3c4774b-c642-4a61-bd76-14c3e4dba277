import clsx from 'clsx';
import styles from './chat-messages.module.scss';
import React, { useState } from 'react';
import ReactionComponent from './reactions/ReactionComponent';
import ReactionMenu from './reactions/ReactionMenu';
import { ClickAwayListener, Popover } from '@mui/material';

type Props = {
    obj: any;
    sdk: any;
    chatUserId: any;
    handleReactionSelect: any;
    autoScrollToBottom:Function
};
export const ChatMessage: React.FC<Props> = ({
    obj,
    sdk,
    chatUserId,
    handleReactionSelect,
    autoScrollToBottom
}) => {
    const [openReactionMenu, setOpenReactionMenu] = useState(false);
    const [anchorEl, setAnchorEl] = useState(null);

    const handleOpenReactionMenu = (event:any) => {
        setOpenReactionMenu(true);
        setAnchorEl(event.currentTarget);
    };

    const handleCloseReactionMenu = () => {
        setOpenReactionMenu(false);
    };

    return (
        <>
            <ClickAwayListener onClickAway={() => setOpenReactionMenu(false)}>
                <>
                <div onClick={handleOpenReactionMenu} key={obj._id} className={clsx(styles.messageText, obj.user._id === chatUserId && styles.ownMessages)}>
                    <p dangerouslySetInnerHTML={{ __html: obj.message }}></p>
                    {!obj.groupSameUserMsg && <div className={styles.chatArrow}></div>}
                    <ReactionComponent reactions={obj.likes} sdk={sdk} messageId={obj._id} autoScrollToBottom={autoScrollToBottom}/>
                    <span className={styles.chatTime}>{obj.time}</span>
                </div>
                {/* {openReactionMenu && <ReactionMenu onReactionSelect={(key) => { handleReactionSelect(key, obj) }} />} */}
                <Popover
                open={openReactionMenu}
                anchorEl={anchorEl}
                onClose={handleCloseReactionMenu}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
                disableScrollLock 
            >
                <ReactionMenu onReactionSelect={(key) => { handleReactionSelect(key, obj) }} />
            </Popover>
            </>
            </ClickAwayListener>
        </>
    );
};
