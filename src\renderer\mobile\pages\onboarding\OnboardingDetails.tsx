//@ts-nocheck
import clsx from 'clsx';
import styles from './OnboardingDetails.module.scss'
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { schema } from './onboardingDetailsSchema';
import { useEffect, useState } from 'react';
import { EmptyString, routes, userTypes } from '../../library/common';
import { ReactComponent as ErrorEmailIcon } from '../../../assets/images/errorEmail.svg';
import { Autocomplete, Fade, Tooltip } from '@mui/material';
import axios from 'axios';
import { IonContent, IonPage, useIonLoading, useIonRouter, useIonViewWillEnter, useIonViewWillLeave } from '@ionic/react';
import { CustomMenu } from '../../components/CustomMenu';
import { onbaoardingEmailValidator } from '../../library/helper';
import { ReactComponent as CrossLogo } from '../../../assets/mobile-images/crossBtn.svg';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import { ReactComponent as ShowPassIcon } from '../../../assets/mobile-images/show-pass.svg';
import { ReactComponent as HidePassIcon } from '../../../assets/mobile-images/hide-pass.svg';

function OnboardingMobileDetails() {
    const router = useIonRouter();
    const [present, dismiss] = useIonLoading();
    const { register, handleSubmit, getValues, clearErrors, setValue, watch, setError, control, reset, formState: { errors, dirtyFields, isDirty, isValid } } = useForm({
        resolver: yupResolver(schema),
        defaultValues:{
            userType: "",
    companyName: null,
    companyEntity: null,
    companyNameObj: {},
    firstName: "",
    lastName: "",
    zipCode: "",
    emailAddress: "",
    password: '',
    reEnterEmailAddress: ""
        },
        mode: "onBlur"
    });

    const showCategory = true;
    const [isDropdownOpen, setIsDropdownOpen] = useState(false)
    const [isCompanyDropdownOpen, setIsCompanyDropdownOpen] = useState(false)
    const [isInputFocused, setIsInputFocused] = useState({
        userType: false,
        CompanyName: false,
        CompanyEntity: false,
        ZipCode: false,
        FirstName: false,
        LastName: false,
        EmailAddress: false,
        ReEnterEmailAddress: false,
        submitBtn: false,
    });
    const [companyNameInput, setCompanyNameInput] = useState("");
    const [companyNameValue, setCompanyNameValue] = useState(null);
    const [openCompanyName, setOpenCompanyName] = useState(false);
    const [onboardCompanies, setOnboardCompanies] = useState([]);
    const [companyEntityInput, setCompanyEntityInput] = useState("");
    const [companyEntityValue, setCompanyEntityValue] = useState(null);
    const [onboardCompanyEntities, setOnboardCompanyEntities] = useState([]);
    const [disableCompanyEntity, setDisableCompanyEntity] = useState(true);
    const [userData, setUserData] = useState(router.routeInfo.routeOptions?.state);
    const { setShowLoader, showErrorPopup} = useGlobalStore();

    const [passwordVisibility, setPasswordVisibility] = useState({
        password1: true,
        password2: true,
      });
    
      const togglePasswordVisibility = (field) => {
          setPasswordVisibility((prevState) => ({
              ...prevState,
              [field]: !prevState[field],
          }));
      };

    useIonViewWillEnter(() => {
        setCompanyNameInput("")
        setCompanyEntityInput("")
        reset();
        getUserCompanyData();
    }, [])

    useIonViewWillLeave(() => {
        setUserData(null);
        reset();
    }, [])

    useEffect(() => {
        if (userData?.data) {
            setValue("userType", userData?.data.userType)
            setValue("companyName", userData?.data.companyName)
            setValue("firstName", userData?.data.firstName)
            setValue("lastName", userData?.data.lastName)
            setValue("emailAddress", userData?.data.emailAddress)
            if (userData?.errorMessage) {
                setValue("reEnterEmailAddress", "")
                setError("emailAddress", { message: userData.errorMessage }, { shouldFocus: true })
            }
            setValue("zipCode", userData?.data.zipCode)
            setValue("companyEntity", userData?.data.companyEntity)
            userData.zipcodeErrorMessage && setError("zipCode", { message: 'Enter valid zip code' })
        }
    }, [userData])

    useEffect(() => {
        if (watch('companyName')?.length > 1) {
            setDisableCompanyEntity(false)
            const companyData: any = onboardCompanies?.find((companyData: any) => companyData.company_name === watch("companyName"))
            setOnboardCompanyEntities(companyData?.client_company ?? [])
        } else {
            setDisableCompanyEntity(true)
            setOnboardCompanyEntities([])
        }
    }, [watch('companyName')])

    const handleInputFocus = (inputName: any) => {
        setIsInputFocused((inputState) => ({
            ...inputState,
            [inputName]: true,
        }));
    };

    const getUserCompanyData = async() => {
        setShowLoader(true);
        try{
            const getCompanyResponse = await axios.get(import.meta.env.VITE_API_SERVICE + '/user/company')
            if (getCompanyResponse?.data?.data && typeof getCompanyResponse.data.data !== 'string') {
                setOnboardCompanies(getCompanyResponse.data.data);
            }
            setShowLoader(false);
        }catch(err){
            console.error(err)
            setShowLoader(false);
        }
    }

    const handleInputBlur = (inputName: any) => {
        setIsInputFocused((inputState) => ({
            ...inputState,
            [inputName]: false,
        }));
    };
    const isAnyInputFocused = Object.values(isInputFocused).some((state) => state);

    const onSubmit = async (data: any) => {
        try {
            setShowLoader(true);
            const verifyOnBoardUserEmailPayload = {
                data: {
                    email_id: data.emailAddress,
                },
            };
            const verifyOnBoardUserZipcodePayload = {
                data: {
                    zip_code: data.zipCode,
                },
            };
            const responseData = await axios.post(import.meta.env.VITE_API_SERVICE + "/user/verifyOnBoardUserEmail", verifyOnBoardUserEmailPayload);
            const zipcodeResponseData = await axios.post(import.meta.env.VITE_API_SERVICE + "/user/verifyZipCode", verifyOnBoardUserZipcodePayload)
            if (responseData.data.data?.error_message || zipcodeResponseData.data.data?.error_message) {
                const onboardData: any = {
                    data,
                    errorMessage: responseData.data.data.error_message,
                    zipcodeErrorMessage: zipcodeResponseData.data.data.error_message,
                    tncCheckbox: false,
                }
                setUserData(onboardData)
                // router.push("/onboarding-detail", undefined, undefined, onboardData)
                // navigate(routes.onboardingDetails, {
                //   state: {
                //     data,
                //     errorMessage: responseData.data.error_message,
                //     zipcodeErrorMessage: zipcodeResponseData.data.error_message,
                //     tncCheckbox: false,
                //   },
                // });
                setShowLoader(false);
            } else {
                if (userData?.data?.userType && userData?.tncCheckbox && userData?.tncData) {
                    const payload = {
                        data: {
                            user_type: data.userType,
                            company_name: data.companyName,
                            first_name: data.firstName,
                            last_name: data.lastName,
                            email_id: data.emailAddress,
                            bryzos_terms_condtion_id: userData?.tncData.id,
                            accepted_terms_and_condition:
                                userData?.tncData.terms_conditions_version,
                            zip_code: data.zipCode,
                            client_company: data.companyEntity
                        },
                    };
                    const responseData = await axios.post(import.meta.env.VITE_API_SERVICE + "/user/onBoard", payload);
                    if (responseData.data.data.error_message) {
                        const onboardData: any = {
                            data,
                            errorMessage: responseData.data.data.error_message,
                            tncCheckbox: userData?.tncCheckbox,
                        }
                        setUserData(onboardData)
                        // router.push("/onboarding-detail", undefined, undefined, onboardData)
                        // navigate(routes.onboardingDetails, {
                        //   state: {
                        //     data,
                        //     errorMessage: responseData.data.error_message,
                        //     tncCheckbox: userData?.tncCheckbox,
                        //   },
                        // });
                        setShowLoader(false);
                    } else {
                        router.push("/onboarding-thank-you",{animate:true,direction:'forward'});
                        setShowLoader(false);
                        // navigate(routes.onboardingThankYou);
                    }
                } else {
                    router.push("/onboarding-tnc",{animate:true,direction:'forward'}, undefined, { state: data })
                    setShowLoader(false);
                    //   navigate(routes.onboardingTnc, { state: data });
                }

            }


        } catch (error) {
            setShowLoader(false);
            console.error(error);
        }
    };

    const handleEmailBlur = () => {
        const emailAddress = getValues('emailAddress')?.trim();
        const reEnterEmailAddress = getValues('reEnterEmailAddress')?.trim();
        onbaoardingEmailValidator(emailAddress, reEnterEmailAddress, setError, clearErrors);
    }

    const handlePasswordBlur = () => {
        const password = getValues('password')?.trim();
        const confirmPassword = getValues('confirmPassword')?.trim();
        if (password === confirmPassword) {
            clearErrors(["password", "confirmPassword"]);
        } else {
            setError("password", { message: "Password does not match!" });
        }
    }

    return (
        <IonPage>
            <IonContent>
                <div className={clsx(styles.onboardingDetailsMain)} >
                    <div className={styles.onboardingLogo}>
                        <img src='/onboardingLogo.png' />
                    </div>
                    <div className={styles.onboardingForm}>
                        <CustomMenu
                            keyHandler={() => { handleInputFocus('userType'); setIsDropdownOpen(true) }}
                            control={control}
                            name={'userType'}
                            placeholder={'Choose User Type'}
                            MenuProps={{
                                classes: {
                                    paper: clsx(styles.Dropdownpaper, 'selectUserType'),
                                    list: styles.muiMenuList,
                                },
                            }}
                            onOpen={() => { setIsDropdownOpen(true) }}
                            onClick={() => {
                                handleInputFocus('userType')
                                setIsDropdownOpen(true)
                            }}
                            onClose={() => {
                                handleInputBlur('userType')
                                setIsDropdownOpen(false)
                            }}
                            category={'THIS CANNOT BE CHANGED...CHOOSE WISELY.'}
                            items={userTypes.map(userType => ({ title: userType.name, value: userType.value }))}
                            className={clsx('selectDropdown2', isDropdownOpen ? 'dropdownFocused' : '')}
                        />
                        <div className='w100'>
                            <Controller
                                name='companyName'
                                control={control}
                                render={({ field: { ...rest } }) => (
                                    <Autocomplete
                                        freeSolo
                                        id={`combo-box-demo`}
                                        open={companyNameInput.length > 1 && openCompanyName}
                                        onOpen={() => setOpenCompanyName(true)}
                                        onClose={(e) => {
                                            setOpenCompanyName(false)
                                        }}
                                        options={onboardCompanies?.length ? onboardCompanies : []}
                                        value={companyNameValue}
                                        inputValue={companyNameInput}
                                        className={clsx(styles.inputOnboarding1)}
                                        onInputChange={(event, value, reason) => {
                                            if (reason !== "reset") {
                                                setCompanyNameInput(value)
                                                setValue('companyName', value, { shouldDirty: true })
                                            }
                                        }}
                                        disablePortal={true}
                                        classes={{
                                            root: styles.autoCompleteDesc,
                                            popper: styles.autocompleteDescPanel,
                                            paper: styles.autocompleteDescInnerPanel,
                                            listbox: styles.listAutoComletePanel,
                                        }}
                                        getOptionLabel={(item: any) => {
                                            return item.company_name ? item.company_name : "";
                                        }}
                                        renderInput={(params) => (
                                            <div className={clsx(styles.companyNameInput, errors?.companyName && styles.error)} ref={params.InputProps.ref}>
                                                <input
                                                    type="text"
                                                    {...params.inputProps}
                                                    placeholder="Main Company Name"
                                                    className={clsx(styles.poDescription)}
                                                />
                                            </div>
                                        )}
                                        onChange={(event, item, reason) => {
                                            if (reason === "selectOption") {
                                                setCompanyNameInput(item?.company_name)
                                                setValue('companyName', item?.company_name, { shouldDirty: true })
                                            }
                                            setCompanyNameValue(item)
                                            rest.onChange(item?.company_name ?? companyNameInput);
                                        }}
                                        onBlur={(e: any) => {
                                            if (/<|>/g.test(e.target.value) || e.target.value.length === 0) {
                                                setError("companyName", { message: 'Company Name is not valid' })
                                            } else {
                                                clearErrors('companyName')
                                            }
                                            rest.onBlur(e);
                                            handleInputBlur('CompanyName')
                                            setIsCompanyDropdownOpen(false)
                                        }}
                                        onFocus={() => {
                                            handleInputFocus('CompanyName')
                                            setIsCompanyDropdownOpen(true)
                                        }}
                                    />
                                )}
                            />

                            <div className={clsx(styles.inputOnboarding1)}>
                                <Controller
                                    name="companyEntity"
                                    control={control}
                                    render={({ field: { ...rest } }) => (
                                        <Autocomplete
                                            disabled={disableCompanyEntity}
                                            className={clsx(styles.inputOnboarding1)}
                                            classes={{
                                                root: styles.autoCompleteDesc,
                                                popper: styles.autocompleteDescPanel,
                                                paper: styles.autocompleteDescInnerPanel,
                                                listbox: styles.listAutoComletePanel,
                                            }}
                                            id={`combo-box-demo1`}
                                            freeSolo
                                            value={companyEntityValue}
                                            inputValue={companyEntityInput}
                                            onChange={(event, value: any) => {
                                                setCompanyEntityValue(value);
                                                rest.onChange(value ?? null);
                                            }}
                                            onInputChange={(event, newInputValue) => {
                                                setCompanyEntityInput(newInputValue);
                                                setValue("companyEntity", newInputValue, { shouldDirty: true });
                                            }}
                                            onBlur={(e: any) => {
                                                if (/<|>/g.test(e.target.value) || e.target.value.length === 0) {
                                                    setError("companyEntity", { message: 'Company Entity is not valid' })
                                                } else {
                                                    clearErrors('companyEntity')
                                                }
                                                rest.onBlur(e);
                                                handleInputBlur('CompanyEntity')
                                                setIsCompanyDropdownOpen(false)
                                            }}
                                            onFocus={() => {
                                                handleInputFocus('CompanyEntity')
                                                setIsCompanyDropdownOpen(true)
                                            }}
                                            disablePortal={true}
                                            options={onboardCompanyEntities?.length ? onboardCompanyEntities : []}
                                            renderInput={(params) => (
                                                <div className={clsx(styles.companyNameInput, errors?.companyEntity && styles.error)} ref={params.InputProps.ref}>
                                                    <input
                                                        type="text"
                                                        {...params.inputProps}
                                                        placeholder="Your Company Entity/Location"
                                                        className={clsx(styles.poDescription)}
                                                    />
                                                </div>
                                            )}
                                            getOptionLabel={(item) => {
                                                return item ?? "";
                                            }}
                                        />
                                    )}
                                />
                            </div>

                            <div className={clsx(styles.inputOnboarding2, styles.emailErrorContainer)}>
                            <input className={clsx(styles.inputOnboarding, errors?.firstName && styles.error)}
                                {...register("firstName")}
                                placeholder='First Name'
                                onFocus={() => handleInputFocus('FirstName')}
                                onBlur={(e) => {
                                    register("firstName").onBlur(e);
                                    handleInputBlur('FirstName')
                                }} />
                            <input className={clsx(styles.inputOnboarding, errors?.lastName && styles.error)}
                                    {...register("lastName")}
                                    placeholder='Last Name'
                                    onFocus={() => handleInputFocus('LastName')}
                                    onBlur={(e) => {
                                        register("lastName").onBlur(e);
                                        handleInputBlur('LastName')
                                    }}
                                />
                                <Tooltip
                                    title={errors?.emailAddress?.message || errors?.reEnterEmailAddress?.message}
                                    arrow
                                    placement={"top"}
                                    disableInteractive
                                    TransitionComponent={Fade}
                                    TransitionProps={{ timeout: 200 }}
                                    classes={{
                                        tooltip: "inputQtyTooltip",
                                    }}
                                >
                                    <input {...register("emailAddress")} className={clsx(styles.inputOnboarding, (errors?.emailAddress || errors?.reEnterEmailAddress) && styles.error)} type='email' placeholder='Enter Email Address'
                                        onFocus={() => handleInputFocus('EmailAddress')}
                                        onBlur={(e) => {
                                            register("emailAddress").onBlur(e);
                                            handleInputBlur('EmailAddress')
                                            handleEmailBlur(e)
                                        }} />
                                </Tooltip>

                                <input {...register("reEnterEmailAddress")} className={clsx(styles.inputOnboarding, (errors?.emailAddress || errors?.reEnterEmailAddress) && styles.error)} type='email' placeholder='Re-Enter Email Address'
                                    onFocus={() => handleInputFocus('ReEnterEmailAddress')}
                                    onBlur={(e) => {
                                        register("reEnterEmailAddress").onBlur(e);
                                        handleInputBlur('ReEnterEmailAddress');
                                        handleEmailBlur(e);
                                    }} />
                                {(errors?.emailAddress || errors?.reEnterEmailAddress) &&
                                    <div className={styles.errorBorder}>
                                        <ErrorEmailIcon />
                                    </div>
                                }
                                {/* <Tooltip
                                    title={errors?.zipCode?.message}
                                    arrow
                                    placement={"top"}
                                    disableInteractive
                                    TransitionComponent={Fade}
                                    TransitionProps={{ timeout: 200 }}
                                    classes={{
                                        tooltip: "inputQtyTooltip",
                                    }}
                                > */}
                            

                                <div className={styles.passwordErrorContainer}>
                                    <div className={styles.confirmPasswordInput}>
                                     <input {...register("password")} className={clsx(styles.inputOnboarding, (errors?.password || errors?.confirmPassword) && styles.error)} type={passwordVisibility.password1 ? 'password' : 'text'} placeholder='Password'
                                        onFocus={() => handleInputFocus('password')}
                                        onChange={(e) => {
                                        e.target.value = e.target.value.trim();
                                        register("password").onChange(e);
                                        setValue('password', e.target.value);
                                        }}
                                        onBlur={(e) => {
                                        register("password").onBlur(e);
                                        handleInputBlur('password')
                                        handlePasswordBlur()
                                        }} />
                                        <button className={clsx(styles.showPassBtn,(errors?.password || errors?.confirmPassword) && styles.errorPassIcon)} onClick={() => togglePasswordVisibility('password1')}>
                                            {passwordVisibility.password1 ? <ShowPassIcon /> : <HidePassIcon />}
                                        </button>
                                    
                                    </div>
                                    <div className={styles.confirmPasswordInput}>
                                    <input {...register("confirmPassword")} className={clsx(styles.inputOnboarding, (errors?.password || errors?.confirmPassword) && styles.error)} type={passwordVisibility.password2 ? 'password' : 'text'} placeholder='Confirm Password'
                                        onFocus={() => handleInputFocus('confirmPassword')}
                                        onChange={(e) => {
                                        e.target.value = e.target.value.trim();
                                        register("confirmPassword").onChange(e);
                                        setValue('confirmPassword', e.target.value);
                                        }}
                                        onBlur={(e) => {
                                        register("confirmPassword").onBlur(e);
                                        handleInputBlur('confirmPassword')
                                        handlePasswordBlur()
                                        }} />
                                    <button className={clsx(styles.showPassBtn,(errors?.password || errors?.confirmPassword) && styles.errorPassIcon)} onClick={() => togglePasswordVisibility('password2')}>
                                        {passwordVisibility.password2 ? <ShowPassIcon /> : <HidePassIcon />}
                                    </button>
                                    </div>
                                        {(errors?.password || errors?.confirmPassword) &&
                                            <div className={styles.errorBorder}>
                                                <ErrorEmailIcon />
                                            </div>
                                        }
                                 </div>
                                {(errors?.password || errors?.confirmPassword) && <span className='errorText2 positionAbsolute'><CrossLogo />{errors?.password?.message || errors?.confirmPassword?.message}</span>}
                                <input {...register("zipCode")} className={clsx(styles.inputOnboarding, errors?.zipCode && styles.error)} type='tel' placeholder='Enter your zip code'
                                    onFocus={() => handleInputFocus('ZipCode')}
                                    onChange={(e) => {
                                        register("zipCode").onChange(e);
                                        const zipCode = e.target.value.replace(/\D/g, '');
                                        setValue('zipCode', zipCode);
                                    }}
                                    maxLength={5}
                                    onBlur={(e) => {
                                        register("zipCode").onBlur(e);
                                        handleInputBlur('ZipCode')
                                    }} />
                                {errors?.zipCode?.message && <span className='errorText2 positionAbsolute'><CrossLogo />{errors?.zipCode?.message}</span>}
                                {/* </Tooltip> */}

                            </div>
                        </div>

                    </div>
                    <div className={clsx(styles.btnSection, (Object.keys(errors).length === 0 && isValid) ? styles.isEnabled : '')}>
                        <button className={clsx(styles.nextBtn, (Object.keys(errors).length === 0 && isValid) && styles.enableBtn)} disabled={!(Object.keys(errors).length === 0 && isValid)}
                            onClick={handleSubmit(onSubmit)}> Next </button>
                    </div>

                    <div className={clsx(styles.loginBtnSection, (Object.keys(errors).length === 0 && isValid) ? styles.isEnabled : '')}>
                        Already have an account? <button onClick={() => router.push('/login',{animate:true,direction:'forward'})}>Login</button>
                    </div>

                </div>
            </IonContent>
        </IonPage>
    );
}
export default OnboardingMobileDetails;
