import drive from '@googleapis/drive';
import { join } from 'path';

export const RootFolderId = process.env.DRIVE_FOLDERID;

const getDriveService = () => {
  const KEYFILEPATH = join('./', 'config.json');
  const SCOPES = ['https://www.googleapis.com/auth/drive'];

  const auth = new drive.auth.GoogleAuth({
    keyFile: KEYFILEPATH,
    scopes: SCOPES,
  });
  const driveService = drive.drive({ version: 'v3', auth });
  return driveService;
};

export default getDriveService;