// @ts-nocheck
import { <PERSON><PERSON><PERSON>, IonContent, useIonRouter, useIonViewWillEnter, useIonLoading, useIonViewWillLeave } from '@ionic/react';
import styles from './Setting.module.scss';
import { ReactComponent as ProfileIcon } from '../../../../assets/mobile-images/Profile.svg';
import { ReactComponent as ChangePasswordIcon } from '../../../../assets/mobile-images/password.svg';
import { ReactComponent as CompanyInfoIcon } from '../../../../assets/mobile-images/Company_Info.svg';
import { ReactComponent as DeliverToIcon } from '../../../../assets/mobile-images/Deliver_To.svg';
import { ReactComponent as FundingSettingsIcon } from '../../../../assets/mobile-images/money-check-edit.svg';
import { ReactComponent as YourProductsIcon } from '../../../../assets/mobile-images/boxes.svg';
import { ReactComponent as DocumentsLibraryIcon } from '../../../../assets/mobile-images/Documents_Library.svg';
import { ReactComponent as MyReportsIcon } from '../../../../assets/mobile-images/MyReports.svg';
import { ReactComponent as LogoutIcon } from '../../../../assets/mobile-images/Logout.svg';
import { ReactComponent as SettingsRightArrowIcon } from '../../../../assets/mobile-images/SettingsRightArrow.svg';
import { useEffect } from 'react';
import useSellerSettingStore, { SellerSetting, Profile, CompanyDetails, W9Form, YourProducts, StockingLocation, FundingSettings } from './SellerSettingStore';
import axios from 'axios';
import { useGlobalStore, getSocketConnection , useAuthStore } from '@bryzos/giss-ui-library';
import clsx from 'clsx';

const SellerInfoSetting = (props: any) => {
    const router = useIonRouter()
    const { setSellerSettingInfo, setProfileInfo,  setCompanyInfo, setW9FormInfo, setYourProductsInfo, setStockingLocationInfo, setFundingSettingsInfo, resetSellerSetting } = useSellerSettingStore();
    const setShowLoader = useGlobalStore(state => state.setShowLoader);
    const socket = getSocketConnection();
    const {setTriggerLogout, initiateLogout} = useAuthStore()

    useIonViewWillEnter(() => {
        setShowLoader(true);
        axios.get(import.meta.env.VITE_API_SERVICE + '/user/sellingPreference')
            .then(response => {
                setSellerSettingData(response.data.data);
                setShowLoader(false);
            })
            .catch(error => {
                console.error(error);
                setShowLoader(false);
                // setApiFailureDialog(true)
            }
        );
    }, [])


    const setSellerSettingData = (sellerSetting: SellerSetting) => {
        if (sellerSetting) {
            const profile: Profile = {
                email_id: sellerSetting.email_id,
                first_name: sellerSetting.first_name,
                last_name: sellerSetting.last_name,
                phone: sellerSetting.phone,
            };
            setProfileInfo(profile);

            const companyDetails: CompanyDetails = { 
                company_name: sellerSetting.company_name, 
                client_company: sellerSetting.client_company, 
                company_address_line1: sellerSetting.company_address_line1, 
                company_address_city: sellerSetting.company_address_city,
                company_address_state_id: sellerSetting.company_address_state_id,
                company_address_zip: sellerSetting.company_address_zip
            }
            setCompanyInfo(companyDetails);

            const w9Form: W9Form = {
                w9_form_s3_url: sellerSetting.w9_form_s3_url,
            };
            setW9FormInfo(w9Form);

            const yourProducts: YourProducts = {
                products_s3_url: sellerSetting.products_s3_url,
            };
            setYourProductsInfo(yourProducts);

            const stockingLocation: StockingLocation = {
                stocking_location: sellerSetting.stocking_location,
                send_invoices_to: sellerSetting.send_invoices_to, 
                shipping_docs_to: sellerSetting.shipping_docs_to
            };
            setStockingLocationInfo(stockingLocation);
            if(sellerSetting.funding_settings){
                const fundingSettings: FundingSettings = {
                    id: sellerSetting.funding_settings.id,
                    user_id: sellerSetting.funding_settings.user_id,
                    payment_info_id: sellerSetting.funding_settings.payment_info_id,
                    bank_name: sellerSetting.funding_settings.bank_name,
                    routing_number: sellerSetting.funding_settings.routing_number,
                    account_number: sellerSetting.funding_settings.account_number,
                    reference_document_id: sellerSetting.funding_settings.reference_document_id,
                    pgpm_mapping_id: sellerSetting.funding_settings.pgpm_mapping_id
                };
                setFundingSettingsInfo(fundingSettings);
            }
            setSellerSettingInfo(sellerSetting);
        }
    }
    
    return (
        <IonPage>
            <IonContent>
                <div className={styles.settingsPageMain}>
                    <h2 className={styles.heading}>Setting </h2>
                    <div className={styles.containDiv}>
                        <div className={styles.settingsContent}>
                            <div className={styles.settingsPageBtn}>
                                <button onClick={() => router.push('/seller-profile',{animate:true,direction:'forward'})}><ProfileIcon />Profile<SettingsRightArrowIcon className={styles.rightIcon} /></button>
                            </div>
                            <div className={styles.settingsPageBtn}>
                              <button onClick={()=> router.push('/change-password',{animate:true,direction:'forward'})}><ChangePasswordIcon/>Change Password<SettingsRightArrowIcon className={styles.rightIcon}/></button>
                            </div>
                            <div className={styles.settingsPageBtn}>
                                <button onClick={() => router.push('/seller-company-information',{animate:true,direction:'forward'})}><CompanyInfoIcon />Company Information<SettingsRightArrowIcon className={styles.rightIcon} /></button>
                            </div>
                            <div className={styles.settingsPageBtn}>
                                <button onClick={() => router.push('/stocking-location',{animate:true,direction:'forward'})}><DeliverToIcon />Stocking Location<SettingsRightArrowIcon className={styles.rightIcon} /></button>
                            </div>
                            <div className={styles.settingsPageBtn}>
                                <button onClick={() => router.push('/funding-setting',{animate:true,direction:'forward'})}><FundingSettingsIcon />Funding Settings<SettingsRightArrowIcon className={styles.rightIcon} /></button>
                            </div>
                            <div className={styles.settingsPageBtn}>
                                <button onClick={() => router.push('/seller-document-info',{animate:true,direction:'forward'})}><DocumentsLibraryIcon />Documents Library<SettingsRightArrowIcon className={styles.rightIcon} /></button>
                            </div>
                            <div className={styles.settingsPageBtn}>
                                <button onClick={() => router.push('/your-products',{animate:true,direction:'forward'})}><YourProductsIcon />Your Products<SettingsRightArrowIcon className={styles.rightIcon} /></button>
                            </div>
                            <div className={styles.settingsPageBtn}>
                                <button onClick={() => router.push('/seller-my-reports',{animate:true,direction:'forward'})}><MyReportsIcon />My Reports<SettingsRightArrowIcon className={styles.rightIcon} /></button>
                            </div>
                            <div className={styles.settingsPageBtn}>
                                <button onClick={() => initiateLogout()}><LogoutIcon />Logout<SettingsRightArrowIcon className={styles.rightIcon} /></button>
                            </div>
                            <div className={styles.appVersion}>
                                <span className={clsx(socket?.connected && styles.active)}>v{import.meta.env.VITE_REACT_APP_VERSION}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </IonContent>
        </IonPage>
    )

}
export default SellerInfoSetting;