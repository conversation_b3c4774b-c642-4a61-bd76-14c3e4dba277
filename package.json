{"name": "bryzos-extended-pricing-widget", "version": "1.0.161-q4", "private": true, "scripts": {"test": "vite test", "start:web": "vite --mode qa", "build:web": "vite build --mode development", "build:web:staging": "vite build --mode staging", "build:web:qa": "vite build --mode qa", "build:web:demo": "vite build --mode demo", "build:web:production": "vite build --mode production", "config-generate-electron": "node ./scripts/configMoveElectron.mjs", "ionic:serve": "cross-env NODE_ENV=qa npm run config-generate-electron && npm run start:web", "ionic:build": "cross-env NODE_ENV=qa npm run config-generate-electron && npm run build:web:qa", "ionic:build:staging": "cross-env NODE_ENV=staging npm run config-generate-electron && npm run build:web:staging", "ionic:build:qa": "cross-env NODE_ENV=qa npm run config-generate-electron && npm run build:web:qa", "ionic:build:demo": "cross-env NODE_ENV=demo npm run config-generate-electron && npm run build:web:demo", "ionic:build:prod": "cross-env NODE_ENV=production npm run config-generate-electron && npm run build:web:production"}, "dependencies": {"@bryzos/giss-ui-library": "1.6.5", "@capacitor/android": "^6.0.0", "@capacitor/app": "^6.0.0", "@capacitor/core": "^6.0.0", "@capacitor/device": "^6.0.0", "@capacitor/filesystem": "^6.0.0", "@capacitor/haptics": "^6.0.0", "@capacitor/ios": "^6.0.0", "@capacitor/keyboard": "^6.0.0", "@capacitor/local-notifications": "^6.0.0", "@capacitor/network": "^6.0.0", "@capacitor/status-bar": "^6.0.0", "@capacitor/toast": "^6.0.0", "@capgo/capacitor-updater": "^6.0.18", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@hookform/resolvers": "^3.0.0", "@ionic/react": "^8.2.0", "@ionic/react-router": "^8.2.0", "@mantine/hooks": "^7.1.2", "@mui/material": "^5.11.10", "@mui/x-date-pickers": "^6.1.0", "@tanstack/react-query": "^4.27.0", "@tanstack/react-query-devtools": "^4.27.0", "@types/mime-types": "^2.1.4", "amazon-cognito-identity-js": "^6.3.6", "aws-amplify": "^5.0.20", "axios": "^1.3.4", "capacitor-plugin-safe-area": "3.0.1", "dayjs": "^1.11.7", "file-saver": "^2.0.5", "immer": "^10.0.1", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "pusher-js": "^8.2.0", "raygun4js": "^2.25.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.43.6", "react-input-emoji": "^5.9.0", "react-router-dom": "^5.3.4", "react-share": "^5.1.0", "socket.io-client": "^4.6.1", "truevault": "^1.3.1", "use-immer": "^0.9.0", "uuid": "^9.0.0", "web-vitals": "^3.3.0", "yup": "^1.0.2", "zustand": "^4.3.6"}, "devDependencies": {"@capacitor/cli": "^6.0.0", "@dotenvx/dotenvx": "^0.44.1", "@testing-library/jest-dom": "^6.1.2", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/file-saver": "^2.0.5", "@types/mime-types": "^2.1.4", "@types/node": "^20.4.8", "@types/raygun4js": "^2.13.8", "@types/react": "^18.2.18", "@types/react-dom": "^18.2.7", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^9.0.2", "@vitejs/plugin-react": "^4.0.4", "clsx": "^2.0.0", "concurrently": "^8.0.1", "cross-env": "^7.0.3", "postcss": "^8.4.21", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.64.2", "ts-loader": "^9.4.4", "ts-node": "^10.9.1", "typescript": "^5.1.6", "vite": "^4.2.1", "vite-plugin-progress": "^0.0.7", "vite-plugin-svgr": "^3.2.0", "vite-tsconfig-paths": "^4.0.7"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}