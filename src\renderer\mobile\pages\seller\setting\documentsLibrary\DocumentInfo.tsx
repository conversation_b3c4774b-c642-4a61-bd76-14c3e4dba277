//@ts-nocheck
import { IonContent, IonPage, useIonLoading, useIonRouter, useIonViewWillEnter, useIonViewWillLeave } from "@ionic/react";
import { useEffect, useRef, useState } from "react";
import styles from './DocumentInfo.module.scss';
import { yupResolver } from '@hookform/resolvers/yup';
import { documentInfoSchema } from '../SettingSchema';
import { useForm } from 'react-hook-form';
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import { ReactComponent as UploadIcon } from '../../../../../assets/mobile-images/icon_Upload.svg';
import { useGlobalStore } from "@bryzos/giss-ui-library";
import { fileType, mobileDiaglogConst, prefixUrl, sellerSettingConst } from "../../../../library/common";
import useSellerSettingStore, {W9Form as W9FormModal} from "../SellerSettingStore";

import useDialogStore from '../../../../components/Dialog/DialogStore';
import { uploadBuyerDocumentInfoToS3, saveUserSetting, downloadFiles } from "../../../../library/helper";

function SellerDocumentInfo() {
  const { 
      register, 
      handleSubmit, 
      getValues, 
      clearErrors, 
      setValue, 
      reset, 
      formState: { errors, isDirty } } = useForm({
      resolver: yupResolver(documentInfoSchema)
  });
  const router = useIonRouter();
  const [present, dismiss] = useIonLoading();
  const { w9FormInfo, setW9FormInfo } = useSellerSettingStore();
  const [isSaveDisable, setIsSaveDisable] = useState(true);
  const {userData, setShowLoader} = useGlobalStore();
  const inputFileRef = useRef();
  const { showCommonDialog, resetDialogStore } = useDialogStore();

  useEffect(() => {
    if (isDirty)
      setIsSaveDisable(false);

    // const handleBackButton = (ev: BackButtonEvent) => {
    //   ev.detail.register(10, async () => {
    //     backToSetting();
    //   });
    // };

    // document.addEventListener('ionBackButton', handleBackButton);
    // return () => {
    //   document.removeEventListener('ionBackButton', handleBackButton);
    // };
  }, [isDirty])

  const irsW9FormEditHandler = () => {
      inputFileRef.current.click();
  }
  useIonViewWillEnter(() => {
      if (w9FormInfo && w9FormInfo.w9_form_s3_url) {
          setValue("w9_form_s3_url", w9FormInfo.w9_form_s3_url)
          clearErrors();
      }
  }, [w9FormInfo])

  useIonViewWillLeave(() => {
      setIsSaveDisable(true);
      reset();
      resetDialogStore();
  }, [])

    

  const uploadIRSW9File = async (event: any) => {
    const file = event.target.files[0];
    setShowLoader(true);
    try{
      if (event.target.files.length !== 0) {
        const certUrl = await uploadBuyerDocumentInfoToS3(file,userData,prefixUrl.irsW9Prefix,import.meta.env.VITE_S3_UPLOAD_SETTINGS_IRS_W9_BUCKET_NAME);
        setValue("w9_form_s3_url", certUrl)
        setIsSaveDisable(false);
        setShowLoader(false);
      }
    }catch(error) {
      console.error(error);
      setIsSaveDisable(true);
      setShowLoader(false);
    }
  }

  const routeBackToSetting = ()=>{
    router.push('/seller-setting',{animate:true,direction:'forward'});
    resetDialogStore();
  }

  const backToSetting = () => {
      if(isDirty)
      showCommonDialog(mobileDiaglogConst.unsavedChangesMsg,[{name: mobileDiaglogConst.stay, action: resetDialogStore},{name: mobileDiaglogConst.goBackToSetting, action: routeBackToSetting}]);
      else
      routeBackToSetting();
  }
  const onSubmit = async(data) => {
    setShowLoader(true);
    const detail : W9FormModal = data;  
    try{
        await saveUserSetting(sellerSettingConst.apiRoutesForSave.documentInfo, detail, userData);
        setW9FormInfo(detail);
        router.push('/seller-setting',{animate:true,direction:'forward'});
        setShowLoader(false);
    }catch(err){
        console.error(err)
        setShowLoader(false);
    }
}

  const viewW9Form = () => {
    setShowLoader(true);
    const url = getValues("w9_form_s3_url");
    const fileName = url.substring(url.lastIndexOf('/') + 1, url.length);
    downloadFiles(url, fileName, fileType.unknown).then(() => {
      setShowLoader(false);
    });
  }

  return (
    <IonPage>
      <IonContent>
        <>
          <div className={styles.profile}>
            <h2 className={styles.heading}>
              <BackBtnArrowIcon onClick={backToSetting}/>
              <span>Documents Library</span>
            </h2>
            <div className={styles.profileComponent}>
              <label> IRS W-9 Form</label>
              {getValues("w9_form_s3_url") ? (
                <div className={styles.uploadFileStatus}>
                  <span className={styles.uploadedFileName}>
                  <button onClick={viewW9Form} className={styles.viewBtn}>View</button><span className={styles.orText}>or</span><button className={styles.viewBtn} onClick={irsW9FormEditHandler}>Edit </button>
                  </span>
                </div>
              ) : (
                  <label className={styles.uploadFileBtn}>
                  <UploadIcon />
                  <button
                    onClick={irsW9FormEditHandler}
                    className={styles.uploadText}
                  >
                    Upload W-9
                  </button>
                  
                </label>
              )}
              <input
                    {...register('w9_form_s3_url')}
                    type='file'
                    onChange={(e) => {
                      uploadIRSW9File(e);
                      register('w9_form_s3_url').onChange(e);
                    }}
                    ref={inputFileRef}
                  />
            </div>
            <div className={styles.btnSection}>
              <button className={styles.saveBtn} onClick={handleSubmit(onSubmit)}  disabled={isSaveDisable}>
                Save
              </button>
            </div>
          </div>
        </>
      </IonContent>
    </IonPage>
  );
}
export default SellerDocumentInfo;
