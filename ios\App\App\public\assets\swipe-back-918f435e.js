import{bM as h,Z as b,bN as f}from"./vendor-74ddad81.js";/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const k=(n,m,g,p,X)=>{const c=n.ownerDocument.defaultView;let s=h(n);const w=t=>{const{startX:e}=t;return s?e>=c.innerWidth-50:e<=50},a=t=>s?-t.deltaX:t.deltaX,v=t=>s?-t.velocityX:t.velocityX;return b({el:n,gestureName:"goback-swipe",gesturePriority:101,threshold:10,canStart:t=>(s=h(n),w(t)&&m()),onStart:g,onMove:t=>{const e=a(t)/c.innerWidth;p(e)},onEnd:t=>{const o=a(t),e=c.innerWidth,r=o/e,i=v(t),y=e/2,l=i>=0&&(i>.2||o>y),u=(l?1-r:r)*e;let d=0;if(u>5){const M=u/Math.abs(i);d=Math.min(M,540)}X(l,r<=0?.01:f(0,r,.9999),d)}})};export{k as createSwipeBackGesture};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
