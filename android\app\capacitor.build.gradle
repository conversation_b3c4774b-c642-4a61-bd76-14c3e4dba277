// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-app')
    implementation project(':capacitor-device')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-local-notifications')
    implementation project(':capacitor-network')
    implementation project(':capacitor-status-bar')
    implementation project(':capacitor-toast')
    implementation project(':capgo-capacitor-updater')
    implementation project(':capacitor-plugin-safe-area')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
