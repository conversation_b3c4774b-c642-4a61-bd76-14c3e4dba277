
  .createPoContent {
    padding: 12px 16px 20px 16px;
    border-radius: 0px 0px 10px 10px;
    // -webkit-backdrop-filter: blur(60px);
    // backdrop-filter: blur(60px);
    // background-color: rgba(0, 0, 0, 0.75);
    margin: 0px auto;
    max-width: 600px;
    width: 100%;
    filter: blur(0); //backdrop-filter not adjusting to electron resizing

    .formInnerContent {

      .formInputGroup {
        display: flex;
        margin-bottom: 8px;

        .lblInput {
          font-family: Noto Sans;
          font-size: 14px;
          font-weight: normal;
          line-height: 1.6;
          text-align: left;
          color: #fff;
          width: 152px;
          height: 34px;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          padding: 6px 4px 6px 10px;
          border: solid 0.5px #000;
          background-color: rgba(0, 0, 0, 0.25);
          border-radius: 4px 0px 0px 4px;
          border-right: 0px;

          .questionIcon {
            display: flex;
            align-items: center;
            margin-left: auto;
            transition: all 0.1s;

            .questionIcon2 {
              display: none;
            }

            .questionIcon3 {
              display: none;
            }

            .questionIcon4 {
              display: none;
            }

            &:hover {
              .questionIcon1 {
                display: none;
              }

              .questionIcon2 {
                display: block;
              }
            }
          }
        }

        .inputSection {
          height: 34px;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: flex-start;
          padding: 6px 8px 6px 10px;
          border: solid 0.5px #000;
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 0px 4px 4px 0px;
          flex: 1;

          &.paddingLR0{
            padding-left: 0px;
            padding-right: 0px;
          }

          &.cityInput {
            flex: 0 0 96px;
          }

           &.stateInput{
              flex: 0 85px;
            }

          &.zipCodeInput {
            flex: 0 0 72px;
            padding: 6px 3px 6px 6px;

          }

          &.bdrRadius0 {
            border-radius: 0px;
          }

          &.bdrRight0 {
            border-right: 0px;
          }

          input {
            width: 100%;
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.6;
            text-align: left;
            color: #fff;
            border: 0px;
            background-color: transparent;
            padding: 0px;

            &:focus {
              outline: none;
              box-shadow: none;
            }

            &::placeholder {
              color: rgba(255, 255, 255, 0.5);
              font-weight: normal;
            }
          }
        }
        .errorInput {
          border: 1px solid red;

          &:focus-within {
            border: solid 1px red;
            outline: none;
            box-shadow: none;
          }
        }
        &.FormInputGroupError {
          .lblInput {
            border: solid 0.5px #f00;
            background-color: #f00;
            cursor: pointer;
            white-space: nowrap;
          }

          .borderOfError {
            border: solid 0.5px #f00;
          }
        }

        select {
          background: transparent;
          border: 0;
          color: rgba(255, 255, 255, 0.5);
          height: 100%;
          width: 100%;
          font-family: Noto Sans;
          font-size: 14px;
          font-weight: normal;
          line-height: 1.6;
          text-align: left;
          color: #fff;
        }

        &:focus-within {
          .lblInput {
            background-color: #70ff00;
            border: solid 0.5px #70ff00;
            color: #000;

            .questionIcon {
              display: flex;
              align-items: center;
              margin-left: auto;
              transition: all 0.1s;

              .questionIcon3 {
                display: none;
              }

              .questionIcon1 {
                display: none;
              }

              .questionIcon2 {
                display: none;
              }

              .questionIcon4 {
                display: block;
              }

              &:hover {
                .questionIcon4 {
                  display: none;
                }

                .questionIcon1 {
                  display: none;
                }

                .questionIcon2 {
                  display: none;
                }

                .questionIcon3 {
                  display: block;
                }
              }
            }
          }

          .inputSection {
            height: 34px;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: flex-start;
            padding: 6px 8px 6px 10px;
            border: solid 0.5px #70ff00;
            background-color: rgba(0, 0, 0, 0.2);
            //border-radius: 0px 4px 4px 0px;
            flex: 1;

            &.paddingLR0{
              padding-left: 0px;
              padding-right: 0px;
            }

            &.cityInput {
              flex: 0 0 96px;
            }

            &.stateInput{
              flex: 0 85px;
            }

            &.zipCodeInput {
              flex: 0 0 72px;
              padding: 6px 3px 6px 6px;

            }

            &:last-child {
              border-left: 0px;
            }

            input {
              width: 100%;
              font-family: Noto Sans;
              font-size: 14px;
              font-weight: normal;
              line-height: 1.6;
              text-align: left;
              // color: #70ff00;
              border: 0px;
              background-color: transparent;
              padding: 0px;

              &:focus {
                outline: none;
                box-shadow: none;
              }

              &::placeholder {
                // color: #70ff00;
                font-weight: normal;
              }
            }
          }
        }
      }
      .formInputGroupFocus {
        .lblInput {
          background-color: #70ff00;
          border: solid 0.5px #70ff00;
          color: #000;

          .questionIcon {
            display: flex;
            align-items: center;
            margin-left: auto;
            transition: all 0.1s;

            .questionIcon3 {
              display: none;
            }

            .questionIcon1 {
              display: none;
            }

            .questionIcon2 {
              display: none;
            }

            .questionIcon4 {
              display: block;
            }

            &:hover {
              .questionIcon4 {
                display: none;
              }

              .questionIcon1 {
                display: none;
              }

              .questionIcon2 {
                display: none;
              }

              .questionIcon3 {
                display: block;
              }
            }
          }
        }

        .inputSection {
          height: 34px;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: flex-start;
          padding: 6px 8px 6px 10px;
          border: solid 0.5px #70ff00;
          background-color: rgba(0, 0, 0, 0.2);
          //border-radius: 0px 4px 4px 0px;
          flex: 1;

          &.paddingLR0{
            padding-left: 0px;
            padding-right: 0px;
          }

          &.cityInput {
            flex: 0 0 96px;
          }

          &.stateInput{
            flex: 0 85px;
          }

          &.zipCodeInput {
            flex: 0 0 72px;
            padding: 6px 3px 6px 6px;

          }

          &:last-child {
            border-left: 0px;
          }

          input {
            width: 100%;
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.6;
            text-align: left;
            // color: #70ff00;
            border: 0px;
            background-color: transparent;
            padding: 0px;

            &:focus {
              outline: none;
              box-shadow: none;
            }

            &::placeholder {
              // color: #70ff00;
              font-weight: normal;
            }
          }
        }
      }
    }

    .addPoLineTable {
      width: 100%;
      border-top: solid 1px rgba(255, 255, 255, 0.6);
      padding-top: 6px;
      overflow-y: auto;
      height: 100%;
      max-height: 255px;
      // padding-right: 5px;

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #9da2b2;
        border-radius: 50px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      tbody {
        // display:block;
        // max-height:300px;
        // overflow-y:scroll;
      }

      thead,
      tbody tr {
        display: table;
        width: 100%;
      }

      table {
        width: 100%;
        border-spacing: 1px;
        border-collapse: collapse;

        tr {

          th {
            font-family: Noto Sans;
            font-size: 16px;
            font-weight: 600;
            line-height: 1.6;
            text-align: left;
            color: #fff;
            padding: 4px 0px 6px 0px;
            border-top:solid 1px rgba(255, 255, 255, 0.6);
            border-bottom: solid 1px rgba(255, 255, 255, 0.6);

            &:nth-child(1) {
              width: 100%;
            }

            &:nth-child(2) {
              width: 100%;
              min-width: 240px
            }

            &:nth-child(3) {
              width: 100%;
              text-align: center;
              min-width: 88px
            }

            &:nth-child(4) {
              width: 100%;
              text-align: center;
              min-width: 88px;
              span{
                display: flex;
                justify-content: right;
                align-items: center;
                gap: 5px;
              }
              label{
                height: 20px;
              }
            }

            &:nth-child(5) {
              width: 100%;
              min-width: 104px;
              text-align: right;
            }

            span {
              border-bottom: solid 1px rgba(255, 255, 255, 0.6);
              display: block;
              padding: 0px 3px 3px 3px;
            }
          }
        }

        tr {
          border-top: 1px solid #fff;

          &:first-child {
            border-top: 0px;
          }

          td {
            font-family: Noto Sans;
            font-size: 18px;
            font-weight: bold;
            line-height: 1.6;
            text-align: left;
            vertical-align: top;
            color: #fff;
            padding: 16px 4px 4px 4px;

            &:nth-child(2) {
              font-family: Noto Sans;
              font-size: 14px;
              line-height: 1.4;
              text-align: left;
              color: #fff;
              font-weight: normal;
              line-height: normal;
            }

            .prodId {
              display: inline-block;
            }

            .lineNone {
              line-height: 0;
              position: relative;
            }

            .poDescription {
              width: 100%;
              height: 120px;
              padding: 8px;
              border-radius: 4px 4px 0px 0px;
              border: solid 0.5px #000;
              background-color: rgba(255, 255, 255, 0.2);
              color: #fff;
              resize: none;
              font-family: Noto Sans;
              font-size: 14px;
              font-weight: normal;
              line-height: 1.4;
              text-align: left;


              &::placeholder {
                color: #bbb;
              }

              &:focus-within {
                border: solid 1px #70ff00;
                outline: none;
                box-shadow: none;
              }
            }

            .poQty,
            .poPerUm {
              width: 80px;

              input {
                font-family: Noto Sans;
                font-size: 18px;
                font-weight: normal;
                line-height: 1.6;
                color: #fff;
                text-align: right;
                padding: 2px 7px;
                border-radius: 4px;
                border: solid 0.5px #000;
                background-color: rgba(255, 255, 255, 0.2);
                height: 29px;
                width: 100%;

                &:focus-within {
                  border: solid 1px #70ff00;
                  outline: none;
                  box-shadow: none;
                }
              }

              .errorInput {
                border: 1px solid red;

                &:focus-within {
                  border: solid 1px red;
                  outline: none;
                  box-shadow: none;
                }
              }

              .selectUom {
                .uomDrodown {
                  width: 90px;
                  border: 0;
                  font-family: Noto Sans;
                  font-size: 16px;
                  line-height: 1.6;
                  text-align: left;
                  color: #fff;
                  font-weight: normal;

                  .MuiSelect-select {
                    padding: 20px 6px 6px 6px;
                  }

                  svg {
                    transform: unset;
                    color: #fff;
                    right: 10px;
                  }

                  fieldset {
                    border: 0;
                  }
                }
              }

              .selectUom1 {
                .uomDrodown {
                  width: 110px;
                  border: 0;
                  font-family: Noto Sans;
                  font-size: 16px;
                  line-height: 1.6;
                  text-align: left;
                  color: #fff;
                  font-weight: normal;

                  .MuiSelect-select {
                    padding: 20px 6px 6px 6px;
                  }

                  svg {
                    transform: unset;
                    color: #fff;
                    right: 10px;
                  }

                  fieldset {
                    border: 0;
                  }
                }
              }
            }
            .poQty,
            .poPerUm {
              color: rgba(255, 255, 255, 0.6);
              font-weight: normal;
            }

            &:nth-child(5) {
              color: rgba(255, 255, 255, 0.6);
              font-weight: normal;
            }

          }
        }

        tbody {
          tr {
            
            &:nth-child(odd){
              height: 146px;
            }
            &:nth-child(even){
              border: none;
              td{
                padding-top: 0px;
              }
            }
            td {
              &:nth-child(1) {
                min-width: 40px;
              }

              &:nth-child(2) {
                width: 100%;
                min-width: 240px;
              }

              &:nth-child(3) {
                width: 100%;
                min-width: 88px;
                text-align: right;
              }

              &:nth-child(4) {
                width: 100%;
                min-width: 88px;
                text-align: right;
              }

              &:nth-child(5) {
                width: 100%;
                min-width: 104px;
                text-align: right;
                max-width: 104px;
                word-wrap: break-word;
              }
            }
          }
        }
      }
    }
    .totalWeight{
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      padding-left: 40px;
      padding-bottom: 3px;
      border-bottom: 0.2px solid #fff;
      margin-top: -12px;
    }
    .lineWeight{
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.4;
      text-align: left;
      color: rgba(255, 255, 255, 0.75);
    }
    .addMoreLine {
      position: relative;
      margin-top: 16px;
      display: flex;
      align-items: center;

      button {
        background-color: transparent;
        border: 0px;
        transition: all 0.1s;
        position: relative;
        top:1.5px;
        .addLineHover{
          display: none;
        }
        &:hover{
          .addLine{
            display:none;
          }
          .addLineHover{
            display: inline-block;
          }
        }

        &:focus-visible {
          border: 1px solid #70ff00;
        }
      }

      span {
        width: 100%;
        display: flex;
        border-top: 0.2px solid #fff;
        position: absolute;
        z-index: -1;
      }
    }

    .removeLine {
      position: relative;
      margin-top: 16px;

      button {
        z-index: 1;
        position: absolute;
        top: -13px;
        left: 0;
        background-color: transparent;
        border: 0px;
        transition: all 0.1s;
        .removeLineHoverIcon{
          display: none;
        }
        &:hover{
          .removeLineIcon{
            display:none;
          }
          .removeLineHoverIcon{
            display: inline-block;
          }
        }
        &:focus-visible {
          border: 1px solid #70ff00;
        }
      }

      // span {
      //   width: 100%;
      //   display: flex;
      //   border-top: 0.2px solid #fff;
      //   position: absolute;
      //   bottom: 13px;
      //   z-index: -1;
      // }
    }

    .totalAmt {
      margin-top: 8px;
      margin-bottom: 12px;

      .saleTax {
        font-family: Noto Sans;
        font-size: 18px;
        line-height: 1.6;
        text-align: right;
        color: #fff;

        .questionIcon {
          vertical-align: middle;
          margin-left: 6px;
          transition: all 0.1s;

          .questionIcon2 {
            display: none;
          }

          &:hover {
            .questionIcon1 {
              display: none;
            }

            .questionIcon2 {
              display: inline-block;
            }
          }
        }
      }

      .totalPurchase {
        font-family: Noto Sans;
        font-size: 24px;
        font-weight: 600;
        line-height: 1.6;
        text-align: right;
        color: #fff;
      }

      table {
        width: 100%;
        display: flex;
        justify-content: end;

        tr {
          td {
            text-align: right;
            padding: 0px 6px;

            .prodId {
              display: inline-block;
            }
          }
        }
      }
    }

    .btnOfCreatePo {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: center;
      gap: 20px;
      margin-top: 12px;

      .selectedpayment {
        font-family: Noto Sans;
        font-size: 24px;
        font-weight: 600;
        line-height: 1.6;
        color: #fff;
        width: 100%;
        height: 54px;
        border-radius: 4px;
        border: solid 1px rgba(255, 255, 255, 0.1);
        background-color: rgba(255, 255, 255, 0.1);
        cursor: pointer;

        .selectPaymentMethod {
          padding: 0px 20px;
          font-family: Noto Sans;
          font-size: 18px;
          line-height: 1.6;
          text-align: left;
          color: #fff;
          height: 50px;
          width: 100%;

          .MuiSelect-select {
            display: flex;
            justify-content: center;
          }

          svg {
            right: 24px;
            top: calc(50% - 0.6em);
            transform: unset;
            color: #fff;
          }

          fieldset {
            border: 0;
          }
        }
      }

      button {
        width: 100%;
        height: 54px;
        padding: 8px 0;
        border-radius: 4px;
        border: solid 1px rgba(255, 255, 255, 0.1);
        background-color: rgba(255, 255, 255, 0.1);
        cursor: pointer;
        transition: all 0.1s;

        &:hover {
          background-color: #70ff00;

          &.placeOrder {
            color: #000;
          }
        }

        &.placeOrder {
          font-family: Noto Sans;
          font-size: 24px;
          font-weight: normal;
          line-height: 1.6;
          color: #fff;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          border: solid 1px rgba(255, 255, 255, 0.1);
          background-color: rgba(255, 255, 255, 0.1);

          &:hover {
            background-color: rgba(255, 255, 255, 0.1);

            &.placeOrder {
              color: #fff;
            }
          }
        }
      }
    }

    .textOfCreatePo {
      width: 100%;
      font-size: 14px;
      font-family: Noto Sans;
      line-height: 1.6;
      font-weight: 300;
      text-align: center;
      color: #fff;
      margin-top: 12px;
    }

    .backBtnMain {
      width: 100%;
      text-align: center;
    }

    .cancelPOGoBack {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-family: Noto Sans;
      font-size: 18px;
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.5);
      cursor: pointer;
      margin-top: 10px;
      padding: 0px;
      background-color: transparent;
      border: 0px;
      transition: all 0.1s;

      &:hover {
        color: #70ff00;
      }
    }

  }

.selectPaymentMethodPaper.selectPaymentMethodPaper {
  padding: 3px 4px 8px 12px;
  backdrop-filter: blur(24px);
  background-color: #ffffff4c;
  box-shadow: 0px 8px 30px #000000cc;
  margin-top: 9px;
  overflow: hidden;
  background: url(../../assets/images/DropDownBG.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;

  ul {
    overflow: auto;
    max-height: 260px;
    padding-right: 8px;

    &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 50px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    li {
      font-family: Noto Sans;
      font-size: 16px;
      font-weight: normal;
      line-height: 1.57;
      text-align: left;
      color: #fff;
      border-radius: 2px;
      margin-bottom: 3px;

      &:hover {
        background-color: #fff;
        color: #000;
      }

      &[aria-selected="true"] {
        background-color: #fff;
        color: #000;
        border-radius: 2px;
      }

    }

    .Mui-selected.Mui-selected {
      background-color: transparent;
    }
  }
}

.selectUomPaper.selectUomPaper {
  padding: 3px 4px 3px 6px;
  backdrop-filter: blur(24px);
  background-color: #ffffff4c;
  box-shadow: 0px 8px 30px #000000cc;
  margin-top: 4x;
  overflow: hidden;
  background: url(../../assets/images/DropDownBG.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;

  ul {
    overflow: auto;
    max-height: 260px;
    padding-right: 2px;

    &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 50px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    li {
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      border-radius: 2px;

      &[aria-selected="true"] {
        background-color: #fff;
        color: #000;
      }

      &:hover {
        background-color: #fff;
        color: #000;
      }

    }

    .Mui-selected.Mui-selected {
      background-color: transparent;
    }
  }
}


.autocompleteDescPanel {
  border-radius: 4px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  -webkit-backdrop-filter: blur(24px);
  backdrop-filter: blur(24px);
  background-color: #ffffff4c;
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  padding-right: 4px;
  // padding-top: 4px;
  background: url(../../assets/images/DropDownBG.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
  border-radius: 0px 0px 4px 4px;
}

.listAutoComletePanel.listAutoComletePanel {
  width: 100%;
  max-height: 316px;
  padding: 6px 4px 6px 10px;
  margin-top: 4px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 20px;

  }

  span {
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    box-shadow: none;
    padding: 4px 8px;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 4px;

    &:hover {
      border-radius: 2px;
      background-color: #fff;
      color: #000;
    }

    &[aria-selected="true"] {
      // background-color: #EBF2FF;
      // color: #397aff;
      background-color: #fff;
      color: #000;
    }
  }
}

.Dropdownpaper.Dropdownpaper {
  padding: 3px 4px 8px 8px;
  -webkit-backdrop-filter: blur(30px);
  backdrop-filter: blur(30px);
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
  background-color: rgba(255, 255, 255, 0.3);
  margin-top: 7px;
  overflow: hidden;
  width: 66px;
  border-radius: 0px 0px 4px 4px;
  background: url(../../assets/images/DropDownBG.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;

  &.DropdownDeliveryDate {
    margin-top: 8px;
  }

  ul {
    overflow: auto;
    max-height: 230px;
    padding-right: 4px;
    padding-top: 0px;
    padding-bottom: 0px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 50px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    li {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      border-radius: 2px;
      padding: 3px 5px;
      margin-bottom: 2px;

      &:hover {
        background-color: #fff;
        color: #000;
      }

      &[aria-selected="true"] {
        background-color: #fff;
        color: #000;
        border-radius: 2px;
      }
    }
  }
}


.ErrorDialog {
  .dialogContent {
    max-width: 330px;
    width: 100%;
    height: 230px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 30px 34px 30px 34px;
    object-fit: contain;
    border-radius: 10px;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(0, 0, 0, 0.72);
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: center;
    color: #fff;

    p {
      margin-bottom: 20px;
    }


    .submitBtn {
      height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 10px 24px;
      border-radius: 4px;
      border: solid 0.5px #fff;
      background-color: transparent;
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: center;
      color: #fff;
      margin-top: 20px;
      transition: all 0.1s;

      &:hover {
        background-color: #70ff00;
        border: solid 0.5px #70ff00;
        color: #000;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          border: solid 0.5px #fff;
          background-color: transparent;
          color: #fff;
        }
      }
    }


  }

}

.w100 {
  width: 100%;
}

.partNumberFiled {
  position: relative;

  input {
    width: 100%;
    height: 25px;
    padding: 8px;
    border-radius: 0px 0px 4px 4px;
    border: solid 0.5px #000;
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    resize: none;
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;

    &::placeholder {
      color: #bbb;
    }

    &:focus-within {
      border: solid 1px #70ff00;
      outline: none;
      box-shadow: none;
    }
  }

  span {
    position: absolute;
    right: 10px;
    top: 4px;
  }

}

.headerNoteCreatePO {
  height: 72px;
  padding: 8px;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.15);
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 10px;
  position: relative;

  .headerNoteText {
    font-family: Noto Sans;
    font-size: 12px;
    line-height: 1.4;
    text-align: center;
    color: #fff;
  }

  .marginTop8 {
    margin-top: 8px;
  }

  .leftIcon {
    position: absolute;
    left: 8px;
    top: 20px;
  }

  .rightIcon {
    position: absolute;
    right: 8px;
    top: 20px;
  }

  svg {
    width: 32px;
    height: 32px;
  }
}

.domesticMaterialCheckbox {
  margin-top: 5px;
  display: flex;
  padding-bottom: 10px;
  .lblCheckbox {
    display: flex;
    align-items: center;
    white-space: nowrap;
    padding-left: 21px;
    font-size: 12px;
    &:focus-within {
      .checkmark{
        border: solid 1px #70ff00;
      }
    }
    .domesticMaterialTex {


      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.6;
      text-align: left;
      color: rgba(255, 255, 255, 0.75);
    }

    .checkmark {
      width: 15px;
      height: 15px;
      border-radius: 1.7px;
      border: solid 0.7px #3b4665;
      background-color: #ebedf0;
      top: 4px;

      &::after {
        left: 5px;
        top: 2px;
        width: 3px;
        height: 6px;

      }
    }

    input:checked~.checkmark {
      background-color: #70ff00;
      border: solid 0.7px #70ff00;
    }

  }
  .div50{
    width: 280px;
  }
}

.questionIconDesc {
  vertical-align: middle;
  margin-left: 6px;
  transition: all 0.1s;
  position: absolute;
  top: 12px;
  right: 90px;
  cursor: pointer;
  .questionIcon2 {
    display: none;
  }

  &:hover {
    .questionIcon1 {
      display: none;
    }

    .questionIcon2 {
      display: inline-block;
    }
  }
}