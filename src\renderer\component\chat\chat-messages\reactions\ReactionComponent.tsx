import React, { useState, useEffect } from 'react';
import styles from './reaction.module.scss';
import clsx from 'clsx';
import { useGlobalStore } from '@bryzos/giss-ui-library';

// Interface for a single reaction
interface Reaction {
  reaction: string;
  userId: string;
  username: string;
  _id: string;
}

// Interface for the props of the component
interface ReactionComponentProps {
  reactions: Reaction[];
  sdk: any;
  messageId: string;
  autoScrollToBottom:Function
}

// Mapping of reactions to their respective emojis
const reactionEmojis: { [key: string]: string } = {
  wave: '👋',
  thumbs_up: '👍',
  clap: '👏',
  pray: '🙏',
  strong: '💪',
  celebrate: '🎉',
  heart: '❤️'
};

const ReactionComponent: React.FC<ReactionComponentProps> = ({ reactions, sdk, messageId, autoScrollToBottom }) => {
  const [reactionCounts, setReactionCounts] = useState<{ [key: string]: number }>({});
  const { userData }: any = useGlobalStore();
  const userId = userData?.data?.chat_data?.user_id;
  
  
  useEffect(() => {
    // Count each reaction type
    extractCountFromReactions(reactions); 
  }, [reactions]);

  useEffect(() => {
    if (!sdk) {
      return;
    }
    (async () => {
      sdk.on('messageLiked', handleMessageLike);
      return ()=>{
        sdk.off('messageLiked',handleMessageLike);
      }
    })();
  }, [sdk]);

  const handleMessageLike = (message: any) => {
    if (message.messageId === messageId) {
      if(message.action === "liked"){
        autoScrollToBottom();
        const test = reactions.filter((reaction)=>(reaction.userId === message.userId && reaction.reaction === message.reaction && reaction.username === message.username))
        if(test.length==0)
          reactions.push({ reaction: message.reaction, userId: message.userId, username: message.username, _id: '' });
      }else{
        reactions = reactions.filter((reaction)=>(reaction.userId !== message.userId || reaction.reaction !== message.reaction));
      }
      extractCountFromReactions(reactions);
    }
  }

  const extractCountFromReactions = (reactions:Reaction[])=>{
    const counts = reactions.reduce((acc: { [key: string]: number }, item) => {
      const { reaction } = item;
        acc[reaction] = (acc[reaction] || 0) + 1;
      return acc;
    }, {});
    
    setReactionCounts(counts);
  }

  const handleReactionClick = (reaction:string,e: React.MouseEvent<HTMLDivElement, MouseEvent>)=>{
    const myReactions:Reaction[] = reactions.filter(item=>item.userId === userId && item.reaction === reaction);
    if(myReactions.length > 0){
      sdk.likeMessage(messageId, reaction, "unliked");
    }else{
      sdk.likeMessage(messageId, reaction, "liked");
    }
    e.stopPropagation();
  }


  return (
    <div className={styles.reactionContainer} >
      {Object.entries(reactionCounts).map(([reaction, count]) => (
        <div key={reaction} onClick={(e) => handleReactionClick(reaction, e)}  className={clsx(styles.reaction,'reaction')} title={`${count} ${reaction.split('_').join(' ').toUpperCase()}`}>
         <span>{reactionEmojis[reaction]} </span> <span className={styles.countReaction}>{count}</span>
      </div>
      ))}
    </div>
  );
}

export default ReactionComponent;
