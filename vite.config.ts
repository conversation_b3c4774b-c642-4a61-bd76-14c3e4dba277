import { defineConfig, splitVendorChunkPlugin } from 'vite';
import react from '@vitejs/plugin-react';
import viteTsconfigPaths from 'vite-tsconfig-paths';
import svgrPlugin from 'vite-plugin-svgr';
// import checker from 'vite-plugin-checker';
import progress from 'vite-plugin-progress';
// import beep from '@rollup/plugin-beep';
import { visualizer } from 'rollup-plugin-visualizer';


//import postcssPxtorem from 'postcss-pxtorem'; 

import dns from 'dns';
dns.setDefaultResultOrder('verbatim');

const isDev = process.env.NODE_ENV === 'development';
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    // beep(),
    progress(),
    // checker({
    //   typescript: true,
    //   eslint: {
    //     lintCommand: 'eslint "./src/**/*.{ts,tsx}"',
    //   },
    // }),
    splitVendorChunkPlugin(),
    react(),
    viteTsconfigPaths(),
    svgrPlugin(),
    visualizer({
      open: false,
      gzipSize: true,
    }),
  ],
  server: {
    port: 3200,
    open: !true,
  },
  build: {
    outDir: 'build',
    sourcemap: true,
  },
  css: {
    modules: {
      generateScopedName: isDev
        ? '[name]__[local]___[hash:base64:5]'
        : '[local]_[hash:base64:5]',
    },
  },
  resolve: {
    alias: [
      {
        find: './runtimeConfig',
        replacement: './runtimeConfig.browser',
      },
    ],
  },
});
