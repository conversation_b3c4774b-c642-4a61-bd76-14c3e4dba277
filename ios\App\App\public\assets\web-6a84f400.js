import{bJ as P,c1 as x,c2 as E}from"./vendor-74ddad81.js";function m(w){const e=w.split("/").filter(t=>t!=="."),r=[];return e.forEach(t=>{t===".."&&r.length>0&&r[r.length-1]!==".."?r.pop():r.push(t)}),r.join("/")}function R(w,e){w=m(w),e=m(e);const r=w.split("/"),t=e.split("/");return w!==e&&r.every((i,n)=>i===t[n])}class g extends P{constructor(){super(...arguments),this.DB_VERSION=1,this.DB_NAME="Disc",this._writeCmds=["add","put","delete"],this.downloadFile=async e=>{var r,t;const i=x(e,e.webFetchExtra),n=await fetch(e.url,i);let s;if(!e.progress)s=await n.blob();else if(!(n!=null&&n.body))s=new Blob;else{const c=n.body.getReader();let o=0;const d=[],h=n.headers.get("content-type"),y=parseInt(n.headers.get("content-length")||"0",10);for(;;){const{done:f,value:p}=await c.read();if(f)break;d.push(p),o+=(p==null?void 0:p.length)||0;const b={url:e.url,bytes:o,contentLength:y};this.notifyListeners("progress",b)}const u=new Uint8Array(o);let l=0;for(const f of d)typeof f>"u"||(u.set(f,l),l+=f.length);s=new Blob([u.buffer],{type:h||void 0})}return{path:(await this.writeFile({path:e.path,directory:(r=e.directory)!==null&&r!==void 0?r:void 0,recursive:(t=e.recursive)!==null&&t!==void 0?t:!1,data:s})).uri,blob:s}}}async initDb(){if(this._db!==void 0)return this._db;if(!("indexedDB"in window))throw this.unavailable("This browser doesn't support IndexedDB");return new Promise((e,r)=>{const t=indexedDB.open(this.DB_NAME,this.DB_VERSION);t.onupgradeneeded=g.doUpgrade,t.onsuccess=()=>{this._db=t.result,e(t.result)},t.onerror=()=>r(t.error),t.onblocked=()=>{console.warn("db blocked")}})}static doUpgrade(e){const t=e.target.result;switch(e.oldVersion){case 0:case 1:default:t.objectStoreNames.contains("FileStorage")&&t.deleteObjectStore("FileStorage"),t.createObjectStore("FileStorage",{keyPath:"path"}).createIndex("by_folder","folder")}}async dbRequest(e,r){const t=this._writeCmds.indexOf(e)!==-1?"readwrite":"readonly";return this.initDb().then(i=>new Promise((n,s)=>{const o=i.transaction(["FileStorage"],t).objectStore("FileStorage")[e](...r);o.onsuccess=()=>n(o.result),o.onerror=()=>s(o.error)}))}async dbIndexRequest(e,r,t){const i=this._writeCmds.indexOf(r)!==-1?"readwrite":"readonly";return this.initDb().then(n=>new Promise((s,a)=>{const h=n.transaction(["FileStorage"],i).objectStore("FileStorage").index(e)[r](...t);h.onsuccess=()=>s(h.result),h.onerror=()=>a(h.error)}))}getPath(e,r){const t=r!==void 0?r.replace(/^[/]+|[/]+$/g,""):"";let i="";return e!==void 0&&(i+="/"+e),r!==""&&(i+="/"+t),i}async clear(){(await this.initDb()).transaction(["FileStorage"],"readwrite").objectStore("FileStorage").clear()}async readFile(e){const r=this.getPath(e.directory,e.path),t=await this.dbRequest("get",[r]);if(t===void 0)throw Error("File does not exist.");return{data:t.content?t.content:""}}async writeFile(e){const r=this.getPath(e.directory,e.path);let t=e.data;const i=e.encoding,n=e.recursive,s=await this.dbRequest("get",[r]);if(s&&s.type==="directory")throw Error("The supplied path is a directory.");const a=r.substr(0,r.lastIndexOf("/"));if(await this.dbRequest("get",[a])===void 0){const h=a.indexOf("/",1);if(h!==-1){const y=a.substr(h);await this.mkdir({path:y,directory:e.directory,recursive:n})}}if(!i&&!(t instanceof Blob)&&(t=t.indexOf(",")>=0?t.split(",")[1]:t,!this.isBase64String(t)))throw Error("The supplied data is not valid base64 content.");const o=Date.now(),d={path:r,folder:a,type:"file",size:t instanceof Blob?t.size:t.length,ctime:o,mtime:o,content:t};return await this.dbRequest("put",[d]),{uri:d.path}}async appendFile(e){const r=this.getPath(e.directory,e.path);let t=e.data;const i=e.encoding,n=r.substr(0,r.lastIndexOf("/")),s=Date.now();let a=s;const c=await this.dbRequest("get",[r]);if(c&&c.type==="directory")throw Error("The supplied path is a directory.");if(await this.dbRequest("get",[n])===void 0){const h=n.indexOf("/",1);if(h!==-1){const y=n.substr(h);await this.mkdir({path:y,directory:e.directory,recursive:!0})}}if(!i&&!this.isBase64String(t))throw Error("The supplied data is not valid base64 content.");if(c!==void 0){if(c.content instanceof Blob)throw Error("The occupied entry contains a Blob object which cannot be appended to.");c.content!==void 0&&!i?t=btoa(atob(c.content)+atob(t)):t=c.content+t,a=c.ctime}const d={path:r,folder:n,type:"file",size:t.length,ctime:a,mtime:s,content:t};await this.dbRequest("put",[d])}async deleteFile(e){const r=this.getPath(e.directory,e.path);if(await this.dbRequest("get",[r])===void 0)throw Error("File does not exist.");if((await this.dbIndexRequest("by_folder","getAllKeys",[IDBKeyRange.only(r)])).length!==0)throw Error("Folder is not empty.");await this.dbRequest("delete",[r])}async mkdir(e){const r=this.getPath(e.directory,e.path),t=e.recursive,i=r.substr(0,r.lastIndexOf("/")),n=(r.match(/\//g)||[]).length,s=await this.dbRequest("get",[i]),a=await this.dbRequest("get",[r]);if(n===1)throw Error("Cannot create Root directory");if(a!==void 0)throw Error("Current directory does already exist.");if(!t&&n!==2&&s===void 0)throw Error("Parent directory must exist");if(t&&n!==2&&s===void 0){const d=i.substr(i.indexOf("/",1));await this.mkdir({path:d,directory:e.directory,recursive:t})}const c=Date.now(),o={path:r,folder:i,type:"directory",size:0,ctime:c,mtime:c};await this.dbRequest("put",[o])}async rmdir(e){const{path:r,directory:t,recursive:i}=e,n=this.getPath(t,r),s=await this.dbRequest("get",[n]);if(s===void 0)throw Error("Folder does not exist.");if(s.type!=="directory")throw Error("Requested path is not a directory");const a=await this.readdir({path:r,directory:t});if(a.files.length!==0&&!i)throw Error("Folder is not empty");for(const c of a.files){const o=`${r}/${c.name}`;(await this.stat({path:o,directory:t})).type==="file"?await this.deleteFile({path:o,directory:t}):await this.rmdir({path:o,directory:t,recursive:i})}await this.dbRequest("delete",[n])}async readdir(e){const r=this.getPath(e.directory,e.path),t=await this.dbRequest("get",[r]);if(e.path!==""&&t===void 0)throw Error("Folder does not exist.");const i=await this.dbIndexRequest("by_folder","getAllKeys",[IDBKeyRange.only(r)]);return{files:await Promise.all(i.map(async s=>{let a=await this.dbRequest("get",[s]);return a===void 0&&(a=await this.dbRequest("get",[s+"/"])),{name:s.substring(r.length+1),type:a.type,size:a.size,ctime:a.ctime,mtime:a.mtime,uri:a.path}}))}}async getUri(e){const r=this.getPath(e.directory,e.path);let t=await this.dbRequest("get",[r]);return t===void 0&&(t=await this.dbRequest("get",[r+"/"])),{uri:(t==null?void 0:t.path)||r}}async stat(e){const r=this.getPath(e.directory,e.path);let t=await this.dbRequest("get",[r]);if(t===void 0&&(t=await this.dbRequest("get",[r+"/"])),t===void 0)throw Error("Entry does not exist.");return{type:t.type,size:t.size,ctime:t.ctime,mtime:t.mtime,uri:t.path}}async rename(e){await this._copy(e,!0)}async copy(e){return this._copy(e,!1)}async requestPermissions(){return{publicStorage:"granted"}}async checkPermissions(){return{publicStorage:"granted"}}async _copy(e,r=!1){let{toDirectory:t}=e;const{to:i,from:n,directory:s}=e;if(!i||!n)throw Error("Both to and from must be provided");t||(t=s);const a=this.getPath(s,n),c=this.getPath(t,i);if(a===c)return{uri:c};if(R(a,c))throw Error("To path cannot contain the from path");let o;try{o=await this.stat({path:i,directory:t})}catch{const l=i.split("/");l.pop();const f=l.join("/");if(l.length>0&&(await this.stat({path:f,directory:t})).type!=="directory")throw new Error("Parent directory of the to path is a file")}if(o&&o.type==="directory")throw new Error("Cannot overwrite a directory with a file");const d=await this.stat({path:n,directory:s}),h=async(u,l,f)=>{const p=this.getPath(t,u),b=await this.dbRequest("get",[p]);b.ctime=l,b.mtime=f,await this.dbRequest("put",[b])},y=d.ctime?d.ctime:Date.now();switch(d.type){case"file":{const u=await this.readFile({path:n,directory:s});r&&await this.deleteFile({path:n,directory:s});let l;!(u.data instanceof Blob)&&!this.isBase64String(u.data)&&(l=E.UTF8);const f=await this.writeFile({path:i,directory:t,data:u.data,encoding:l});return r&&await h(i,y,d.mtime),f}case"directory":{if(o)throw Error("Cannot move a directory over an existing object");try{await this.mkdir({path:i,directory:t,recursive:!1}),r&&await h(i,y,d.mtime)}catch{}const u=(await this.readdir({path:n,directory:s})).files;for(const l of u)await this._copy({from:`${n}/${l.name}`,to:`${i}/${l.name}`,directory:s,toDirectory:t},r);r&&await this.rmdir({path:n,directory:s})}}return{uri:c}}isBase64String(e){try{return btoa(atob(e))==e}catch{return!1}}}g._debug=!0;export{g as FilesystemWeb};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
