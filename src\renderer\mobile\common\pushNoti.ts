// @ts-nocheck
import {PusherBeamPlugin} from '../BeamPlugin'
import config from '../../../main/config';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import { notificationSchedule } from './notificationHandler';
import { markNotificationAsRead } from '../library/helper';
const { commonAppEventsofPusher } = config
const { privateEvents } = commonAppEventsofPusher;
const state = useGlobalStore.getState();
let notificationSet;
PusherBeamPlugin.initializePusherBeam({
beamId: import.meta.env.VITE_BEAM_ID
});

export const initPushNoti = async () => {
  notificationSet = new Set();
  // console.log('PusherBeamPlugin', JSON.stringify(PusherBeamPlugin))
      // Request permission to use push notifications
    // iOS will prompt user and return if they granted permission or not
    // Android will just grant without prompting
    
    PusherBeamPlugin.requestPermissions().then(result => {
        if (result.receive === 'granted') {
          // Register with Apple / Google to receive push via APNS/FCM
        //   PusherBeamPlugin.register();
  // PusherBeamPlugin.addDeviceInterest({ interest: 'test-private-connection' })

        } else {
          // Show some error
          console.error('permission not granted')
        }
      });

//     PushNotifications.addListener('registration', token => {
//     console.info('Registration token: ', token.value);
//   });

//     PushNotifications.addListener('registrationError', err => {
//     console.error('Registration error: ', err.error);
//   });

  PusherBeamPlugin.addListener('pushNotificationReceived', notification => {
    console.log('Push notification received: ', JSON.stringify(notification));
    let notificationData = '';
    if ('aps' in notification.data) notificationData = notification.data.aps.data.notification;
    if ('notification' in notification.data) notificationData = JSON.parse(notification.data.notification);
    console.log("notificationData", JSON.stringify(notificationData))
    if (notificationData) {
      state.setPusherEvent(notificationData);
      const id = notificationData.notificationId;
      if(id && !notificationSet?.has(id)){
        notificationSet.add(id);
        notificationSchedule(notificationData.title, notificationData.body, notificationData)
        markNotificationAsRead([notificationData]);
      }
    }

  });

PusherBeamPlugin.addListener('pushNotificationActionPerformed', performedData => {
  let notificationData = '';
    if ('aps' in performedData.notification.data) notificationData = performedData.notification.data.aps.data.notification;
    if ('notification' in performedData.notification.data) notificationData = JSON.parse(performedData.notification.data.notification);
    if (notificationData.event) {
      state.setPusherActionEvent(notificationData.event);
    }
    console.log('Push notification action performed', performedData, JSON.stringify(notificationData));
    if(notificationData.navigation){
        // if(notificationData.navigation.state) state.setNavigationStateForNotification(notificationData.navigation.state);
        if(notificationData.navigation.mobileRoutePath) state.setDeepLinkNavigate({'routePath' : "/"+notificationData.navigation.mobileRoutePath, state: notificationData.navigation.state})
    }
});

}

export const addInterest = (channelName: string) => {
  PusherBeamPlugin.addDeviceInterest({ interest: channelName })
}

export const addAuth = (userId: string, authURL: string, headers: {
  accessToken: string;
  emailId: string;
}
) => {
   
  return PusherBeamPlugin.setUserID({
    "beamsAuthURL": authURL,
    "userID": userId,
    "headers": headers
  })
}

export const  unRegister =()=>{
  PusherBeamPlugin.clearAllState().then(x=>{
    console.log('x', x)
  })
  PusherBeamPlugin.removeAllListeners();
}

// export const registerNotifications = async () => {
//   let permStatus = await PushNotifications.checkPermissions();

//   if (permStatus.receive === 'prompt') {
//     permStatus = await PushNotifications.requestPermissions();
//   }

//   if (permStatus.receive !== 'granted') {
//     throw new Error('User denied permissions!');
//   }

//   await PushNotifications.register();
// }

// const getDeliveredNotifications = async () => {
//   const notificationList = await PushNotifications.getDeliveredNotifications();
//   console.log('delivered notifications', notificationList);
// }