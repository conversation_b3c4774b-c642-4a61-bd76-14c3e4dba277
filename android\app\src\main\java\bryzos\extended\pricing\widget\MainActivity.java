package bryzos.extended.pricing.widget;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;

import com.getcapacitor.BridgeActivity;
import bryzos.extended.pricing.widget.PusherBeamPlugin;
import com.pusher.pushnotifications.PushNotifications;

public class MainActivity extends BridgeActivity {

    @Override
    public void onCreate(Bundle savedInstanceState) {
        registerPlugin(PusherBeamPlugin.class);
        registerPlugin(CrashPlugin.class);
        registerPlugin(FileDownloadPlugin.class);
        registerPlugin(AndroidSdkPlugin.class);
        // PushNotifications.start(getApplicationContext(), "4d032e59-16c7-41ba-b977-4396dc559b07");
        Log.d("Test123","Main activity called");
        super.onCreate(savedInstanceState);
    }
    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);

        // Handle the intent if it is a deep link
        if (Intent.ACTION_VIEW.equals(intent.getAction())) {
            Uri uri = intent.getData();
            Log.d("onNewIntent","Opened app through url:" + uri.toString());
            // Use the Uri object to determine what to do in your app
        }
    }

}
