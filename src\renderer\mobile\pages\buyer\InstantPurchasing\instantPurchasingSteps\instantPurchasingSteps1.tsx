//@ts-nocheck
import { useIonRouter, useIonViewDidEnter, useIonViewWillLeave } from "@ionic/react";
import styles from './instantPurchasingSteps.module.scss'
import { CustomMenu } from "../../../../components/CustomMenu";
import { useEffect, useRef, useState } from "react";
import clsx from 'clsx';
import { useGlobalStore, mobileDiaglogConst } from "@bryzos/giss-ui-library";
import useDialogStore from "../../../../components/Dialog/DialogStore";

function InstantPurchasingSteps1(props: any) {
    const userData = useGlobalStore(state => state.userData);
    const [states, setStates] = useState([]);
    const [enableNextBtn, setEnableNextBtn] = useState(true);
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const {productData , referenceData} = useGlobalStore()
    const router = useIonRouter();

    const { ref, ...rest } = props.register("internal_po_number");

    const internalPoNumberRef = useRef<HTMLInputElement>(null);

    useIonViewDidEnter(() => {
        internalPoNumberRef.current?.focus();
    }, []);

    useIonViewWillLeave(()=>{
        resetDialogStore();
    },[])

    useEffect(() => {
        const isEnable = !!(props.getValues('internal_po_number')?.trim() && props.getValues("shipping_details.line1")?.trim() && props.getValues("shipping_details.city")?.trim() && props.getValues("shipping_details.state_id") && props.getValues("shipping_details.zip")?.trim() && props.getValues("delivery_date") && !props.watch('shipping_details.validating_state_id_zip'));
        setEnableNextBtn(isEnable);
        if(!isEnable)props.setMaxStepsEnabled(1);
    },[props.watch()])

    useEffect(()=>{
        if (referenceData) {
            setStates(referenceData?.ref_states);
        }
    },[productData, referenceData, props.watch('recevingHours')]);

    const routeBackToSetting = ()=>{
        router.push('/setting',{animate:true,direction:'forward'});
        resetDialogStore();
    }

    const showErrorDialogue = ()=>{
        showCommonDialog(mobileDiaglogConst.receivingHrsEmpty,[{name: mobileDiaglogConst.stay, action: resetDialogStore},{name: mobileDiaglogConst.goToSettings, action: routeBackToSetting}]);
    }

    return (
        <div className={styles.stepsContentMain}>
            <div className={styles.instantPurchasingForm}>
                <div className={styles.inputPurchasing}   >
                    <input {...rest} type="text" placeholder='Job / PO #' maxLength={20}
                    ref={(e) => { ref(e); internalPoNumberRef.current = e; }}
                    onBlur={(e) => {
                        e.target.value = e.target.value.trim();
                        rest.onBlur(e);
                        props.saveUserActivity();
                    }} />
                </div>
                <div className={styles.inputPurchasing}  >
                    <input {...props.register("shipping_details.line1")} type="text" placeholder='Delivery to: Address' 
                    onBlur={(e) => {
                        e.target.value = e.target.value.trim();
                        props.register("shipping_details.line1").onBlur(e);
                        props.saveUserActivity();
                    }} />
                </div>
                <div className={styles.inputPurchasing}  >
                    <input {...props.register("shipping_details.city")} type="text" placeholder='City' 
                    onBlur={(e) => {
                        e.target.value = e.target.value.trim();
                        props.register("shipping_details.city").onBlur(e);
                        props.saveUserActivity();
                    }} />
                </div>
                <div className={clsx(styles.stateZipcode, styles.stateDropdown)}>
                    <div className={styles.grid1}>
                        <span className={(props.errors?.shipping_details?.state_id?.message && styles.errorMsg)}>
                        <CustomMenu
                            control={props.control}
                            name={'shipping_details.state_id'}
                            placeholder={'State'}
                            MenuProps={{
                                classes: {
                                    paper: styles.Dropdownpaper,
                                },
                            }}
                            onChange={() => props.saveUserActivity()}
                            items={states.map((x: any) => ({ title: x.code, value: x.id }))}
                            className={'selectDropdown deliveryDateDropdown'}                          
                        />
                        </span>
                    </div>
                    <div className={styles.grid1}>
                        <input className={(props.errors?.shipping_details?.zip?.message && styles.errorMsg)} {...props.register("shipping_details.zip")}  type="tel" placeholder="Zip Code"
                            onChange={(e) => {
                                props.register("shipping_details.zip").onChange(e);
                                const zipCode = e.target.value.replace(/\D/g, '');
                                props.setValue("shipping_details.zip", zipCode);
                            }} maxLength="5"
                            onBlur={(e) => {
                                e.target.value = e.target.value.trim();
                                props.register("shipping_details.zip").onBlur(e);
                                props.saveUserActivity();
                            }}
                        />
                    </div>
                </div>
                <div className={styles.inputPurchasing}  >
                    <CustomMenu
                        control={props.control}
                        name={'delivery_date'}
                        placeholder={'Choose Delivery Date'}
                        MenuProps={{
                            classes: {
                                paper: clsx(styles.Dropdownpaper, styles.deliveryDateDropdownList)
                            },
                        }}
                        onfocus= {() => {
                            props.setDeliveryStateFocusEnable(true)
                        }}
                        onBlur= {() => {
                            props.setDeliveryStateFocusEnable(false)
                        }}
                        onChange={(e) =>{ 
                            props.setValue('delivery_date_offset', props.deliveryDateMap[e.target.value].days_to_add);
                            props.saveUserActivity()
                        }}
                        disabled = {props.disableDeliveryDate}
                        onClick={() => {
                            if(props.disableDeliveryDate){
                                showErrorDialogue();
                            }
                        }}
                        renderValue={(val: any) => val ? val : 'Choose Delivery Date'}
                        items={props.deliveryDates}
                        className={'selectDropdown deliveryDateDropdown'}
                    />
                </div>
            </div>
            <div className={styles.stepsBtmSection}>
                <button className={styles.proceedToStepBtn} onClick={() => props.setSelectedInstantPricingStep(2)} disabled={!enableNextBtn || Object.keys(props.errors ?? {}).length > 0 || props.watch('shipping_details.validating_state_id_zip')}>Proceed to<br/>Step 2</button>
            </div>
        </div>
    );
}
export default InstantPurchasingSteps1;
