import * as React from 'react';
import LinearProgress, { LinearProgressProps } from '@mui/material/LinearProgress';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

function LinearProgressWithLabel(props: LinearProgressProps & { value: number }) {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Box sx={{ width: '100%', mr: 1 }}>
        <LinearProgress variant="determinate"  color="success" {...props} />
      </Box>
      <Box sx={{ minWidth: 35 }}>
        {
          props.value && 
          <Typography
          variant="body2"
          sx={{ color: 'white' }}
          >{`${Math.round(props.value)}%`}</Typography>
        }
      </Box>
    </Box>
  );
}

export default function LinearProgressBar({progress}) {

  return (
    <Box sx={{ width: '85%' }}>
      <LinearProgressWithLabel value={progress} />
    </Box>
  );
}