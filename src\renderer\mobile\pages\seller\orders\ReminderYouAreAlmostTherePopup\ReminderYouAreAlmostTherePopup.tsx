//@ts-nocheck
import { yupResolver } from "@hookform/resolvers/yup";
import { Autocomplete, Dialog, TextField } from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import styles from "./ReminderYouAreAlmostTherePopup.module.scss";
import { ReactComponent as CloseIcon } from '../../../../../assets/mobile-images/close_Popup.svg';
import clsx from "clsx";
import { useEffect, useRef, useState } from "react";
import * as yup from "yup";
import { useImmer } from "use-immer";
import { ReactComponent as CheckIcon } from '../../../../../assets/mobile-images/check_icon.svg';
import axios from "axios";
import useGetCompanyLists from "../../../../library/hooks/useGetCompanyLists";
import { CustomMenu } from "../../../../components/CustomMenu";
import { useGlobalStore } from "@bryzos/giss-ui-library";
import { IonModal, useIonLoading, useIonViewWillEnter } from "@ionic/react";
import InputField from "../../../../library/component/InputField/InputField";
import {
  SEND_INVOICES_TO,
  SHIPPING_DOCS_TO,
  formatPhoneNumber,
  formatPhoneNumberRemovingCountryCode,
} from "../../../../library/helper";
import useSaveSellerSetting from "../../../../library/hooks/useSaveSellerSetting";

const schema = yup.object({
  companyName: yup.string().required("Company Name is not valid"),
  yourCompany: yup.string().trim().required('Your Company is not valid'),
  companyAddressLine: yup.string().required("Company Address is not valid"),
  companyCity: yup.string().required("Company Address is not valid"),
  companyState: yup.number().required("Company Address is not valid"),
  companyZipCode: yup.string().required("Company Address is not valid").min(5, 'Company Address is not valid'),
  firstName: yup.string().required("Your Name is not valid"),
  lastName: yup.string().required("Your Name is not valid"),
  emailAddress: yup
    .string()
    .required("Email/Phone is not valid")
    .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/,'Email/Phone is not valid')
    .max(50, 'please do not enter more than 50 characters'),
  phoneNo: yup
    .string()
    .matches(/^\(\d{3}\) \d{3}-\d{4}$/, "Email/Phone is not valid")
    .required("Phone number is required"),
});

const ReminderYouAreAlmostTherePopup = ({ isReminderPopup, open, close }: any) => {
  const {
    userData,
    setSellerSettingsData,
    setShowLoader ,referenceData
  }: any = useGlobalStore();
  const [states, setStates] = useState([]);
  const [showSucessPopup, setShowSucessPopup] = useState(false);
  const [errorKeys, setErrorKeys] = useImmer([]);
  const [companyNameValue, setCompanyNameValue] = useImmer(null);
  const [companyNameInput, setCompanyNameInput] = useState("");
  const [isCompanyNameExists, setIsCompanyNameExists] = useState(false);
  const [yourCompanyValue, setYourCompanyValue] = useImmer(null);
  const [yourCompanyInput, setYourCompanyInput] = useState("");
  const [disableYourCompany, setDisableYourCompany] = useState(true);
  const [yourCompanyList, setYourCompanyList] = useState([]);

  const { data: companyListsData, isLoading: isCompanyListsDataLoading, } = useGetCompanyLists(open);

  const {
    mutate: saveSellerSetting,
    isLoading: isSellerSettingLoading,
    data: saveSellerSettingData,
  } = useSaveSellerSetting(userData?.data?.id);

  const {
    handleSubmit,
    control,
    setFocus,
    watch,
    setValue,
    getValues,
    reset,
    register,
    setError,
    clearErrors,
    formState: { errors, isDirty, isValid },
  } = useForm({
    resolver: yupResolver(schema),
    shouldFocusError: false,
  });

  const singleErrorKey = errorKeys.find((x) => errors[x]);
  const phoneNo = watch("phoneNo");
  const modal = useRef<HTMLIonModalElement>(null);

  useEffect(()=>{
    setShowLoader(true);
  },[])

  useEffect(() => {
    if(open)
    axios.get(import.meta.env.VITE_API_SERVICE + '/user/sellingPreference')
      .then(response => {
        const getSellerSettingData = response.data.data;

        setValue("companyName", getSellerSettingData.company_name)
        setValue("yourCompany", getSellerSettingData.client_company)
        setYourCompanyInput(getSellerSettingData.client_company ?? "")
        setDisableYourCompany(getSellerSettingData.company_name === null)
        setIsCompanyNameExists(getSellerSettingData.company_name);
        setValue('companyAddressLine', getSellerSettingData.company_address_line1);
        setValue('companyCity', getSellerSettingData.company_address_city);
        setValue('companyState', getSellerSettingData.company_address_state_id);
        setValue('companyZipCode', getSellerSettingData.company_address_zip);
        setValue('firstName', getSellerSettingData.first_name);
        setValue('lastName', getSellerSettingData.last_name);
        setValue('emailAddress', getSellerSettingData.email_id);
        setValue('phoneNo', formatPhoneNumberRemovingCountryCode(getSellerSettingData.phone));
        setShowLoader(false);
        if (
          getSellerSettingData?.company_name &&
          getSellerSettingData?.client_company &&
          getSellerSettingData?.company_address_line1 &&
          getSellerSettingData?.company_address_city &&
          getSellerSettingData?.company_address_state_id &&
          getSellerSettingData?.company_address_zip &&
          getSellerSettingData?.first_name &&
          getSellerSettingData?.last_name &&
          getSellerSettingData?.email_id &&
          getSellerSettingData?.phone
        ) {
          setSellerSettingsData(true);
        }
        focusFirstInput();
      })
  }, [open]);

  useEffect(() => {
    if (saveSellerSettingData && !isSellerSettingLoading) {
      setShowSucessPopup(true);
      setShowLoader(false);
    }
  }, [isSellerSettingLoading, saveSellerSettingData]);

  const handlePhoneNoChange = (event) => {
    const { value } = event.target;
    setValue("phoneNo", formatPhoneNumber(value));
  };

  useEffect(() => {
    if (referenceData?.ref_states?.length) {
      setStates(
        referenceData.ref_states.map((state) => ({
          title: state.code,
          value: state.id,
        }))
      );
    }
  }, [referenceData]);

  useEffect(() => {
    const firstError: any = Object.keys(errors).reduce((field: string | null, a: any) => {
      return !!errors[field] ? field : a;
    }, null);

    if (firstError) {
      setFocus(firstError);
    }
  }, [errors, setFocus]);

  useEffect(() => {
    if (isDirty) {
      handleStateZipValidation('companyZipCode', 'companyState')
    }
  }, [watch('companyZipCode'), watch('companyState')])

  useEffect(() => {
    if (watch("companyName") !== null && Array.isArray(companyListsData)) {
      const companyData = companyListsData?.find((companyData: any) => companyData.company_name === watch("companyName"))
      setYourCompanyList(companyData?.client_company ?? [])
    }
  }, [watch("companyName")])

  const sellerSettingFormHandler = (data: any) => {
    setErrorKeys([]);
    setShowLoader(true);
    const payload =
    {
      data: {
        company_name: data.companyName,
        client_company: data.yourCompany,
        address: {
          line1: data.companyAddressLine,
          city: data.companyCity,
          state_id: data.companyState,
          zip: data.companyZipCode,
        },
        first_name: data.firstName,
        last_name: data.lastName,
        email_id: data.emailAddress,
        phone: data.phoneNo,
        send_invoices_to: SEND_INVOICES_TO,
        shipping_docs_to: SHIPPING_DOCS_TO,
      },
    }
    saveSellerSetting(payload);
  };

  const sucessPopupCloseHandler = () => {
    if (saveSellerSettingData) {
      setSellerSettingsData(true);
    }
    close();
    reset()
    setCompanyNameInput("")
    setYourCompanyInput("")
  };

  const showErrorKeyVal = (fieldNames: any) => {
    setErrorKeys(fieldNames);
  };


  const handleStateZipValidation = async (zipCode: any, stateCode: any) => {
    if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
      const payload = {
        data: {
          state_id: getValues(stateCode),
          zip_code: parseInt(getValues(zipCode)),
        },
      };
      const checkStateZipResponse = await axios.post(
        import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
        payload
      );
      if (checkStateZipResponse.data.data === true) {
        clearErrors([stateCode, zipCode]);
      } else {
        setError(
          stateCode,
          { message: "The zip code and state code do not match" },
          { shouldFocus: true }
        );
        setError(
          zipCode,
          { message: "The zip code and state code do not match" },
          { shouldFocus: true }
        );
      }
    }
  };

  const handleFormSubmit = () => {
    if (Object.keys(errors).length === 0) {
      handleSubmit(sellerSettingFormHandler)();
    } else {
      return;
    }
  };

  const focusFirstInput = () => {
    const t = setTimeout(() => {
      clearTimeout(t);

      let element;
      setIsCompanyNameExists((prev) => {
        if (!prev) {
          element = document.getElementById("companyNamePopupAutocomplete");
        } else {
          element = document.getElementById("yourCompanyPopupAutocomplete");
        }
        if (element) {
          element.focus();
        }
        return prev;
      })
    }, 400);
  }

  return (
    <>
      <IonModal className={showSucessPopup ? 'reminderPopupSuccess' : 'reminderPopup'} isOpen={open} backdropDismiss={false} onIonModalDidPresent={focusFirstInput}> 
        <button className={styles.closePopupBtn} onClick={sucessPopupCloseHandler}><CloseIcon /></button>
        <div className={clsx(styles.reminderPopupMain, 'reminderPopup')}>
          {/* {showLoader ? (
          <Loader />
        ) : ( */}
          <>
            {showSucessPopup ? (
              <div className={styles.submitPopupSection}>
                  <span><CheckIcon/></span>
                  <p className={styles.invitationSentSuccessfully}>Save Successfully</p>
                  <button className={styles.doneBtn} onClick={sucessPopupCloseHandler} >Done</button>
              </div>
            ) : (
              <>
                {isReminderPopup ? (
                  <div className={styles.reminderPopupTitle}>
                    <p className={styles.titleText}>
                      REMINDER: TELL US WHO YOU ARE
                    </p>
                    <p className={styles.titleSmallText}>
                      We need to know a little bit about you before you can begin
                      accepting orders. Please provide the following information.{" "}
                    </p>
                  </div>
                ) : (
                  <div className={styles.reminderPopupTitle}>
                    <p className={styles.titleText}>YOU'RE ALMOST THERE!</p>
                    <p className={styles.titleSmallText}>
                      We need a few more details before we can confirm this order
                      with you.
                    </p>
                  </div>
                )}

                {/* <div className={styles.dFlex}>
                  {singleErrorKey && errors[singleErrorKey]?.message && (
                    <span className={styles.errorMessage}>
                      <ErrorMessageIcon /> {errors[singleErrorKey]?.message}
                    </span>
                  )}
                </div> */}

          <div className={styles.innerContent}>

                <div
                  className={clsx(styles.FormInputGroup)}
                >
                  <span className={styles.lblInput}>Main Company</span>
                  <span
                    className={clsx(
                      styles.inputSection, styles.comanyName)}
                  >
                    {isCompanyNameExists ?
                      <p className={styles.inputField} > {watch("companyName")} </p>
                      :
                      <Controller
                        name="companyName"
                        control={control}
                        render={({ field: { ...rest } }) => (
                          <Autocomplete
                            value={companyNameValue}
                            onChange={(event, value) => {
                              setCompanyNameValue(value);
                              if (value?.company_name) {
                                setDisableYourCompany(false)
                              } else {
                                setDisableYourCompany(true)
                                setYourCompanyList([])
                                setYourCompanyInput("")
                              }
                              rest.onChange(value?.company_name ?? null);
                            }}
                            inputValue={companyNameInput}
                            onInputChange={(event, newInputValue) => {
                              setCompanyNameInput(newInputValue);
                              setValue("companyName", getValues("companyName"), { shouldDirty: true });
                            }}
                            className={'companySelectDropdown'}
                            id="companyNamePopupAutocomplete"
                            classes={{
                              root: styles.autoCompleteDesc,
                              popper: styles.autocompleteDescPanel,
                              paper: styles.autocompleteDescInnerPanel,
                              listbox: styles.listAutoComletePanel,
                            }}
                            options={companyListsData?.length ? companyListsData : []}
                            sx={{ width: '100%' }}
                            renderInput={(params) => <TextField className={clsx(styles.companyInput, errors.companyName && styles.errorInput1)} {...params} placeholder="Enter Parent Company Name" />}
                            getOptionLabel={(item) => {
                              return item?.company_name ?? "";
                            }}
                          />
                        )}
                      />
                    }
                  </span>
                </div>

                <div className={clsx(styles.FormInputGroup, errors.yourCompany && styles.FormInputGroupError)}
                  onClick={() => showErrorKeyVal(['yourCompany'])}>
                  <span className={styles.lblInput}>
                    Your Company
                  </span>
                  <span className={clsx(styles.inputSection, styles.comanyName)}>
                    <Controller
                      name="yourCompany"
                      control={control}
                      render={({ field: { ...rest } }) => (
                        <Autocomplete
                          freeSolo
                          disabled={disableYourCompany}
                          value={yourCompanyValue}
                          onChange={(event, value) => {
                            setYourCompanyValue(value);
                            rest.onChange(value ?? null);
                          }}
                          inputValue={yourCompanyInput}
                          onInputChange={(event, newInputValue) => {
                            setYourCompanyInput(newInputValue);
                            rest.onChange(newInputValue)
                          }}
                          className={'companySelectDropdown'}
                          id="yourCompanyPopupAutocomplete"
                          classes={{
                            root: styles.autoCompleteDesc,
                            popper: styles.autocompleteDescPanel,
                            paper: styles.autocompleteDescInnerPanel,
                            listbox: styles.listAutoComletePanel,
                          }}
                          options={yourCompanyList?.length ? yourCompanyList : []}
                          sx={{ width: '100%' }}
                          renderInput={(params) => <TextField className={clsx(styles.companyInput, errors.yourCompany && styles.errorInput1)} {...params} placeholder="Enter Your Company" />}
                          getOptionLabel={(item) => {
                            return item ?? "";
                          }}
                        />
                      )}
                    />
                  </span>
                </div>

                <div
                  className={clsx(
                    (errors.companyAddressLine ||
                      errors.companyState ||
                      errors.companyCity ||
                      errors.companyZipCode) &&
                    styles.FormInputGroupError
                  )}
                  onClick={() =>
                    showErrorKeyVal([
                      "companyAddressLine",
                      "companyState",
                      "companyCity",
                      "companyZipCode",
                    ])
                  }
                >
                  <span className={styles.lblInput}>Company Address</span>
                  <div className="dflex flexColunm">
                    <span
                      className={clsx(
                        styles.inputSection,
                        styles.bdrBtmRightRadius0,
                        styles.bdrBtm0,
                        errors.companyAddressLine && styles.borderOfError
                      )}
                    >
                      <InputField
                        fieldName="companyAddressLine"
                        placeholder="Address"
                        control={control}
                        customClasses={clsx(styles.inputField, errors?.companyAddressLine?.message && styles.errorMsg)}
                      />
                    </span>
                     <div className="dflex w100">
                     <span className={clsx( 'w100', errors.companyCity && styles.borderOfError)}>
                        <span className={styles.lblInput}>City</span>
                        <InputField
                          fieldName="companyCity"
                          placeholder="City"
                          control={control}
                          customClasses={clsx(styles.inputField, errors?.companyCity?.message && styles.errorMsg)}
                        />
                      </span>
                     </div>
                    <div className={clsx(styles.stateDropdown,"dflex")}>
                      <span
                        className={clsx(
                          styles.grid1,
                          errors.companyState && styles.borderOfError
                        )}
                      >
                          <span className={clsx(styles.lblInput,styles.lblState)}>State</span>
                          <span className={(errors?.companyState?.message && 'errorMsgDropdown')}>
                            <CustomMenu
                              control={control}
                              name={"companyState"}
                              placeholder={"Select"}
                              MenuProps={{
                                disablePortal: true,
                                classes: {
                                  paper: styles.Dropdownpaper
                                },
                              }}
                              items={states}
                              className={"selectDropdown"}
                            />
                          </span>
                       
                      </span>
                      <span
                        className={clsx(
                          styles.grid1,
                          errors.companyZipCode && styles.borderOfError
                        )}
                      >
                         <span className={styles.lblInput}>Zip Code</span>
                        <input
                          
                          {...register("companyZipCode")}
                          onChange={(e) => {
                            register("companyZipCode").onChange(e);
                            const zipCode = e.target.value.replace(/\D/g, '');
                            setValue('companyZipCode', zipCode);
                          }}
                          placeholder="Enter"
                          control={control}
                          type="tel"
                          maxLength={5}
                          className={clsx(styles.inputField, errors?.companyZipCode?.message && styles.errorMsg)}
                        />
                      
                      </span>
                    </div>
                  </div>
                </div>

                <div className={clsx(styles.stateDropdown)}>
                    <span
                      className={clsx(
                        styles.grid1,
                        errors.firstName && styles.borderOfError
                      )}
                    >
                        <span className={styles.lblInput}>Your First Name</span>
                      <InputField
                        fieldName="firstName"
                        placeholder="First name"
                        control={control}
                        customClasses={clsx(styles.inputField, errors?.firstName?.message && styles.errorMsg)}
                      />
                    </span>
                
                  <span
                    className={clsx(
                      styles.grid1,
                      errors.lastName && styles.borderOfError
                    )}
                  >
                      <span className={styles.lblInput}>Your Last name</span>
                    <InputField
                      fieldName="lastName"
                      placeholder="Last name"
                      control={control}
                      customClasses={clsx(styles.inputField, errors?.lastName?.message && styles.errorMsg)}
                    />
                  </span>
                </div>

                <div
                  className={clsx(
                    styles.FormInputGroup,
                    (errors.emailAddress || errors.phoneNo) &&
                    styles.FormInputGroupError
                  )}
                  onClick={() => showErrorKeyVal(["emailAddress", "phoneNo"])}
                >
                  <span className={styles.lblInput}>Email</span>
                  <span
                    className={clsx(
                      styles.inputSection,
                      styles.bdrRadius0,
                      styles.bdrRight0,
                      errors.emailAddress && styles.borderOfError
                    )}
                  >
                    <InputField
                      fieldName="emailAddress"
                      placeholder="Email address"
                      control={control}
                      customClasses={clsx(styles.inputField, errors?.emailAddress?.message && styles.errorMsg)}
                    />
                  </span>
                  <span className={styles.lblInput}>Phone</span>
                  <span
                    className={clsx(
                      styles.inputSection,
                      styles.phoneNo,
                      errors.phoneNo && styles.borderOfError
                    )}
                  >
                    <input
                     className={clsx(styles.inputField, errors?.phoneNo?.message && styles.errorMsg)}
                      maxLength={14}
                      pattern="[0-9]"
                      type="tel"
                      {...register("phoneNo")}
                      onChange={(e) => {
                        register("phoneNo").onChange(e);
                        handlePhoneNoChange(e);
                      }}
                      placeholder="(xxx) xxx-xxxx"
                    />
                  </span>
                </div>

                <div className={styles.noteText}>
                    Please note you will also need to upload your W-9 and complete
                    the banking info to receive payment on this order.
                </div>
                </div>
                <div className={styles.settingBtnMain}>
                  <button onClick={handleFormSubmit} disabled={!isDirty} type="submit" className={styles.saveSettingsBtn}>
                    Save
                  </button>
                </div>
                
              </>
            )}
          </>
          {/* )} */}
        </div>
      </IonModal>
    </>
  );
};

export default ReminderYouAreAlmostTherePopup;
