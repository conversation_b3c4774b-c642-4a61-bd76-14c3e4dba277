.orderContent {
    padding: 16px 0px 0px 16px;
    height: calc(100% - 75px);

    .orderSearch{
        padding-right: 16px;
    }

    .btnOfOrderSearch {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        gap: 5px;
        width: 100%;
        border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
        padding-bottom: 11.5px;


        button {
            font-family: Noto Sans;
            font-size: 12px;
            font-weight: 300;
            color: rgba(255, 255, 255);
            flex: 1;
            padding: 10px 0;
            border-radius: 8px;
            background-color: transparent;
            text-align: center;
            opacity: 0.5;
        }

        .activeBtn {
            background-color: rgba(255, 255, 255, 0.1);
            opacity: unset;
            font-weight: normal;
        }
    }

    .orderHeading {
        font-family: Noto Sans Display;
        font-size: 16px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: #fff;
        margin-top: 8px;
        margin-bottom: 8px;
        padding-right: 16px;
    }

    .listOfOrder {
        height: calc(100% - 130px);

        ul {
            height: 100%;
            overflow: auto;
            padding-right: 16px;
        }
    }

    li {
        list-style: none;
        border-bottom:solid 0.5px rgba(255, 255, 255, 0.35);
        padding:8px 0px;
    }

    ion-item{
        --padding-start: 0px;
        --border-color:transparent
    }

    ion-list{
        padding-top: 0px;
    }

    .liOfOrder {
        display: flex;
        width: 100%;
        gap: 12px;
        text-align: left;
        margin-top: 0px;

        .orderDetailsGrid {
            flex-grow: 1;

            .firstLine {
                font-family: Noto Sans Display;
                font-size: 16px;
                line-height: 1.4;
                text-align: left;
                color: #fff;
                margin-bottom: 8px;
            }

            .gridContent {
                display: flex;
            }

            .colGrid1 {
                min-width: 80px;
            }
            .colGrid2 {
                min-width: 80px;
                margin-right: auto;
            }

            .orderDetailslbl {
                opacity: 0.7;
                font-family: Noto Sans Display;
                font-size: 10px;
                font-weight: 300;
                line-height: 1.2;
                text-align: left;
                color: #fff;
            }

            .orderDetailsTxt {
                font-family: Noto Sans Display;
                font-size: 12px;
                font-weight: normal;
                line-height: 1.2;
                text-align: left;
                color: #fff;
            }
            .textRight{
                text-align: right;
            }
        }
    }

}

.availabeAt {
    padding-bottom: 8px;
    color: #fff;
    display: block;
}


.upcomingHeading {
    font-family: Noto Sans Display;
    font-size: 12px;
    font-weight: 300;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    margin-top: 4px;
}

.upcomingSmallHeading {
    opacity: 0.7;
    font-family: Noto Sans Display;
    font-size: 12px;
    font-weight: 300;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    margin-top: 4px;
}

.bigHeading {
    font-family: Noto Sans Display;
    font-size: 12px;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    margin: 5.5px 0px;
    @media (max-width: 400px) {
        font-size: 11px;
    }
}

.smallHeading {
    opacity: 0.7;
    font-family: Noto Sans Display;
    font-size: 12px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
}

.aTag {
    width: 20px;
    height: 20px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 4px;
    border-radius: 2px;
    border: solid 0.5px rgba(255, 255, 255, 0.6);
    background-color: rgba(0, 0, 0, 0.25);
    margin: 0px 4px;
    vertical-align: middle;
}