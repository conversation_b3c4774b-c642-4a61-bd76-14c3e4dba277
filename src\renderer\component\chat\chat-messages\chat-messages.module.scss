.chatMessagesContent {
    display: flex;
    padding: 0px 6px;

    .messageText {
        font-family: Roboto;
        display: flex;
        flex-direction: column;
        width: auto;
        min-width: 100px;
        background: #fff;
        margin: 0px 0px 8px 0px;
        padding: 10px 12px 6px 12px;
        border-radius: 10px;
        border: solid 1px #cecece;
        clear: both;
        font-size: 14px;
        position: relative;
        margin-left: 35px;

        p {
            margin-bottom: 6px;
        }

        .chatArrow {
            width: 0;
            height: 0;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
            border-right: 7px solid #fff;
            position: absolute;
            left: -6px;
            top: 12px;
            transform: translateY(-50%);
        }

        .chatTime {
            color: #000;
            opacity: 0.5;
            font-size: 10px;
            text-align: right;
            white-space: nowrap;
            margin-left: auto;
        }

    }

    .ownMessagesMainContent{
        order: 1;
    }

    .messageContent{
        display: flex;
        width: 80%;
    }
    .ownMessages {
        background-color: rgba(174, 208, 247, 0.9) !important;
        margin-left: auto;
        margin-right: 10px;
        border: solid 1px rgba(136, 188, 247, 0.9);

        .chatArrow {
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
            border-left: 7px solid rgba(174, 208, 247, 0.9);
            border-right: 0px;
            right: -6px;
            top: 12px;
            left: unset;
            transform: translateY(-50%);
        }

    }

    .ownMessagesBedge1 {
        top: 12px;
        right: -14px;
        font-size: 10px;
        font-weight: bold;
        min-width: 28px;
        padding: 2px 2px;
        height: 28px;
        border-radius: 50%;
    }

    .ownMessagesBedge {
        font-size: 10px;
        font-weight: bold;
        min-width: 28px;
        padding: 2px 2px;
        height: 28px;
        border-radius: 50%;
        top: 12px;
        right: 14px !important;
        left: auto;
        background-color: #0072ff;
    }
    .ownMessagesSection{
        display: flex;
        margin-left: auto;
        margin-right: 24px;
    }
}

.dateMain{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 8px;
    position: sticky;
    top: 0;
    z-index: 99;

    .date {
        font-size: 14px;
        background-color: #dbdbdb;
        border-radius: 12px;
        padding: 2px 16px;
        color: #000;
        text-align: center;
    }
    
}

.userName {
    font-size: 14px;
    text-transform: capitalize;
    margin-bottom: 3px;
}

.noChatFound {
    color: #fff;
    font-size: 18px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 580px;


    p {
        font-size: 16px;
        color: #fff;
    }
}

.reactionTooltip{
    margin-top: 0px !important;
    background-color: transparent !important;
    padding: 0px;
}