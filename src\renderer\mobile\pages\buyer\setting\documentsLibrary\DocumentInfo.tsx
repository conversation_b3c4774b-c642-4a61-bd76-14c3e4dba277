// @ts-nocheck
import { IonPage, IonContent, useIonRouter, IonModal, useIonViewWillEnter, useIonViewWillLeave, useIonLoading } from '@ionic/react';
import styles from './DocumentInfo.module.scss';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { documentInfoSchema } from '../SettingSchema';
import { useEffect, useRef, useState } from 'react';
import { CustomMenu } from '../../../../components/CustomMenu';
import { buyerSettingPayLoadFormatter, downloadFiles, formatPhoneNumberWithCountryCode, saveUserSetting, uploadBuyerDocumentInfoToS3 } from '../../../../library/helper';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import axios from 'axios';
import clsx from 'clsx';
import { buyerSettingConst, certificateList, prefixUrl, mobileDiaglogConst, commomKeys, fileType } from '../../../../library/common';
import { v4 as uuidv4 } from 'uuid';
import { Dialog, ListSubheader, MenuItem, Select } from '@mui/material';
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import { ReactComponent as UploadIcon } from '../../../../../assets/mobile-images/icon_Upload.svg';
import { ReactComponent as DeleteIcon } from '../../../../../assets/mobile-images/delete.svg';
import { ReactComponent as ClosePopTwo } from '../../../../../assets/mobile-images/close_Popup.svg';
import { ReactComponent as IconLeftArrow } from '../../../../../assets/mobile-images/iconleftArrow.svg';
import { ReactComponent as AddStateIcon } from '../../../../../assets/mobile-images/Add-State.svg';
import { ReactComponent as ApprovedCheckIcon } from '../../../../../assets/mobile-images/check_approved.svg';
import useBuyerSettingStore, { BuyerSetting, SalesCertificate } from '../BuyerSettingStore';
import useDialogStore from '../../../../components/Dialog/DialogStore';
import DialogPopup from '../../../../library/component/DialogPopup/DialogPopup';
import ResaleCertificate from './ResaleCertificate/ResaleCertificate';

const ExpirationMenuPropsTop = {
    classes: {
        paper: clsx(styles.Dropdownpaper)
    },
    // anchorOrigin: {
    //     vertical: -31,
    //     horizontal: "left"
    // },
    // transformOrigin: {
    //     vertical: "bottom",
    //     horizontal: "left"
    // },
}

const DocumentInfo = (props) => {
    return(
        props.documentLibInfo ? 
            <DocumentLibraryInfo {...props}  />
        :
            <IonPage>
                <DocumentLibraryInfo />
            </IonPage>
    )
}

const resaleCertificatesDefaultValue ={
    cerificateFileName: null,
    cerificateS3Url: null,
    stateId: null,
    expirationDate: null,
    status: null,
    id: null,
    isDeletable: true,
}
const defaultValues = {
    resaleCertificates: [resaleCertificatesDefaultValue, resaleCertificatesDefaultValue]  
}

const DocumentLibraryInfo = (props) => {
    const { 
        register, 
        handleSubmit, 
        getValues, 
        clearErrors, 
        setValue, 
        watch, 
        setError, 
        trigger,
        control,
        reset, 
        formState: { errors, dirtyFields, isDirty, isValid } } = useForm({
            defaultValues: defaultValues,
        resolver: yupResolver(documentInfoSchema)
    });
    const [states, setStates] = useState([]);
    const [ResaleExpiration, setResaleExpiration] = useState([]);
    // const [resaleCertificateList, setResaleCertificateList] = useState(null);
    const [certificateState, setCertificateState] = useState('0');
    const [signedUrl, setSignedUrl] = useState('');
    const [uploadCertName, setUploadCertName] = useState('');
    const [CertStatus, setCertStatus] = useState(null);
    const [CertId, setCertId] = useState(null);
    const [uploadCertProgress, setUploadCertProgress] = useState(null);
    const [isSaveDisable, setIsSaveDisable] = useState(true);
    const [showBackToPoBtn, setShowBackToPoBtn] = useState(false);
    // const [validationInProgress, setValidationInProgress] = useState(true);
    // const [openConfirmationDialog, setOpenConfirmationDialog] = useState(0);
    const {userData, setShowLoader , referenceData} = useGlobalStore();
    const modal = useRef<HTMLIonModalElement>(null);
    const { documentLibInfo, setDocumentLibInfo, buyerSetting, setBuyerSettingInfo } = useBuyerSettingStore();
    const documentInfo =   props.documentLibInfo ?? documentLibInfo ;
    const router = useIonRouter()
    const [present, dismiss] = useIonLoading();
    const { showCommonDialog, resetDialogStore } = useDialogStore();

    const [showDialogPopup, setShowDialogPopup] = useState(false);
    const [dialogTitle, setDialogTitle] = useState('');
    const [dialogContent, setDialogContent] = useState('');
    const [dialogBtnTitle, setDialogBtnTitle] = useState('');
    const [dialogType, setDialogType] = useState('');

    const { fields, remove, append, replace } = useFieldArray({ control, name: "resaleCertificates"});
    const [scrollToBottom, setScrollToBottom] = useState(false);
    const scrollStateRef = useRef(null);

    useEffect(()=>{
        if(isDirty){
            setShowBackToPoBtn(false)
            setIsSaveDisable(false);
        }

        // const handleBackButton = (ev: BackButtonEvent) => {
        //     ev.detail.register(10, async () => {
        //         backToSetting();
        //     });
        // };

        // document.addEventListener('ionBackButton', handleBackButton);
        // return () => {
        //     document.removeEventListener('ionBackButton', handleBackButton);
        // };
    },[isDirty])

    useEffect(()=>{
        if(referenceData){
            let expiresData = [];
            referenceData.ref_resale_cert_expiration.map((expiration) => {
                const expireData = {
                    title: expiration.expiration_display_string,
                    value: expiration.expiration_value
                }
                return expiresData = [...expiresData, expireData];
            })
            setResaleExpiration(expiresData);
            setStates(referenceData.ref_states);
        }
    },[referenceData])

    useEffect(() => {
        if (documentInfo?.length > 0) {
            const resaleCertificates = [resaleCertificatesDefaultValue, resaleCertificatesDefaultValue];

            documentInfo.forEach((data, i) => {
                const obj = {
                    cerificateFileName: data.file_name,
                    cerificateS3Url: data.cerificate_url_s3,
                    stateId: data.state_id,
                    expirationDate: data.expiration_date,
                    status: data.status,
                    id: data.id,
                    isDeletable: data.is_deletable,
                }
                if (i < 2) {
                    resaleCertificates[i] = obj;
                } else {
                    resaleCertificates.push(obj);
                }
            });
            replace(resaleCertificates);
            reset(watch());
        }
    }, [documentInfo])

    useIonViewWillLeave(() => {
        resetDialogStore();
        setIsSaveDisable(true);
        reset();
    }, [])

    const resaleCertFileRef = useRef();
    const resaleCertEditHandler = () => {
        resaleCertFileRef.current.click();
    }

    const uploadCertFile = async (event) => {
        const file = event.target.files[0];

        if (event.target.files.length !== 0) {
            setShowLoader(true);
            setIsSaveDisable(true);
            setUploadCertProgress(true)
            setCertStatus(null)
            setCertId(null)
            setValue("fileName",file.name)
            setUploadCertName(file.name);
            try{
                const certUrl = await uploadBuyerDocumentInfoToS3(file,userData,prefixUrl.resaleCertPrefix,import.meta.env.VITE_S3_UPLOAD_SETTINGS_RESALE_CERT_BUCKET_NAME);
                setUploadCertProgress(false)
                setSignedUrl(certUrl)
                setIsSaveDisable(false);
                showCommonDialog(buyerSettingConst.uploadCertDialogContent,[{name: commomKeys.successBtnTitle, action: resetDialogStore}]);
                // setShowDialogPopup(true);
                // setDialogTitle(commomKeys.uploadSuccessful)
                // setDialogContent(buyerSettingConst.uploadCertDialogContent)
                // setDialogBtnTitle(commomKeys.successBtnTitle)
                // setDialogType(commomKeys.actionStatus.success)
                setShowLoader(false);
            }catch(error) {
                console.error(error);
                signedUrl ? setUploadCertProgress(false) : setUploadCertProgress(null);
                showCommonDialog(commomKeys.errorContent,[{name: commomKeys.errorBtnTitle, action: resetDialogStore}]);
                // setApiFailureDialog(true)
                setIsSaveDisable(false);
                setShowLoader(false);
            }
        }
    }

    const closeSavedChangesConfirmationPopup = () => {
      if (props.documentLibInfo) {
        setShowBackToPoBtn(true);
      } else {
        router.push('/setting', { animate: true, direction: 'forward' });
        resetTheFields(certificateState);
      }
      resetDialogStore();
    };

    const deletePopup = () => {
        showCommonDialog("Delete?",[{name:'Yes', action: deleteCertificate},{name: 'No', action: resetDialogStore}]);
    }

    const deleteCertificate = () => {
        resetDialogStore()
        setShowLoader(true);
        const payload = {
            data: {
                cert_id: CertId,
            },
        };
        axios.post(import.meta.env.VITE_API_SERVICE + `/user/deleteResaleCert`,  payload )
            .then(response => {
                if (response.data.data.error_message){
                    setShowLoader(false);
                    showCommonDialog(response.data.data.error_message,[{name: 'Ok', action: resetDialogStore}]);
                }
                else{
                    console.log('File deleted successfully:', response.data);
                    clearFields()
                    setShowLoader(false);
                    if (props.setOpenDocumentInfo) {
                        handleCloseDocumentPopup()
                    }else{
                        router.push('/setting',{animate:true,direction:'forward'});
                    }
                }
            })
            .catch(error => {
                console.error('Error deleting file:', error);
                setShowLoader(fasle);
            });
    };
    const setDocumentLibraryFields = (resaleCertificate: SalesCertificate)=>{
        reset({
            "fileName":resaleCertificate.file_name,
            'resaleCertExpire': resaleCertificate.expiration_date,
            'resaleCertState': resaleCertificate.state_id,
        })
        setSignedUrl(resaleCertificate.cerificate_url_s3)
        setCertStatus(resaleCertificate.status);
        setCertId(resaleCertificate.id)
        setUploadCertName(resaleCertificate.file_name)
        setUploadCertProgress(false)
    }

    const clearFields = ()=>{
        reset({
            "fileName":null,
            'resaleCertExpire': null,
            'resaleCertState': null,
        })
        setSignedUrl(null)
        setCertStatus(null);
        setCertId(null)
        setUploadCertProgress(null)
    }

    const certificateOnChangeHandler = (e)=>{
        if(isDirty && certificateState.length !== 0){
            showCommonDialog(mobileDiaglogConst.unsavedChangesMsg,[{name: mobileDiaglogConst.continue, action: ()=>{resetTheFields(e.target.value)}},{name: mobileDiaglogConst.stay, action: resetDialogStore}]);
        }else{
            setCertificateState(e.target.value)
            const index = e.target.value - 1;
            if(documentInfo[index]){
                setDocumentLibraryFields(documentInfo[index])
            }else{
                clearFields();
            }
        }
    }

    const resetTheFields = (certificateIndex)=>{
        setCertificateState(certificateIndex);
        const index = certificateIndex-1;
        const document = documentInfo[index];
        if(document){
            setDocumentLibraryFields(document);           
        }else{
            clearFields();
            setUploadCertProgress(null)
        }
        setIsSaveDisable(true);
        resetDialogStore();
    }

    const onSubmit = async(data) => {
        setShowLoader(true);
        setIsSaveDisable(true);

        if (data?.resaleCertificates?.length > 0) {
            let stateCodeText = "";

            let payload = [];

            data.resaleCertificates.forEach(certificate => {
                const state = states.find(state => state.id === certificate.stateId);
                if (state && (certificate.status === 'Pending' || !certificate.status)) {
                    stateCodeText += state.code + ", ";
                }

                if (certificate.cerificateS3Url) {
                    payload.push({
                        cerificate_url_s3: certificate.cerificateS3Url,
                        expiration_date: certificate.expirationDate,
                        file_name: certificate.cerificateFileName,
                        id: certificate.id,
                        state_id: certificate.stateId,
                        status: certificate.status,
                    })
                }
            });

            try {
                const response = (await saveUserSetting(buyerSettingConst.apiRoutesForSave.documentInfo, payload, userData)).data;
                setDocumentLibInfo([...documentInfo]);
                setShowLoader(false);

                if (response?.data?.error_message) {
                    showCommonDialog(response.data.error_message, [{ name: 'Ok', action: resetDialogStore }]);
                    return;
                }

                const message = `Certificate has been submitted for approval. Taxes will no longer be applied for orders delivering to ${stateCodeText} immediately upon Bryzos approval.`;
                showCommonDialog(message, [{ name: commomKeys.successBtnTitle, action: closeSavedChangesConfirmationPopup }]);
                if (props.setUserDetailForPurchase) props.setUserDetailForPurchase()
            } catch (err) {
                console.error(err)
                setShowLoader(false);
            }
        }
    }

      const handleFormSubmit = () => {
        console.log('check errorss', errors)
            handleSubmit(onSubmit)();
    }

    const routeBackToSetting = ()=>{
        router.push('/setting',{animate:true,direction:'forward'});
        resetDialogStore();
    }

    const backToSetting = () => {
        if(props.setOpenDocumentInfo){
            props.setOpenDocumentInfo(false)
        }else{
            if(isDirty)
            showCommonDialog(mobileDiaglogConst.unsavedChangesMsg,[{name: mobileDiaglogConst.stay, action: resetDialogStore},{name: mobileDiaglogConst.goBackToSetting, action: routeBackToSetting}]);
            else
            routeBackToSetting();
        }
    }

    const handleCloseDocumentPopup = () =>{
        props.setOpenDocumentInfo(false)
        props.setUserDetailForPurchase()
    }



    const addStateOnclick = ()=>{
        append(resaleCertificatesDefaultValue) ;
        setScrollToBottom(true);
    }

    useEffect(() => {
        if(scrollToBottom){
            if(scrollStateRef.current){
                scrollStateRef.current.scrollTop = scrollStateRef.current.scrollHeight;
            }
            setScrollToBottom(false);
        }
    },[scrollToBottom]);

    return(
            <IonContent>
                <div className={styles.profile}>
                    <h2 className={styles.heading}><BackBtnArrowIcon onClick={backToSetting}/><span>Documents Library</span> {props.documentLibInfo && <ClosePopTwo onClick={backToSetting}/>}</h2>
                        <span className={styles.resaleCertheading}>Resale Certificates</span>
                    <div className={styles.settingsContent} ref={scrollStateRef}>
                        {fields?.map((field, index) =>
                            <ResaleCertificate
                                key={field.id} trigger={trigger}
                                getValues={getValues}
                                control={control}
                                register={register}
                                errors={errors}
                                states={states}
                                ResaleExpiration={ResaleExpiration}
                                setValue={setValue}
                                watch={watch}
                                index={index}
                                remove={remove}
                                isDirty={isDirty}
                                documentInfo={field}
                                setOpenDocumentInfo={props.setOpenDocumentInfo}
                                handleCloseDocumentPopup={handleCloseDocumentPopup}
                            />)}
                    </div>
                     <div className={styles.addStateSection}>
                        <button className={styles.addStateBtn} onClick={addStateOnclick}><AddStateIcon/>Add State</button>
                     </div>

                    <div className={styles.btnSection}>
                        {showBackToPoBtn && <a className={styles.backToPoBtn} onClick={backToSetting}> <IconLeftArrow /> Back to PO </a>}
                        <button onClick={() => handleFormSubmit()}  disabled={(!isDirty || !isValid)}>Save</button>
                    </div>
                </div>
            </IonContent>
    )
}
 export default DocumentInfo;