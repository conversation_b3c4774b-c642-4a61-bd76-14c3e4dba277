// @ts-nocheck
import clsx from 'clsx';
import styles from './OrderPreview.module.scss';
import { useEffect, useState } from 'react';
import axios from 'axios';
import { IonContent, IonPage, useIonLoading, useIonRouter, useIonViewWillEnter, } from '@ionic/react';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import dayjs from 'dayjs';
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import { format4DigitAmount, formatCurrencyWithComma, formatToTwoDecimalPlaces } from '../../../../library/helper';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import moment from 'moment';
import 'moment-timezone';
dayjs.extend(utc);
dayjs.extend(timezone);
const OrderPreview = () => {
  const {
    userData,
    setShowLoader , referenceData
  }: any = useGlobalStore();
  const router = useIonRouter();
  const [ordersListPreview, setOrdersListPreview] = useState([]);
  const [poIndex, setPoIndex] = useState(router.routeInfo?.routeOptions?.poIndex);
  const ordersList = router.routeInfo?.routeOptions?.ordersList
  let stateRef: any = [];
  if (referenceData) stateRef = referenceData.ref_states;
  const startIndex = 0;
  const lastIndex = ordersList?.length - 1;
  const disablePreviousButton = startIndex === poIndex;
  const disableNextButton = lastIndex === poIndex;
  const state = stateRef?.find((stateDetail: any) => stateDetail.id == ordersListPreview?.state_id)?.code;

    useEffect(() => {
        if (ordersList)
            getSpecificOrderData(ordersList[poIndex].seller_po_number);
    }, [poIndex, ordersList])

    function display(data: string) {
        const lines = data.split('\n');
        const firstLine = lines[0];
        const restLines = lines.slice(1);

        return (
            <div>
                <p className="liHead">{firstLine}</p>
                {restLines.map((line, index) => (
                    <p key={index}>{line}</p>
                ))}
            </div>
        );
    }
    const productListing = ordersListPreview?.items?.map((product: any, index: any) => {
        return (<tr key={index}>
            <td>{index + 1}</td>
            <td >
                <span className={styles.description}>
                    {display(product.description)}
                </span>
                {/* <div className={styles.stripLoad}>
                Strip Load / Include MTRs
            </div> */}
                {(product.domestic_material_only > 0) && <div className={styles.domesticMaterialLbl}>
                    {domesticMaterialText}
                </div>}
            </td>
            <td className={styles.qtyTd}>
                <span>{formatCurrencyWithComma(product.qty)} {product.qty_unit} <br />
                    {product.price_unit === "Lb" ? format4DigitAmount(product.seller_price_per_unit) : formatToTwoDecimalPlaces(product.seller_price_per_unit)}</span> / <span>{product.price_unit}</span>
            </td>
            <td className={styles.lastTd}><div className={styles.div1}>$ {formatToTwoDecimalPlaces(product.seller_line_total)}</div></td>
        </tr>);
    })
    const routeBackToSetting = () => {
        router.push('/your-order-listing', { animate: true, direction: 'forward' });
    }

    const previousPage = () => {
        if (poIndex > startIndex) {
            setPoIndex((state: any) => state - 1);
        }
    }
    const nextPage = () => {
        if (poIndex < lastIndex) {
            setPoIndex((state: any) => state + 1);
        }
    }

    const getSpecificOrderData = (poNumber) => {
        setShowLoader(true);
        axios.get(import.meta.env.VITE_API_ORDER_SERVICE + `/seller/getPoDetails/${poNumber}`).then((response) => {
            response.data.data.delivery_date = moment.utc(response.data.data.delivery_date).tz('America/Chicago').format('M/DD/YY');
            // response.data.data.delivery_date = moment.utc(response.data.data.delivery_date).tz('America/Chicago').format('MMM DD, YYYY');
            setOrdersListPreview(response.data.data);
            setShowLoader(false);
        })
            .catch((error) => {
                console.error(error);
                setShowLoader(false);
                // setApiFailureDialog(true)
            });
    }

    return (
        <IonPage>
            <IonContent>
                <>
                    <div className={styles.yourOrderPreview}>
                        <div className={styles.orderBuyerPo}><div className={styles.heading}><BackBtnArrowIcon onClick={routeBackToSetting} /><span>Order# {ordersListPreview.seller_po_number}</span></div></div>
                        <div className={styles.poAndDateDetails}>
                            <div>Job / PO #:  <span>{ordersListPreview.internal_po_number}</span> ({ordersListPreview.seller_po_number})</div>
                            {/* <div>Delivery Date:  <span>{dayjs.utc(ordersListPreview.delivery_date).format('M/DD/YY')}</span></div> */}
                            <div>Delivery Date:  <span>{ordersListPreview.delivery_date}</span></div>
                            <div>Delivery Destination:  <span>{ordersListPreview?.city}, {state} {ordersListPreview?.zip}</span></div>
                        </div>
                        <div className={styles.addPoLineTable}>
                            <table className={styles.tableGrid}>
                                <thead>
                                    <tr>
                                        <th><span>LN</span></th>
                                        <th><span>DESCRIPTION</span></th>
                                        <th><span>QTY @ $/UM</span></th>
                                        <th colSpan={2} className={styles.lastTd}><span>EXT</span></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {productListing}
                                </tbody>

                            </table>
                        </div>

                        <table className={styles.totalAmt}>
                            <tr>
                                <td><div><span></span></div></td>
                                <td className={styles.materialTotal}><div><span>Material Total</span></div></td>
                                <td className={styles.materialTotal}><div><span>$</span></div></td>
                                <td className={styles.materialTotal1}><div><span> {formatToTwoDecimalPlaces(ordersListPreview?.seller_po_price)}</span></div></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td><span className={styles.saleTax}>Sales Tax</span></td>
                                <td><span className={styles.txtSign}>$</span></td>
                                <td><span className={styles.txtNumber}>{formatToTwoDecimalPlaces('0')}</span></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td><span className={styles.saleTax}>Freight Charge</span></td>
                                <td><span className={styles.txtSign}>$</span></td>
                                <td><span className={styles.txtNumber}>{formatToTwoDecimalPlaces('0')}</span></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td><span className={styles.saleTax}>Expedited Delivery Fee</span></td>
                                <td><span className={styles.txtSign}>$</span></td>
                                <td><span className={styles.txtNumber}>{formatToTwoDecimalPlaces('0')}</span></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td><span className={styles.totalPurchase}>Total Purchase</span></td>
                                <td><span className={styles.totalPurchaseSign}>$</span></td>
                                <td><span className={styles.totalPurchaseNumber}>{formatToTwoDecimalPlaces(ordersListPreview.seller_po_price)}</span></td>
                            </tr>
                        </table>
                        <div className={styles.acceptOrderHead}>
                            <button className={clsx(styles.previewNextPreBtn, styles.btnPreNextPo, disablePreviousButton && styles.nextprevdisabledBtn)} disabled={disablePreviousButton} onClick={previousPage}><span>Prev PO</span></button>
                            <button className={clsx(styles.previewNextPreBtn, styles.btnPreNextPo, disableNextButton && styles.nextprevdisabledBtn)} disabled={disableNextButton} onClick={nextPage} ><span>Next PO</span></button>
                        </div>
                    </div>
                </>
            </IonContent>
        </IonPage>
    );
};
export default OrderPreview;
