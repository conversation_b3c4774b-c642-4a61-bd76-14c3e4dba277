// @ts-nocheck
import { <PERSON><PERSON><PERSON>, IonContent, useIonRouter, useIonViewWillEnter, useIonViewWillLeave, useIonLoading } from '@ionic/react';
import styles from './Setting.module.scss';
import { ReactComponent as ProfileIcon } from '../../../../assets/mobile-images/Profile.svg';
import { ReactComponent as ChangePasswordIcon } from '../../../../assets/mobile-images/password.svg';
import { ReactComponent as CompanyInfoIcon } from '../../../../assets/mobile-images/Company_Info.svg';
import { ReactComponent as DeliverToIcon } from '../../../../assets/mobile-images/Deliver_To.svg';
import { ReactComponent as ReceivingHoursIcon } from '../../../../assets/mobile-images/Receiving_Hours.svg';
import { ReactComponent as PaymentSettingsIcon } from '../../../../assets/mobile-images/Payment_Settings.svg';
import { ReactComponent as DocumentsLibraryIcon } from '../../../../assets/mobile-images/Documents_Library.svg';
import { ReactComponent as MyReportsIcon } from '../../../../assets/mobile-images/MyReports.svg';
import { ReactComponent as LogoutIcon } from '../../../../assets/mobile-images/Logout.svg';
import { ReactComponent as SettingsRightArrowIcon } from '../../../../assets/mobile-images/SettingsRightArrow.svg';
import { ReactComponent as WarningIcon } from '../../../../assets/mobile-images/Warning.svg';
import { useEffect, useState } from 'react';
import useBuyerSettingStore, { BNPLModel, BuyerSetting, Payment, Profile, ReceivingHours, SalesCertificate } from './BuyerSettingStore';
import axios from 'axios';
import clsx from 'clsx';
import { useGlobalStore, getSocketConnection , useAuthStore } from '@bryzos/giss-ui-library';

const Setting = (props: any) => {
    const router = useIonRouter()
    const { setProfileInfo, setCompanyInfo, setDeliveryInfo, setPaymentInfo, setDocumentLibInfo, setReceivingHoursInfo, setBuyerSettingInfo , resetBuyerSetting} = useBuyerSettingStore();
    const [isPaymentDisabled, setIsPaymentDisabled] = useState(false);
    const setShowLoader = useGlobalStore(state => state.setShowLoader);
    const socket = getSocketConnection();
    const {setTriggerLogout, initiateLogout} = useAuthStore()

    useIonViewWillEnter(() => {
        setShowLoader(true)
        axios.get(import.meta.env.VITE_API_SERVICE + '/user/buyingPreference')
            .then(response => {
                setBuyerSettingData(response.data.data); 
                setShowLoader(false);
            })
            .catch(error => {
                console.error(error);
                setShowLoader(false);
                // setApiFailureDialog(true)
            }
        );
    }, [])

    useIonViewWillLeave(()=>{
        setIsPaymentDisabled(false);
    })

    const setBuyerSettingData = (buyerSetting: BuyerSetting) => {
        if (buyerSetting) {
            const profile: Profile = {
                email_id: buyerSetting.email_id,
                first_name: buyerSetting.first_name,
                last_name: buyerSetting.last_name,
                phone: buyerSetting.phone,
            };
            for(const key in profile){
                if(profile[key] === null && !isPaymentDisabled)setIsPaymentDisabled(true);
            }
            setProfileInfo(profile);
            const companyDetails: CompanyDetails = { 
                company_name: buyerSetting.company_name, 
                client_company: buyerSetting.client_company, 
                company_address_line1: buyerSetting.company_address_line1, 
                company_address_city: buyerSetting.company_address_city,
                company_address_state_id: buyerSetting.company_address_state_id,
                company_address_zip: buyerSetting.company_address_zip
            }
            for(const key in companyDetails){
                if(companyDetails[key] === null && !isPaymentDisabled)setIsPaymentDisabled(true);
            }
            setCompanyInfo(companyDetails);
            const deliveryDetails: DeliveryDetails =  { 
                delivery_address_line1: buyerSetting.delivery_address_line1, 
                delivery_address_city: buyerSetting.delivery_address_city, 
                delivery_address_state_id: buyerSetting.delivery_address_state_id, 
                delivery_address_zip: buyerSetting.delivery_address_zip,
                delivery_days_add_value: buyerSetting.delivery_days_add_value, 
                send_invoices_to: buyerSetting.send_invoices_to, 
                shipping_docs_to: buyerSetting.shipping_docs_to
            }
            setDeliveryInfo(deliveryDetails);
            const receivingHoursList : ReceivingHours[] = buyerSetting.user_delivery_receiving_availability_details;
            setReceivingHoursInfo(receivingHoursList);
            const bnpl: BNPLModel = buyerSetting.bnpl_settings ??  null;
            const ach: BNPLModel = buyerSetting.ach_credit ?? null;
            const paymentInfo : Payment = bnpl || ach ? {bnpl, ach} : null;
            paymentInfo.default_payment_method = buyerSetting.default_payment_method;
            setPaymentInfo(paymentInfo);
            const salesCertificates: SalesCertificate[] = buyerSetting.resale_certificate;
            setDocumentLibInfo(salesCertificates);
            setBuyerSettingInfo(buyerSetting);
        }
    }


    
    return (
        <IonPage>
            <IonContent>
                <div className={styles.settingsPageMain}>
                    <h2 className={styles.heading}>Setting </h2>
                    <div className={styles.containDiv}>
                        {isPaymentDisabled &&
                                <div className={clsx(styles.snackbarContainer,styles.show)} >
                                    <span className='dflex'><WarningIcon /></span>
                                    <div className={styles.errorTest}>
                                        <div className={styles.errorSms}>Payment settings disabled</div>
                                        <div className={styles.content}>To enter payment information, please complete your profile and company information.</div>
                                    </div>
                                </div>
                        }
                        <div className={styles.settingsPageBtn}>
                            <button onClick={()=> router.push('/profile',{animate:true,direction:'forward'})}><ProfileIcon/>Profile<SettingsRightArrowIcon className={styles.rightIcon}/></button>
                        </div>
                        <div className={styles.settingsPageBtn}>
                            <button onClick={()=> router.push('/change-password',{animate:true,direction:'forward'})}><ChangePasswordIcon/>Change Password<SettingsRightArrowIcon className={styles.rightIcon}/></button>
                        </div>
                        <div className={styles.settingsPageBtn}>
                            <button onClick={()=> router.push('/company-information',{animate:true,direction:'forward'})}><CompanyInfoIcon/>Company Information<SettingsRightArrowIcon className={styles.rightIcon}/></button>
                        </div>
                        <div className={styles.settingsPageBtn}>
                            <button onClick={()=> router.push('/delivery-detail',{animate:true,direction:'forward'})}><DeliverToIcon/>Deliver To<SettingsRightArrowIcon className={styles.rightIcon}/></button>
                        </div>
                        <div className={styles.settingsPageBtn}>
                            <button onClick={()=> router.push('/receiving-hours',{animate:true,direction:'forward'})}><ReceivingHoursIcon/>Receiving Hours<SettingsRightArrowIcon className={styles.rightIcon}/></button>
                        </div>
                        <div className={clsx(styles.settingsPageBtn, isPaymentDisabled ? styles.disabledBtn : '')}>
                            <button onClick={()=> router.push('/payment',{animate:true,direction:'forward'})} disabled={isPaymentDisabled}><PaymentSettingsIcon/>Payment Settings<SettingsRightArrowIcon className={styles.rightIcon}/></button>
                        </div >
                        <div className={styles.settingsPageBtn}>
                            <button onClick={()=> router.push('/document-info',{animate:true,direction:'forward'})}><DocumentsLibraryIcon/>Documents Library<SettingsRightArrowIcon className={styles.rightIcon}/></button>
                        </div>
                        <div className={styles.settingsPageBtn}>
                            <button onClick={()=> router.push('/my-reports',{animate:true,direction:'forward'})}><MyReportsIcon/>My Reports<SettingsRightArrowIcon className={styles.rightIcon}/></button>
                        </div>
                        <div className={styles.settingsPageBtn}>
                            <button onClick={() => initiateLogout()}><LogoutIcon/>Logout<SettingsRightArrowIcon className={styles.rightIcon}/></button>
                        </div>
                        <div className={styles.appVersion}>
                            <span className={clsx(socket?.connected && styles.active)}>v{import.meta.env.VITE_REACT_APP_VERSION}</span>
                        </div>
                    </div>
                </div>
            </IonContent>
        </IonPage>
    )

}
export default Setting;