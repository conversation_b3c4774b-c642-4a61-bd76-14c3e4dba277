// @ts-nocheck
import React, { useEffect, useState, useContext, useRef } from 'react';
import {UserContext} from '../UserContext';
import axios from 'axios';
import { ReactComponent as ResetIcon } from '../assets/images/icon-reset.svg';
import { ReactComponent as ResetIconHover } from '../assets/images/icon-reset-hover.svg';
import { ReactComponent as ShareIcon } from '../assets/images/icon-share.svg';
import { ReactComponent as ShareIconHover } from '../assets/images/icon-share-hover.svg';
// import { ReactComponent as ComeSoonArrow } from '../assets/images/come-soon-arrow.svg';
import { ReactComponent as DownWhiteArrow } from '../assets/images/downArrowSelected.svg';
import { ReactComponent as DownWhiteDArrow } from '../assets/images/downArrow.svg';
import { ReactComponent as UpWhiteArrow } from '../assets/images/upArrowSelected.svg';
import { ReactComponent as UpWhiteDArrow } from '../assets/images/upArrow.svg';
import { ReactComponent as CheckIcon } from '../assets/images/checkIcon.svg';
import { ReactComponent as CheckDIcon } from '../assets/images/unCheck.svg';
import { ReactComponent as DropdownIcon } from '../assets/images/icon_Triangle.svg';
import { ReactComponent as TncCloseIcon } from '../assets/images/tnc-close.svg';
import { ReactComponent as TncCloseHoverIcon } from '../assets/images/tnc-close-hover.svg';
import { ReactComponent as QuestionIcon } from '../assets/images/setting-question.svg';
import { ReactComponent as QuestionHoverIcon } from '../assets/images/question-white-hover.svg';
import {
    userRole,
    keyUpCode,
    keyDownCode,
    keyEnterCode,
    routes, 
    MinSearchDataLen,
    commomKeys, 
} from '../../common';

import { Select, MenuItem, Tooltip } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import Fade from '@mui/material/Fade'; 
import { priceFeedBakTooltip, priceUnitChangeTooltip, resetBtnTooltip, shareListTooltip, shareWidgetTooltip } from '../tooltip';
import { useHeightListener } from '../hooks/useHeightListener';
import { useStore } from '../helper/store';
import {searchProducts, getValidSearchData} from '../helper/commonFunctionality';
import { get2DigitFormater, get4DigitFormater } from '../helper';
import { CommonTooltip } from '../component/Tooltip/tooltip';
import { useDebouncedValue } from '@mantine/hooks';
import { v4 as uuidv4 } from 'uuid';
import DialogPopup from '../component/DialogPopup/DialogPopup';

const Home = () => {
    const userContext = useContext(UserContext);
    const user = userContext.user;
    const navigate = useNavigate();
    const [searchData, setSearchData] = useState('');
    const [products, setProducts] = useState('');
    const [feedbackData, setFeedBackData] = useState({});
    const initialSelectedProducts = user.selectedProducts ? user.selectedProducts : [];
    const [selectedProducts, setSelectedProducts] = useState(initialSelectedProducts);
    const [showSearchPanel, setShowSearchPanel] = useState(true);
    const [showWidgetPanel, setShowWidgetPanel] = useState(false);
    const [singleProduct, setSingleProduct] = useState([]);
    const [shareProductsLength, setShareProductsLength] = useState(0);
    const [focusSingleProduct, setFocusSingleProduct] = useState({});
    const [showListPanel, setShowListPanel] = useState(false);
    const [showTncPanel, setShowTncPanel] = useState(false);
    const [selectedOption, setSelectedOption] = useState('cwt');
    const [isUserChangedSelectedOption, setIsUserChangedSelectedOption] = useState(false);
    const [emailData, setEmailData] = useState('');
    const [emailError, setEmailError] = useState(' ');
    const [textarea, setTextarea] = useState('');
    const [selectedIndex, setSelectedIndex] = useState(-1);
    const [isSendInProgress, setIsSendInProgress]= useState(false);
    const [enableRejectSearchAnalytic, setEnableRejectSearchAnalytic]= useState(true);
    const [sessionId, setSessionId] = useState('');
    //Dialog Popup 
    const [showDialogPopup, setShowDialogPopup] = useState(false);
    const [dialogTitle, setDialogTitle] = useState('');
    const [dialogContent, setDialogContent] = useState('');
    const [dialogBtnTitle, setDialogBtnTitle] = useState('');
    const [dialogType, setDialogType] = useState('');
    //
    const resetFiltersInPurchaseOrders = useStore( state => state.resetFiltersInPurchaseOrders );
    const enableShareWidget = useStore(state => state.enableShareWidget);
    const setEnableShareWidget = useStore(state => state.setEnableShareWidget);
    
    const selectedProductRef = useRef(null);
    const searchProductInputRef = useRef(null);
    const containerRef = useRef(null);
    const analyticRef = useRef();


    const ref = useHeightListener();
    const showAlertForNewPo = useStore(state => state.showAlertForNewPo);
    const setShowLoader = useStore(state => state.setShowLoader);
    const setSearchSessionId = useStore(state => state.setSearchSessionId);
    const searchSessionId = useStore(state => state.searchSessionId);
    const [debouncedSearchData, cancelDebouncedSearchData] = useDebouncedValue(searchData, 400);
    analyticRef.current = sessionId;

    useEffect(()=>{
        resetFiltersInPurchaseOrders();
        setShowLoader(false);
        if(searchSessionId){
            setSessionId(searchSessionId)
        }else{
            const sessionId = uuidv4();
            setSessionId(sessionId);
        }
        
        return ()=>{
            setEnableShareWidget(false);
            const dataOpticsPayload = {
                "data": {
                    "session_id": analyticRef.current,
                    "move_to_screen": location.pathname.replace('/',"")
                }
            }
            dataOpticsApi2(dataOpticsPayload)
        }
    },[]);

    useEffect(()=>{
        if(enableShareWidget){
            handleOpenWidget();
        }
        else{
            handleCloseWidget();
        }
    },[enableShareWidget])

    useEffect(() => {
      if (user.allProductsData) {
        setProducts(user.allProductsData);
        let _selectedOption = "";
        if (!isUserChangedSelectedOption) {
          let isAnyPipeProduct = false;
          let isAnyNonPipeProduct = false;

          selectedProducts?.forEach((_obj) => {
            const currentProduct = user.allProductsData?.find(
              (_product) => _product.Product_ID === _obj.id
            );
            if (currentProduct?.Key2 === "Pipe") {
              isAnyPipeProduct = true;
            } else {
              isAnyNonPipeProduct = true;
            }
          });

          if (isAnyNonPipeProduct && isAnyPipeProduct) {
            _selectedOption = "cwt,ft";
          } else if (isAnyNonPipeProduct) {
            _selectedOption = "cwt";
          } else if (isAnyPipeProduct) {
            _selectedOption = "ft";
          }
          setSelectedOption(_selectedOption);
        }
      }
    }, [user?.allProductsData]);

    useEffect(() => {
        if (containerRef.current && selectedProductRef.current) {
            const container = containerRef.current;
            const selectedItem = selectedProductRef.current;
            const itemTop = selectedItem.offsetTop;
            const itemHeight = selectedItem.offsetHeight;
            const containerHeight = container.offsetHeight;
            const scrollOffset = container.scrollTop;
            if (itemTop < scrollOffset) {
                container.scrollTop = itemTop - 90;
            } else if (itemTop + itemHeight > scrollOffset + containerHeight) {
                container.scrollTop = itemTop + itemHeight - containerHeight - 60;
            }
        }

    }, [selectedIndex]);

    useEffect(() => {
        if(debouncedSearchData?.length >= MinSearchDataLen ){
            searchAnalyticsApi(sessionId, null, debouncedSearchData)
        }
        
    },[debouncedSearchData])

    useEffect(() => {
        const searchProductList = searchProducts(products, getValidSearchData(searchData), searchData);
        if(searchData.length === 1 ){
            setEnableRejectSearchAnalytic(true)
        }
        if(searchData.length === 0 && searchProductList.length === 0 && debouncedSearchData && enableRejectSearchAnalytic){
            searchAnalyticsApi(sessionId, 'Reject', debouncedSearchData)
        }
    },[searchData])

    useEffect(() => {
        if(sessionId){
            setSearchSessionId(sessionId);
        }
    }, [sessionId])

    const navigateToOrderClaim = () => {
        if(showAlertForNewPo)
        navigate(routes.orderPage);
    }

    const selectProduct = (product) => {
        setEnableRejectSearchAnalytic(false)
        searchAnalyticsApi(sessionId, 'Accept', debouncedSearchData)
        const isDuplicate = selectedProducts.some((data) => data.id === product.Product_ID);
        if (!isDuplicate) {
            let productWithPrice = {};
            const lineSessionId = uuidv4();
            if (user.data.type === userRole.buyerUser) {
                const cwt_price = product.Buyer_Pricing_CWT;
                const lb_price = product.Buyer_Pricing_LB;
                const ft_price = product.Buyer_Pricing_Ft;
                productWithPrice = {
                    "id": product.Product_ID,
                    "UI_Description": product.UI_Description,
                    "cwt_price": cwt_price,
                    "ft_price": ft_price,
                    "lb_price": lb_price,
                    "product_type_pipe": product.Key2 === "Pipe" ?? false,
                    "line_session_id": lineSessionId,
                }
            } else if (user.data.type === userRole.sellerUser) {
                const cwt_price = product.Seller_Pricing_CWT;
                const lb_price = product.Seller_Pricing_LB;
                const ft_price = product.Seller_Pricing_Ft;
                productWithPrice = {
                    "id": product.Product_ID,
                    "UI_Description": product.UI_Description,
                    "cwt_price": cwt_price,
                    "ft_price": ft_price,
                    "lb_price": lb_price,
                    "product_type_pipe": product.Key2 === "Pipe" ?? false,
                    "line_session_id": lineSessionId,
                }
            } else if (user.data.type === userRole.neutralUser) {
                const cwt_price = product.Neutral_Pricing_CWT;
                const lb_price = product.Neutral_Pricing_LB;
                const ft_price = product.Neutral_Pricing_Ft;
                productWithPrice = {
                    "id": product.Product_ID,
                    "UI_Description": product.UI_Description,
                    "cwt_price": cwt_price,
                    "ft_price": ft_price,
                    "lb_price": lb_price,
                    "product_type_pipe": product.Key2 === "Pipe" ?? false,
                    "line_session_id": lineSessionId,
                }
            }
            setSelectedProducts(prevSelectedProducts => [productWithPrice, ...prevSelectedProducts]);
            let storeInUserState = [productWithPrice, ...initialSelectedProducts];
            userContext.setUser({...user,selectedProducts: storeInUserState});

            setSelectedProducts((prevSelectedProducts) => {
                let _selectedOption = null;
                if (!isUserChangedSelectedOption) {
                    let isAnyPipeProduct = false;
                    let isAnyNonPipeProduct = false;
    
                    prevSelectedProducts.forEach((_obj) => {
                        const currentProduct = products?.find(
                            (_product) => _product.Product_ID === _obj.id
                        );
                        if (currentProduct?.Key2 === "Pipe") {
                            isAnyPipeProduct = true;
                        } else {
                            isAnyNonPipeProduct = true;
                        }
                    });
    
                    if (isAnyNonPipeProduct && isAnyPipeProduct) {
                        _selectedOption = "cwt,ft";
                    } else if (isAnyNonPipeProduct) {
                        _selectedOption = "cwt";
                    } else if (isAnyPipeProduct) {
                        _selectedOption = "ft";
                    }
                    setSelectedOption(_selectedOption);
                }
                prevSelectedProducts.map((productData) => {
                    productData.product_share_type = _selectedOption;
                    if(productData.id === product.Product_ID){
                        const dataOpticsPayload = {
                            "data": [{
                                "session_id": sessionId,
                                "line_session_id": productData.line_session_id,
                                "product_id": productData.id,
                                "description": productData.UI_Description,
                                "price_shared": false,
                                "price_share_unit": productData.product_share_type !== _selectedOption ? _selectedOption : productData.product_type_pipe ? "ft" : "cwt",
                                "search_price_unit": productData.product_share_type !== _selectedOption ? _selectedOption : productData.product_type_pipe ? "ft" : "cwt"
                            }]
                        }
                        dataOpticsApi1(dataOpticsPayload)
                    }
                })
                return prevSelectedProducts;
            });
        } 
        setSearchData('');
        searchProductInputRef.current.focus();

    }
    const handlePriceRatingChange = (event, productId) => {
        let priceFeedbackType = null;
        if (isUserChangedSelectedOption) {
            priceFeedbackType = selectedOption;
        } else {
            priceFeedbackType = productId.product_type_pipe ? "ft" : "cwt"
        }
        let feedBack = {
            data: {
                "user_id": user.data.id,
                "product_id": productId.id,
                "product_description": productId.UI_Description,
                "feedback": event.target.value,
                "prices": {
                    "price_ft": productId.ft_price.trim().replace("$", ""),
                    "price_lb": productId.lb_price.trim().replace("$", ""),
                    "price_cwt": productId.cwt_price.trim().replace("$", "")
                },
                "price_feedback_type": priceFeedbackType,
                "user_type": user.data.type
            }
        }
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/savePricingFeedback', feedBack)
            .then(response => {
                if (response.data.data === "Feedback noted") {

                    setFeedBackData({ ...feedbackData, [productId.id]: event.target.value });

                }
            })
            .catch(error => {
                setShowDialogPopup(true)
                setDialogType();
                setDialogTitle();
                setDialogContent(commomKeys.errorContent)
                setDialogBtnTitle(commomKeys.errorBtnTitle)
            });
    };
    const handleOpenWidget = () => {
        setSingleProduct([]);
        setFocusSingleProduct({});
        setShowSearchPanel(false);
        setEmailError('')
        setShowWidgetPanel(true);
    };
    const handleCloseWidget = () => {
        setTextarea('')
        setEmailData('')
        setSingleProduct([]);
        setFocusSingleProduct({});
        setShowSearchPanel(true);
        setShowWidgetPanel(false);
        setEnableShareWidget(false);
    };
    const handleOpenTnc = () => {
        setShowTncPanel(true);
        setShowListPanel(true);
    };
    const handleCloseTnc = () => {
        setShowTncPanel(false);
        setShowListPanel(false);
    };
    const handleValidation = (event) => {
        setEmailData(event.target.value);
        if(event.target.value.length !== 0){
        validateEmail(event.target.value);
        }
        

    }
    const validateEmail = (email) => {

        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(email)) {
            setEmailError('Invalid Email Format');
        } else {
            const suffixes = [".com", ".org", ".net", ".edu", ".co", ".co.in", ".site", ".in"];

            for (const suffix of suffixes) {
                if (!email.endsWith(suffix)) {
                    setEmailError('Please enter valid email address');


                } else {
                    setEmailError('');
                    return;
                }
            }
        }

    };
    const handleSelectSingleProduct = (singleProducts) => {
        const isDuplicate = singleProduct.some((data) => data.id === singleProducts.id);
        if (isDuplicate) {
            delete focusSingleProduct[singleProducts.id];
            const removeSingleProduct = singleProduct.filter((data) => data.id !== singleProducts.id)

            setSingleProduct(removeSingleProduct);
        } else {
            setSingleProduct(prevSingleSelectedProducts => [...prevSingleSelectedProducts, singleProducts]);
            setFocusSingleProduct({ ...focusSingleProduct, [singleProducts.id]: true });
        }
    }
     const handleSharePrice = (event) => {
        setShowSearchPanel(false);
        setShowWidgetPanel(true);
        setEmailError('')
        if (singleProduct.length === 0) {
            setSingleProduct(selectedProducts)
            setShareProductsLength(selectedProducts.length === 1 ? `${selectedProducts.length} Price` : `${selectedProducts.length} Prices`)
        }else{
            setShareProductsLength(singleProduct.length === 1 ? `${singleProduct.length} Price` : `${singleProduct.length} Prices` )
        }
    }
    const handleSubmitData = (event) => {
        if (emailData === '') {
            setEmailError('Please enter email address')

        } else if (emailData !== '' && emailError === '') {
            
            setIsSendInProgress(true);
            if (singleProduct.length !== 0) {
                const productList = singleProduct.map((product) => {
                    return {
                        "product_id": product.id,
                        "product_description": product.UI_Description,
                        "price_ft": product.ft_price.trim().replace("$", ""),
                        "price_lb": product.lb_price.trim().replace("$", ""),
                        "price_cwt": product.cwt_price.trim().replace("$", ""),
                        "price_share_type": product.product_share_type !== selectedOption ? selectedOption : product.product_type_pipe ? "ft" : "cwt",
                    }
                })
                const dataOpticsPayload = {
                    "data": singleProduct.map((product) => {
                        return {
                            "session_id": sessionId,
                            "line_session_id": product.line_session_id,
                            "product_id": product.id,
                            "description": product.UI_Description,
                            "price_shared": true,
                            "search_price_unit": product.product_share_type !== selectedOption ? selectedOption : product.product_type_pipe ? "ft" : "cwt",
                        }
                    })
                }
                dataOpticsApi1(dataOpticsPayload)
                const payload = {
                    data: {
                        "user_id": user.data.id,
                        "from_email": user.data.email_id,
                        "to_email": emailData,
                        "email_content": textarea.trim().length === 0 ? null : textarea.trim(),
                        "products": productList,
                    }
                }
                axios.post(import.meta.env.VITE_API_SERVICE + '/user/shareProductPrice', payload)
                    .then(response => {
                        setTextarea('')
                        setEmailData('')
                        setSingleProduct([])
                        navigate(routes.successPage)
                        setFocusSingleProduct({});

                    })
                    .catch(error => {
                        
                setIsSendInProgress(false);
                        console.error(error)});
            } else {

                const payload = {
                    data: {
                        "user_id": user.data.id,
                        "from_email": user.data.email_id,
                        "to_email": emailData,
                        "email_content": textarea.trim().length === 0 ? null : textarea.trim()
                    }
                }
                axios.post(import.meta.env.VITE_API_SERVICE + '/user/shareWidgetRequest', payload)
                    .then(response => {
                        setTextarea('')
                        setEmailData('')
                        navigate(routes.successPage)
                    })
                    .catch(error => {
                        
                setIsSendInProgress(false);
                        console.error(error)});
            }
        }
        else {
        }
    }

    const searchAnalyticsApi = (sessionId, status, searchKeyword) => {
        const payload = {
            "data": {
                "session_id": sessionId,
                "keyword" : searchKeyword,
                "status" : status,
                "source" : 'search'
                
            }
          }
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/create_po_search', payload)
        .catch(err => console.error(err))
    }
    const handleResetData = () => {
        setSearchData('');
        setShowListPanel(true);
        setSelectedProducts([]);
        userContext.setUser({...user,selectedProducts: []});
        setFeedBackData({});
        setTextarea('');
        setSingleProduct([])
        setEmailData('')
        setFocusSingleProduct(false);
        setIsUserChangedSelectedOption(false);
        const newSessionId = uuidv4();
        setSessionId(newSessionId)
    }
    function display(data) {
        const lines = data.UI_Description.split('\n');
        const firstLine = lines[0];
        const restLines = lines.slice(1);

        return (
            <div>
                <p className="liHead">{firstLine}</p>
                {restLines.map((line, index) => (
                    <p key={index}>{line}</p>
                ))}
            </div>
        );
    }

    const handleSearchKeyDown = (event) => {
        const startIndex = 0;
        const endIndex = searchProducts(products, getValidSearchData(searchData), searchData).length - 1;
        if (event.keyCode === keyUpCode && selectedIndex > startIndex) {

            setSelectedIndex(selectedIndex - 1)


        } else if (event.keyCode === keyDownCode && selectedIndex < endIndex) {
            setSelectedIndex(selectedIndex + 1)

        }else if(event.keyCode === keyEnterCode && selectedIndex >= startIndex){
            setSelectedIndex(-1);
            const selectedProductId = selectedProductRef.current.getAttribute('value')
            selectProduct(products[selectedProductId - 1]);
        }
    }

    const showSelectedPrize = (product) => {
        const decimal2Formater = get2DigitFormater();
        const decimal4Formater = get4DigitFormater();

        let price = 0;
        if (!isUserChangedSelectedOption) {
            if (product.product_type_pipe && product.product_share_type === selectedOption) {
                price = +product.ft_price.replace("$", "");
                return decimal2Formater.format(price ? price : 0).replace("$", "");
            } else {
                price = +product.cwt_price.replace("$", "");
                return decimal2Formater.format(price).replace("$", "");
            }
        } else {
            price = +product[`${selectedOption}_price`].replace("$", "");
            if (selectedOption === "lb") {
                return decimal4Formater.format(price ? price : 0).replace("$", "");
            } else {
                return decimal2Formater.format(price ? price : 0).replace("$", "");
            }
        }
    };

    const unitDropdownChangeHandler = (e: any) => {
        setSelectedOption(e.target.value); 
        setIsUserChangedSelectedOption(true);
        const dataOpticsPayload = {
            "data": selectedProducts.map((selectedProduct: any)=>{
                const createObj = {
                    "session_id": sessionId,
                    "line_session_id": selectedProduct.line_session_id,
                    "product_id": selectedProduct.id,
                    "description": selectedProduct.UI_Description,
                    "search_price_unit": e.target.value
                }
                return createObj;
            })
        }
        dataOpticsApi1(dataOpticsPayload)
        
        
    }

    const dataOpticsApi1 = (payload: any) => {
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/saveSearchPriceData', payload)
        .catch((error) => {console.error(error)})
    }

    const dataOpticsApi2 = (payload: any) => {
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/saveSearchPriceMoveOutScreen', payload)
        .catch((error) => {console.error(error)})
    }


    if(!user.data){
        return <>
        </>
    }
    return (
        // <div className='bgImg' ref={ref}>
        //     <div className='headerPanel commonHeader'>
        //         <Header handleCloseWidget={handleCloseWidget} />
        //     </div>
            <div className='mainContent' ref={ref}>
            <div className={showSearchPanel ? 'searchPanel' : 'searchPanel hidden'}>
                <div className={`homeBody ${searchData === '' && selectedProducts.length === 0 ? '' : 'bdrRadiusNone'}`}>
                <div>
                        {selectedProducts.length === 0 ? <input 
                                type='search' 
                                name='search' 
                                onKeyDown={handleSearchKeyDown} 
                                autoFocus 
                                value={searchData} 
                                onChange={event => { 
                                    setSearchData(event.target.value); 
                                    setShowListPanel(false); 
                                    setShowTncPanel(false); 
                                    setSelectedIndex(-1); 
                                }} 
                                placeholder={selectedProducts.length === 0 ? 'Search by Product' : 'Search Another Product'} 
                                ref={searchProductInputRef}/> : 
                                    <span>
                                    <CommonTooltip
                                    title={'Use this search bar to search any product using any description'}
                                    tooltiplabel={
                                                <input 
                                        type='search' 
                                        name='search' 
                                        onKeyDown={handleSearchKeyDown} 
                                        value={searchData} 
                                        onChange={event => { 
                                            setSearchData(event.target.value); 
                                            setShowListPanel(false); 
                                            setShowTncPanel(false); 
                                            setSelectedIndex(-1); 
                                        }} 
                                        placeholder={selectedProducts.length === 0 ? 'Search by Product' : 'Search Another Product'} 
                                        ref={searchProductInputRef}/>
                                            }
                                    placement={'top-start'}
                                    classes={{
                                        popper: 'tooltipPopper',
                                        tooltip: 'tooltipMain tooltipSearch',
                                        arrow: 'tooltipArrow'
                                    }}
                                    localStorageKey="searchbarTooltip"
                                    />
                                    </span>
                        
                        }
                     
                   </div>

                </div>
                {!((searchData === '' && selectedProducts.length === 0) || showListPanel) && <div className="listBody">
                    <div className='ulBody' ref={containerRef}>
                        {
                            searchProducts(products, getValidSearchData(searchData), searchData).map((product, index) => (
                               
                                <CommonTooltip
                                key={product.id}
                                title={'Click on the product to view instant pricing'}
                                tooltiplabel={
                                    <div className={`liBody ${index === selectedIndex ? 'selectedLiBody' : ''}`}
                                    ref={index === selectedIndex ? selectedProductRef : null}
                                    key={product.id}
                                    value={product.id}
                                    onClick={() => selectProduct(product)}>
                                    {display(product)}
                                </div>
                                        }
                                placement={'top-end'}
                                classes={{
                                    popper: 'tooltipPopper tooltipSearchList',
                                    tooltip: 'tooltipMain tooltipRight2',
                                    arrow: 'tooltipArrow'
                                }}
                                localStorageKey="searchListTooltip"
                                />
                              
                            ))
                        }
                    </div>
                     {selectedProducts.length > 0 && searchData &&
                       <div className='lineH'></div>
                     }
                    {selectedProducts.length !== 0 && <div className="selectProduct">
                        <div className='headingSelect'>
                            <div className='firstDiv'>

                                <Tooltip
                                    title={resetBtnTooltip()}
                                    arrow
                                    placement={'right'}
                                    disableInteractive
                                    TransitionComponent={Fade}
                                    TransitionProps={{ timeout: 200 }}
                                    classes={{
                                        tooltip: 'rightTooltip',
                                    }}
                                >
                                    <div className='resetIcon cp' onClick={() => handleResetData()}>
                                        <span className='img1'><ResetIcon /></span>
                                        <span className='img2'><ResetIconHover /></span>
                                    </div>
                                </Tooltip>
                                {showSearchPanel &&
                                <Tooltip
                                    title={shareListTooltip()}
                                    arrow
                                    placement={'right'}
                                    disableInteractive
                                    TransitionComponent={Fade}
                                    TransitionProps={{ timeout: 600 }}
                                    classes={{
                                        tooltip: 'rightTooltip',
                                    }}
                                >
                                    <div className='resetIcon cp' onClick={(event) => handleSharePrice(event)}>
                                        <span className='img1'><ShareIcon /></span>
                                        <span className='img2'><ShareIconHover /></span>
                                        {Object.keys(focusSingleProduct).length !== 0 ? <span className='img3'><ShareIconHover /></span> : ''}
                                    </div>
                                </Tooltip>
                                }

                                <span>

                                    <Select
                                        value={selectedOption}
                                        onChange={(event) => { unitDropdownChangeHandler(event) }}
                                        className={`unitDropdown ${selectedOption === "cwt,ft" ? "multipleunitDropdown" : ""}`}
                                        IconComponent={DropdownIcon}
                                        MenuProps={{
                                            classes: {
                                                paper: 'SelectUnitDropdown',
                                            },
                                        }}
                                    >
                                        {selectedOption === "cwt,ft" && <MenuItem value="cwt,ft" disabled={true} style={{ display: 'none' }}>$/CWT, $/FT</MenuItem>}
                                        <MenuItem value="cwt">$/CWT</MenuItem>
                                        <MenuItem value="ft">$/FT</MenuItem>
                                        <MenuItem value="lb">$/LB</MenuItem>
                                    </Select>
                                </span>
                                <Tooltip
                                    title={priceUnitChangeTooltip()}
                                    arrow
                                    placement={'bottom-start'}
                                    disableInteractive
                                    TransitionComponent={Fade}
                                    TransitionProps={{ timeout: 600 }}
                                    classes={{
                                        tooltip: 'priceUnitChangeTooltip',
                                    }}
                                >
                                    <span className='questionIconPriceChange'>
                                        <QuestionIcon className='questionIcon1' />
                                        <QuestionHoverIcon className='questionIcon2' />
                                    </span>
                                </Tooltip>
                            </div>
                            <div className='secondDiv'>
                                {/* {user.data.type === userRole.sellerUser ? (<div className={`buyNowBtn ${showAlertForNewPo? 'showNewPoCliam' : 'buyNowBtnInactive'}`} onClick={navigateToOrderClaim}>{showAlertForNewPo ? 'New POs to Claim': 'New POs Coming Soon'}</div>) : ''} */}
                                {/* <div className='comingSoon'>Coming <br /> Soon <ComeSoonArrow /></div> */}
                                {/* <div className='buyNowBtn' onClick={() => navigate(routes.createPoPage)}>Create PO</div> */}
                            </div>
                        </div>
                       
                        <div className='selectedPordListScroll'>
                            {Array.isArray(selectedProducts) &&
                                selectedProducts.map(product => (
                                    <table className={`liBodyList ${focusSingleProduct[product.id] ? 'clickToShare' : ''}`} onClick={() => handleSelectSingleProduct(product)} key={product.id} >
                                        <tbody>
                                            <tr>
                                                <td>
                                                    {display(product)}
                                                </td>
                                                <td>
                                                    <div className='priceRating'>
                                                        <div>
                                                            <p className='selectPrize'>
                                                                <span className='doller'>$</span>
                                                                <span className='prize'>{showSelectedPrize(product)}</span>
                                                            </p>
                                                        </div>

                                                        <Tooltip
                                                            title={priceFeedBakTooltip()}
                                                            arrow
                                                            placement={'top-end'}
                                                            disableInteractive
                                                            TransitionComponent={Fade}
                                                            TransitionProps={{ timeout: 600 }}
                                                            classes={{
                                                                tooltip: 'priceFeedBakTooltip',
                                                            }}
                                                        >
                                                            <div className='priceFeedback'>
                                                                <label className={feedbackData[product.id] === 'HIGH' ? '' : 'hightBtn'}>
                                                                    <input
                                                                        type="radio"
                                                                        value="HIGH"
                                                                        checked={feedbackData[product.id] === 'HIGH'}
                                                                        onChange={(event) => handlePriceRatingChange(event, product)}
                                                                    />
                                                                    {feedbackData[product.id] === 'HIGH' ? (
                                                                        <UpWhiteArrow />
                                                                    ) : (
                                                                        <UpWhiteDArrow />
                                                                    )}
                                                                </label>
                                                                <label className={feedbackData[product.id] === 'GOOD' ? 'goodBtn' : 'CheckBtn'}>
                                                                    <input
                                                                        type="radio"
                                                                        value="GOOD"
                                                                        checked={feedbackData[product.id] === 'GOOD'}
                                                                        onChange={(event) => handlePriceRatingChange(event, product)}
                                                                    />
                                                                    {feedbackData[product.id] === 'GOOD' ? (
                                                                        <span className='checkDone'>
                                                                            <CheckIcon />
                                                                        </span>
                                                                    ) : (
                                                                        <span>
                                                                            <span className='img1'><CheckDIcon /></span>
                                                                            <span className='img2'><CheckIcon /></span>
                                                                        </span>
                                                                    )}
                                                                </label>
                                                                <label className={feedbackData[product.id] === 'LOW' ? '' : 'lowBtn'}>
                                                                    <input
                                                                        type="radio"
                                                                        value="LOW"
                                                                        checked={feedbackData[product.id] === 'LOW'}
                                                                        onChange={(event) => handlePriceRatingChange(event, product)}
                                                                    />
                                                                    {feedbackData[product.id] === 'LOW' ? (
                                                                        <DownWhiteArrow />
                                                                    ) : (
                                                                        <DownWhiteDArrow />
                                                                    )}
                                                                </label>

                                                            </div>
                                                        </Tooltip>

                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>

                                    </table>
                                )
                                )}
                        </div>
                        <div className='lineH'></div>
                        <div className='headingSelect footerSection'>
                            <div className='firstDiv'>
                                <span className='joinBryzos'>Patent Pending</span>
                            </div>
                            <Tooltip
                                title={shareWidgetTooltip()}
                                arrow
                                placement={'top-end'}
                                disableInteractive
                                TransitionComponent={Fade}
                                TransitionProps={{ timeout: 600 }}
                                classes={{
                                    tooltip: 'shareWidgetTooltip',
                                }}
                            >
                                <div className='secondDiv'>
                                    {/* <button onClick={handleOpenWidget} className='shareWidget'>Share Widget</button> */}
                                </div>
                            </Tooltip>

                        </div>
                    </div>
                    }
                </div>
                }
                <div className={showTncPanel ? 'widgetBody' : 'widgetBody hidden'}>
                    <div className='joinBryzos'>
                        <div className='tncIcon cp' onClick={handleCloseTnc}>
                            <TncCloseIcon className='img1' />
                            <TncCloseHoverIcon className='img2' />
                        </div>
                        <div>
                            <div className='joinBryzosHead'>Apply to join the testing group
                                <span  className='cp'>HERE</span>
                                .
                            </div>

                            <div className='joinBryzosContent'>


                                Bryzos is recruiting buyers and sellers of carbon steel shapes, Pipe ERW, bar, coil, sheet and plate to participate in Gone in 60 Seconds (“GISS”) beta testing.  Not all applicants can be accepted due to limited availability.
                                <br />
                                <br />
                                GISS, the product, focuses on speed & accuracy.
                                Your objective is to find your products and checkout in 60 seconds, or less.  As the seller, your goal is to make sales without quoting in 60 seconds, or less.
                                <br />
                                <br />
                                GISS beta program has been structured to test GISS’ instant pricing model, platform load testing and, most importantly, your experience using GISS!
                                <br />
                                <br />
                                The program is 3 months long.  Buyers will be asked to attempt to make 4 purchases in month 1, 8 purchases in month 2 and 12 purchases in month 3.
                                <br />
                                <br />
                                Thank you!

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className={showWidgetPanel ? 'widgetPanel' : 'widgetPanel hidden'}>
                <div>
                    <div className='widgetBody'>
                        <div>
                            <div className='widgetHead'>
                                {singleProduct.length !== 0 ? 'Share selected prices with a friend or coworker:' : 'Share this desktop widget with a friend or coworker:'}

                            </div>
                            <div className='widgetEmailInput'>
                                <input className={`email-id-share `} type="email" value={emailData} onChange={event => setEmailData(event.target.value)} onBlur={(event) => handleValidation(event)} placeholder='<EMAIL>'></input>
                            </div>
                            {emailError.length !== 0 &&
                                <p className='errorText1'>{emailError}</p>

                            }
                            <div className='widgetTextInput'>
                                <textarea className='smsTextShare  ' name='share-content' value={textarea} onChange={(event) => { setTextarea(event.target.value) }} placeholder={singleProduct.length !== 0 ? `The ${shareProductsLength} you have selected will be shared with your friend. Write your friend a note so they know it is not spam email.` :'Write your friend a note so they know it is not a spam email.'}>
                                </textarea>
                            </div>
                            <div className='widgetButtons'>
                                <button onClick={handleCloseWidget} className='cancelBtn'>Cancel</button>
                                <button className='sendBtn' disabled={isSendInProgress} onClick={handleSubmitData}>Send</button>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <DialogPopup
            dialogTitle={dialogTitle}
            dialogContent={dialogContent}
            dialogBtnTitle={dialogBtnTitle}
            type={dialogType}
            open={showDialogPopup}
            onClose={() => setShowDialogPopup(false)}
            />
            </div>
    );
};

export default Home;

