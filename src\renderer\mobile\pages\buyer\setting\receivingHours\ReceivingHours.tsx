// @ts-nocheck
import { IonPage, IonContent, useIonRouter, useIonViewWillEnter, useIonLoading, useIonViewWillLeave, useIonViewDidEnter } from '@ionic/react';
import styles from '../profile/Profile.module.scss';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useRef, useState } from 'react';
import { buyerSettingPayLoadFormatter, formatPhoneNumber, formatPhoneNumberWithCountryCode, saveUserSetting } from '../../../../library/helper';
import clsx from 'clsx';
import { CustomMenu } from '../../../../components/CustomMenu';
import { receivingHoursSchema } from '../SettingSchema';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import axios from 'axios';
import { RecevingHoursFrom, RecevingHoursTo, buyerSettingConst, mobileDiaglogConst } from '../../../../library/common';
import useBuyerSettingStore, { ReceivingHours as ReceivingHoursModel } from '../BuyerSettingStore';
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import useDialogStore from '../../../../components/Dialog/DialogStore';

const ReceivingHours = () => {
    const { 
        register, 
        handleSubmit, 
        getValues, 
        clearErrors, 
        setValue, 
        watch, 
        setError, 
        reset,
        control, 
        formState: { errors, dirtyFields, isDirty, isValid } } = useForm({
        resolver: yupResolver(receivingHoursSchema)
    });
    const [isSaveDisable, setIsSaveDisable] = useState(true);
    const {userData, setShowLoader} = useGlobalStore();
    const { receivingHoursInfo, setReceivingHoursInfo, buyerSetting, setBuyerSettingInfo } = useBuyerSettingStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();

    useEffect(()=>{
        if(isDirty)
        setIsSaveDisable(false);

        // const handleBackButton = (ev: BackButtonEvent) => {
        //     ev.detail.register(10, async () => {
        //         backToSetting();
        //     });
        // };

        // document.addEventListener('ionBackButton', handleBackButton);
        // return () => {
        //     document.removeEventListener('ionBackButton', handleBackButton);
        // };
    },[isDirty])

    useIonViewWillEnter(() => {
        if (receivingHoursInfo) {
            const weeks = receivingHoursInfo.map( day => {
                day.receivingHrsFrom = [...RecevingHoursFrom]
                day.receivingHrsTo = [...RecevingHoursTo]
                return day;
            });
            setValue('dates',weeks);
            clearErrors();
        }
    }, [receivingHoursInfo])

    const router = useIonRouter();

    useIonViewWillLeave(()=>{
        setIsSaveDisable(true);
        reset();
        resetDialogStore();
    })

    const onSubmit = async(data) => {
        setShowLoader(true)
        data.receivingHrsFrom = undefined;
        data.receivingHrsFromTo = undefined;
        const detail : ReceivingHoursModel[] = data.dates;  
        // const _buyerSetting = {...buyerSetting, user_delivery_receiving_availability_details: [...detail]};
        // const payload: any = buyerSettingPayLoadFormatter(_buyerSetting);
        try{
            await saveUserSetting(buyerSettingConst.apiRoutesForSave.receivingHours, detail, userData);
            // setBuyerSettingInfo(_buyerSetting);
            setReceivingHoursInfo(detail);
            router.push('/setting',{animate:true,direction:'forward'});
            setShowLoader(false);
        }catch(err){
            setShowLoader(false);
        }
    }

    const handleFormSubmit = () => {
        if(Object.keys(errors).length === 0 ){
            handleSubmit(onSubmit)();
        }else{
            return;
        }
    }

    const routeBackToSetting = ()=>{
        router.push('/setting',{animate:true,direction:'forward'});
        resetDialogStore();
    }

    const backToSetting = () => {
        console.log("CALLED")
        if(isDirty)
        showCommonDialog(mobileDiaglogConst.unsavedChangesMsg,[{name: mobileDiaglogConst.stay, action: resetDialogStore},{name: mobileDiaglogConst.goBackToSetting, action: routeBackToSetting}]);
        else
        routeBackToSetting();
    }

    const changeReceivingHrs = (dateIndex, isReceivingHrsFrom, dropdownValue)=>{
        setValue(`dates.${dateIndex}.is_user_available`, true);
        const receivingHrsOption = [];
        let currentDropdown = `dates.${dateIndex}.to`;
        let adjacentDropdown = `dates.${dateIndex}.from`;
        let adjDropDownOptionsCopy = RecevingHoursFrom;
        let dropDownOptionsToBeDisabled = `dates.${dateIndex}.receivingHrsFrom`;
        let onChangingCancelAdjDropDownValue = RecevingHoursFrom[0].value;
        if(isReceivingHrsFrom){
            currentDropdown = `dates.${dateIndex}.from`;
            adjacentDropdown = `dates.${dateIndex}.to`;
            adjDropDownOptionsCopy = RecevingHoursTo;
            onChangingCancelAdjDropDownValue = RecevingHoursTo[RecevingHoursTo.length-2].value;
            dropDownOptionsToBeDisabled = `dates.${dateIndex}.receivingHrsTo`;
        }
        //setting value of user changed dropdown
        setValue(currentDropdown, dropdownValue.toString());
        //Changing (from and to) to closed check
        if(dropdownValue === 'closed'){
            setValue(adjacentDropdown, dropdownValue.toString());
            setValue( `dates.${dateIndex}.is_user_available`, 0);
        }
        //if both were closed and one of the value changes then autofill the other dropdown
        else if(watch(adjacentDropdown) === 'closed'){
            setValue( `dates.${dateIndex}.is_user_available`, 1);
            setValue(adjacentDropdown, onChangingCancelAdjDropDownValue.toString());
        }
        //disabling options from dropdown
        adjDropDownOptionsCopy.forEach(timeOption => {
            const time = {...timeOption};
            if(dropdownValue !== 'closed' && ((!isReceivingHrsFrom && time.value >= dropdownValue) || (isReceivingHrsFrom && time.value <= dropdownValue)))time.disabled = true;
            receivingHrsOption.push(time);
        })
        setValue(dropDownOptionsToBeDisabled, receivingHrsOption);
    }

    return (
        <IonPage>
            <IonContent>
                <div className={styles.profile}>
                <h2 className={styles.heading}><BackBtnArrowIcon onClick={backToSetting}/><span>Receiving Hours</span></h2>
                    <div className={styles.settingsContent}>
                        <div className={styles.truckIn}>
                            <span className={styles.w50}>First Truck In</span>
                            <span className={styles.w50}>Last Truck In</span>
                        </div>
                        {watch('dates')?.map((x, i) => (<span key={x.day} className={styles.inputSectionRecevingHours}>
                            {/* <input type='hidden' value={x.day} {...register(`dates.${x.value}.name`)} /> */}
                            {/* <input id={x.display_name} {...register(`dates.${i}.is_user_available`)} type='checkbox' />
                            <label htmlFor={x.display_name} className={styles.daylbl1}>{x.display_name}</label> */}
                            <span className={`${watch(`dates.${i}.from`)!=='closed' ? styles.daylbl1 : styles.daylbl1}`}>{x.display_name}</span>
                            <span className={styles.gridReceivingHoursDropdown}>
                            <span className={clsx(styles.daylbl2, 'w100 dflex')}>
                                <CustomMenu 
                                    control={control}
                                    defaultValue={x.from}
                                    name={`dates.${i}.from`}
                                    className={clsx((!dirtyFields.dates?.[i]?.from  && 'disabledDropdown'),'selectDropdown')}
                                    MenuProps={{
                                        classes: {
                                            paper: clsx(styles.Dropdownpaper,styles.RecevingHoursDropdown)
                                        }}
                                    }
                                    items={x.receivingHrsFrom}
                                    onChange={(events) => {
                                        changeReceivingHrs(i, true, events.target.value);
                                    }}
                                />
                            </span>
                            <span className={clsx(styles.daylbl3, 'w100 dflex')}>
                                <CustomMenu
                                    defaultValue={x.to}
                                    control={control}
                                    name={`dates.${i}.to`}
                                    className={clsx((!dirtyFields.dates?.[i]?.to  && 'disabledDropdown'),'selectDropdown')}
                                    MenuProps={{
                                        classes: {
                                            paper: clsx(styles.Dropdownpaper,styles.RecevingHoursDropdown)
                                        }}
                                    }
                                    items={x.receivingHrsTo}
                                    onChange={(events) => {
                                        changeReceivingHrs(i, false, events.target.value);
                                    }}
                                />
                            </span>
                            </span>
                        </span>))}
                    
                    </div>

                    <div className={styles.btnSection}>
                        <button onClick={() => handleFormSubmit()}  disabled={isSaveDisable}>Save</button>
                    </div>
                     
                </div>
            </IonContent>
        </IonPage>
    )
}

export default ReceivingHours;