// @ts-nocheck
import styles from './ToastSnackbar.module.scss'
import useSnackbarStore from "./snackbarStore";
import clsx from 'clsx';
import { ReactComponent as WarningIcon } from '../../../../assets/mobile-images/Warning.svg';
import { ReactComponent as ClosePop } from '../../../../assets/mobile-images/closepop.svg';

import { v4 as uuidv4 } from 'uuid';
import { useEffect } from 'react';
import { customNotification } from '../../common';

const ToastSnackbar = () => {
    const { openSnackbar, snackbarMessage, snackbarMessageHeader, snackbarActions, snackbarSeverity, snackbarCloseHandler, snackbarTimer, resetSnackbarStore } = useSnackbarStore();

    useEffect(() => {
        console.log("Enter USEEffect ", snackbarTimer, openSnackbar)
        if (snackbarTimer && openSnackbar) {
            setTimeout(() => {
                resetSnackbarStore()
            }, snackbarTimer);
        }
    }, [openSnackbar, snackbarTimer])

    let backgroundColor;
    if (snackbarSeverity === 'warning') {
        backgroundColor = 'warning';
    }
    else if (snackbarSeverity === 'alert') {
        backgroundColor = 'alert';
    }
    else if (snackbarSeverity === 'success') {
        backgroundColor = 'green'
    } else if (snackbarSeverity === customNotification.priorty.low) {
        backgroundColor = "snackbar_LOW"
    } else if (snackbarSeverity === customNotification.priorty.medium) {
        backgroundColor = "snackbar_MEDIUM"
    } else if (snackbarSeverity === customNotification.priorty.high) {
        backgroundColor = "snackbar_HIGH"
    }

    if (!openSnackbar) return <></>

    return (
        <div className={clsx(styles.snackbarContainer, openSnackbar ? styles.show : styles.hide, `${backgroundColor}`, snackbarSeverity === 'alert' && styles.errorBorder)} >
            {snackbarSeverity === 'alert' && <span className='dflex'><WarningIcon /></span>}
            <div className={styles.errorTest}>
                <div className={clsx(snackbarSeverity === 'alert' ? styles.errorSms : styles.content)} dangerouslySetInnerHTML={{ __html: snackbarSeverity === 'alert' && !snackbarMessageHeader ? 'Error !' : snackbarMessageHeader }} />
                <div className={clsx(snackbarSeverity === 'alert' ? styles.errorContent : styles.content )} dangerouslySetInnerHTML={{ __html: snackbarMessage }} />
            </div>
            {
                snackbarActions?.map((action) => {
                    return <button key={uuidv4()} className={clsx(action.name && styles.actionBtn)} onClick={action.handler}> {action.name} <span className={styles.closeBtn}>{action.icon}</span> </button>
                })
            }
            {
                snackbarCloseHandler ? <button className={styles.closeBtn} onClick={snackbarCloseHandler}> <ClosePop /> </button> : <></>
            }
        </div>
    );
};


export default ToastSnackbar;