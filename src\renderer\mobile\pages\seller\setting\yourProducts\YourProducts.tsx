//@ts-nocheck
import { IonContent, IonPage, useIonLoading, useIonRouter, useIonViewWillEnter, useIonViewWillLeave } from "@ionic/react";
import { useEffect, useRef, useState } from "react";
import styles from '../documentsLibrary/DocumentInfo.module.scss';
import { yupResolver } from '@hookform/resolvers/yup';
import { yourProductsSchema } from '../SettingSchema';
import { useForm } from 'react-hook-form';
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import { ReactComponent as UploadIcon } from '../../../../../assets/mobile-images/icon_Upload.svg';
import { useGlobalStore } from "@bryzos/giss-ui-library";
import { fileType, mobileDiaglogConst, prefixUrl,  sellerSettingConst } from "../../../../library/common";
import useSellerSettingStore, { YourProducts as YourProductsModal } from "../SellerSettingStore";
import useDialogStore from '../../../../components/Dialog/DialogStore';
import { downloadFiles, saveUserSetting, uploadBuyerDocumentInfoToS3 } from "../../../../library/helper";

function YourProducts() {
  const {
    register,
    handleSubmit,
    getValues,
    clearErrors,
    setValue,
    reset,
    formState: { errors, isDirty } } = useForm({
      resolver: yupResolver(yourProductsSchema)
    });
  const { yourProductsInfo, setYourProductsInfo } = useSellerSettingStore();
  const [isSaveDisable, setIsSaveDisable] = useState(true);
  const { userData, setShowLoader } = useGlobalStore();
  const { showCommonDialog, resetDialogStore } = useDialogStore();
  const router = useIonRouter();
  const [present, dismiss] = useIonLoading();
  const productLineFileRef = useRef();

  useEffect(() => {
    if (isDirty)
      setIsSaveDisable(false);

    // const handleBackButton = (ev: BackButtonEvent) => {
    //   ev.detail.register(10, async () => {
    //     backToSetting();
    //   });
    // };

    // document.addEventListener('ionBackButton', handleBackButton);
    // return () => {
    //   document.removeEventListener('ionBackButton', handleBackButton);
    // };
  }, [isDirty])

  const lineCardFormEditHandler = () => {
    productLineFileRef.current.click();
  }

  useIonViewWillEnter(() => {
    if (yourProductsInfo && yourProductsInfo.products_s3_url) {
      setValue("products_s3_url", yourProductsInfo.products_s3_url)
      clearErrors();
    }
  }, [yourProductsInfo])

  const uploadLineCardFile = async (event) => {
    const file = event.target.files[0];
    try {
      if (event.target.files.length !== 0) {
        setShowLoader(true);
        const certUrl = await uploadBuyerDocumentInfoToS3(file, userData, prefixUrl.lineCard, import.meta.env.VITE_S3_UPLOAD_SETTINGS_LINE_CARD_BUCKET_NAME);
        setValue("products_s3_url", certUrl)
        setIsSaveDisable(false);
        setShowLoader(false);
      }
    } catch (error) {
      console.error(error);
      setIsSaveDisable(true);
      setShowLoader(false);
    }
  }

  useIonViewWillLeave(() => {
    setIsSaveDisable(true);
    reset();
    resetDialogStore();
  }, [])

  const routeBackToSetting = () => {
    router.push('/seller-setting',{animate:true,direction:'forward'});
    resetDialogStore();
  }

  const backToSetting = () => {
    if (isDirty)
      showCommonDialog(mobileDiaglogConst.unsavedChangesMsg, [{ name: mobileDiaglogConst.stay, action: resetDialogStore }, { name: mobileDiaglogConst.goBackToSetting, action: routeBackToSetting }]);
    else
      routeBackToSetting();
  }

  const onSubmit = async (data) => {
    setShowLoader(true)
    const detail: YourProductsModal = data;
    try {
      await saveUserSetting(sellerSettingConst.apiRoutesForSave.documentInfo, detail, userData);
      setYourProductsInfo(detail);
      router.push('/seller-setting',{animate:true,direction:'forward'});
      setShowLoader(false);
    } catch (err) {
      console.error(err)
      setShowLoader(false);
    }
  }

  const viewProducts = () => {
    setShowLoader(true);
    const url = getValues("products_s3_url");
    const fileName = url.substring(url.lastIndexOf('/') + 1, url.length);

    console.log('url', url);

    downloadFiles(url, fileName, fileType.unknown).then(() => {
      setShowLoader(false);
    });
  }

  return (
    <IonPage>
      <IonContent>
        <>
          <div className={styles.profile}>
            <h2 className={styles.heading}>
              <BackBtnArrowIcon onClick={backToSetting} />
              <span>Your Products</span>
            </h2>
            <div className={styles.profileComponent}>
              <label> Your Products</label>
              {getValues("products_s3_url") ? (
                <div className={styles.uploadFileStatus}>
                  <span className={styles.uploadedFileName}>
                    <button className={styles.viewBtn} onClick={viewProducts}>
                      View
                    </button>
                    <span className={styles.orText}>or</span>
                    <button
                      className={styles.viewBtn}
                      onClick={lineCardFormEditHandler}
                    >
                      Edit
                    </button>
                  </span>
                </div>
              ) : (
                <label className={styles.uploadFileBtn}>
                  <UploadIcon />
                  <button
                    onClick={lineCardFormEditHandler}
                    className={styles.uploadText}
                  >
                    Upload Line Card
                  </button>
                </label>
              )}
              <input
                {...register('products_s3_url')}
                type='file'
                onChange={(e) => {
                  uploadLineCardFile(e);
                  register('products_s3_url').onChange(e);
                }}
                ref={productLineFileRef}
              />
            </div>
            <div className={styles.btnSection}>
              <button className={styles.saveBtn} onClick={handleSubmit(onSubmit)} disabled={isSaveDisable} >
                Save
              </button>
            </div>
          </div>
        </>
      </IonContent>
    </IonPage>
  );
}
export default YourProducts;
