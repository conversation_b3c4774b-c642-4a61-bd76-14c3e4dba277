import { create } from 'zustand';

export interface Profile { 
    first_name: string, 
    last_name: string, 
    email_id: string, 
    phone: string,
} 

export interface CompanyDetails { 
    company_name: string, 
    client_company: string | null, 
    company_address_line1: string,
    company_address_line2: null, 
    company_address_city: string,
    company_address_state_id: number,
    company_address_zip: string
}

export interface StockingLocation {
    stocking_location: string | null,
    send_invoices_to: string, 
    shipping_docs_to: string
}
export interface FundingSettings {
    id: string,
    user_id: string,
    payment_info_id: string | null,
    bank_name: string | null,
    routing_number: string | null,
    account_number: string | null,
    reference_document_id: string | null,
    pgpm_mapping_id: number | null,
    is_active: 1,
    created_date: string,
    time_stamp: string, 
}
export interface W9Form {
    w9_form_s3_url: string | null,
}
export interface YourProducts {
    products_s3_url: string | null,
}
export interface SellerSetting{
    id: string,
    user_id: string,
    company_name: string,
    company_id: null,
    company_address_line1: string,
    company_address_line2: null,
    company_address_city: string,
    company_address_state_id: 27,
    company_address_zip: string,
    first_name: string,
    last_name: string,
    email_id: string,
    phone: string,
    stocking_location: string | null,
    send_invoices_to: string,
    shipping_docs_to: string,
    w9_form_s3_url: string,
    products_s3_url: string,
    is_active: 1,
    created_date: string,
    time_stamp: string,
    client_company: string,
    funding_settings: FundingSettings,
} 
// export const getbuyerSettingPayload = () => {
//     return 
// }

interface SellerSettingState {
    profileInfo: Profile | null;
    companyInfo: CompanyDetails | null;
    stockingLocationInfo: StockingLocation | null;
    fundingSettingsInfo: FundingSettings | null;
    w9FormInfo: W9Form | null;
    yourProductsInfo: YourProducts | null;
    sellerSetting: SellerSetting | null;
}

const sellerSettingStoreInit: SellerSettingState = {
    profileInfo: null,
    companyInfo: null,
    stockingLocationInfo: null,
    fundingSettingsInfo: null,
    w9FormInfo: null,
    yourProductsInfo: null,
    sellerSetting: null,
}

const useSellerSettingStore = create<SellerSettingState>((set) => ({
    ...sellerSettingStoreInit,
    setProfileInfo: (profileInfo: Profile) => set({ profileInfo }),
    setCompanyInfo: (companyInfo: CompanyDetails) => set({ companyInfo }),
    setStockingLocationInfo: (stockingLocationInfo: StockingLocation) => set({ stockingLocationInfo }),
    setFundingSettingsInfo: (fundingSettingsInfo: FundingSettings) => set({ fundingSettingsInfo }),
    setW9FormInfo: (w9FormInfo: W9Form) => set({ w9FormInfo }),
    setYourProductsInfo: (yourProductsInfo: YourProducts) => set({ yourProductsInfo }),
    setSellerSettingInfo: (sellerSetting: SellerSetting) => set({ sellerSetting }),
    resetSellerSetting: () => set(() => ({
        ...sellerSettingStoreInit
    }))
}));

export default useSellerSettingStore;