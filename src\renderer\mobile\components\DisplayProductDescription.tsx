const DisplayProductDescription = (props: any) => {
    const lines = props.description.split('\n');
    const firstLine = lines[0];
    const restLines = lines.slice(1);
  
    return (
      <div>
        <p className="liHead">{firstLine}</p>
        {restLines.map((line:string, index:number) => (
          <p key={index}>{line}</p>
        ))}
      </div>
    );
}

export default DisplayProductDescription;