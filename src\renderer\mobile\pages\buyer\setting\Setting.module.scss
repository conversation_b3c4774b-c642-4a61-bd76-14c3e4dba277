.settingsPageMain {
    padding-left: 16px;
    overflow: auto;
    height: calc( 100vh - 90px);
    display: flex;
    flex-direction: column;

    .heading {
        font-family: Noto Sans Display;
        font-size: 16px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: #fff;
        padding: 12px 0px;
        margin-top: 0px;
        border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
        display: flex;
        margin-right: 20px;
        margin-bottom: 0px;
    }
    .containDiv{
        flex: 1;
        overflow: auto;
        padding-right: 16px;
        padding-bottom: 20px;
    }


    .settingsPageBtn {
        height: 46px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        gap: 12px;
        padding: 16px 8px 16px 12px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.2);
        margin-top: 16px;

        &.disabledBtn{
            opacity: 0.4;
        }

        button {
            flex-grow: 1;
            font-family: Noto Sans Display;
            font-size: 14px;
            font-weight: 300;
            line-height: 1.6;
            text-align: left;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 12px;

            .rightIcon {
                margin-left: auto;
            }

        }
    }
    .appVersion{
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: 300;
        line-height: 1.6;
        color: #fff;
        text-align: center;
        padding-top: 16px;
        padding-bottom: 16px;
        .active{
            color: #70ff00;
        }
    }
    
}
.snackbarContainer {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    gap: 6px;
    padding: 12px;
    border-radius: 6px;
    border: solid 1px #ff5d47;
    background-color: #ffefed;
    transition: opacity 0.3s ease-in-out;
    margin-top: 16px;

    &.show {
        opacity: 1;
    }

    .content {
        align-self: stretch;
        font-family: Noto Sans;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: normal;
        text-align: left;
        color: #ff5d47;
    }

    .errorTest {
        width: 100%;

        .errorSms {
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: 600;
            line-height: 1.6;
            letter-spacing: normal;
            text-align: left;
            color: #ff5d47;
            margin-bottom: 4px;
        }
    }
}