// @ts-nocheck
import { IonPage, IonContent, useIonRouter, useIonLoading, useIonViewWillLeave } from '@ionic/react';
import styles from './MyReports.module.scss';
import { useState } from 'react';
import { downloadFiles } from '../../../../library/helper';
import { Dialog } from '@mui/material';
import clsx from 'clsx';
import { fileType } from '../../../../library/common';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import { ReactComponent as FolderDownload } from '../../../../../assets/mobile-images/folder-download.svg';

function SellerMyReports() {
    const [openErrorDialog, setOpenErrorDialog] = useState(false);
    const router = useIonRouter();
    const {setShowLoader} = useGlobalStore();

    useIonViewWillLeave(()=>{
        setOpenErrorDialog(false)
    },[])

    const downloadReports = (fileUrl, fileName, fileType) => {
        setShowLoader(true);
        const showError = downloadFiles(fileUrl, fileName, fileType)
        showError.then(res => {
            setShowLoader(false);
            if (res) {
                setOpenErrorDialog(false);
            } else {
                setOpenErrorDialog(true);
            }
        })
        .catch(e => {
            setShowLoader(false);
        });
    }

    const routeBackToSetting = ()=>{
        router.push('/seller-setting',{animate:true,direction:'forward'});
    }

    return (
        <IonPage>
            <IonContent>
                <div className={styles.myReports}>
                    <h2 className={styles.heading}><BackBtnArrowIcon onClick={routeBackToSetting}/><span>My Reports</span></h2>
                    <button  onClick={() => {downloadReports(import.meta.env.VITE_API_SERVICE + `/user/salesOrderExcel`, 'Sales Order History',fileType.excelSheet)}}><FolderDownload/><span>Sales Order History</span></button>
                    
                    <button  onClick={() => {downloadReports(import.meta.env.VITE_API_SERVICE + `/user/receivableStatementExcel`, 'Funding History',fileType.excelSheet)}}><FolderDownload/><span>Funding History (Accounts Receivable Statement)</span></button>
                     
                </div>
                
            <>
                <Dialog
                    open={openErrorDialog}
                    onClose={(event) => setOpenErrorDialog(false)}
                    transitionDuration={200}
                    hideBackdrop
                    classes={{
                        root: styles.ErrorDialog,
                        paper: styles.dialogContent
                    }}

                >
                    <p className={styles.containOfError}>No data found. Please try again in sometime</p>
                    <button className={styles.okForErrorBtn} onClick={(event) => setOpenErrorDialog(false)}>Ok</button>
                </Dialog>
            </>
            </IonContent>
        </IonPage>
    )
}
export default SellerMyReports;
