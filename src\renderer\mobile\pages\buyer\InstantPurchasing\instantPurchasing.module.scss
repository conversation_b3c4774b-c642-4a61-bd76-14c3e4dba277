.instantPurchasingMain {
  display: flex;
  flex-direction: column;
  height: calc(100% - var(--headerHeight));
  &.orderConfirmationStep{
    height: 100%;
  }

  .tabContent {
    height: 100%;
    position: relative;
  }
}

.tabPanel.tabPanel {
  background-color: #000;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.4);

  .purchaseStepstext {
    font-family: Noto Sans Display;
    font-size: 14px;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: center;
    color: #fff;
    display: block;
  }
  .purchaseStepstextHidden{
    visibility: hidden;
  }


  .ion-page {
    padding: 222px 16px 16px 16px;
  }

  .tabs {
    width: 100%;
    padding: 6px 0px 12px 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    svg {
      margin: 0px 8px;
    }
  }

  .tabsBtn {
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: solid 1px rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    font-family: Noto Sans Display;
    font-size: 12px;
    font-weight: 600;
    line-height: 1.2;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    padding: 0px;
    letter-spacing: normal;
    &:last-child {
      margin-right: 0px;
    }

    .stepsContent {
      display: flex;
      flex-direction: column;
    }

    span {
      font-size: 10px;
      font-weight: 300;
      line-height: 1;
      margin-top: 2px;
    }

    &.tabSelected {
      background-color: #70ff00;
      border: 1px solid #70ff00;
      color: #000;
    }
    &.activeTab{
      border: 1px solid #70ff00;
    }
  }

}