import { IonContent, IonPage, useIonRouter, useIonViewWillEnter } from '@ionic/react';
import styles from './OnboardingThankYou.module.scss';
import { useState } from 'react';

function OnboardingThankYou() {
    const router = useIonRouter();
    const[isUserApproved, setIsUserApproved] = useState();
    useIonViewWillEnter(()=>{
        const {isUserApproved} = router.routeInfo.routeOptions;
        setIsUserApproved(isUserApproved)
    },[])
    return (
        <IonPage>
            <IonContent>
                <div className={styles.thankyouBox} >
                    <div className={styles.onboardingLogo}>
                        <img src='/onboardingLogo.png' />
                    </div>
                    <div className={styles.onboardingThnkYouMain}>
                        <h2>Thank you for joining!</h2>
                        {(isUserApproved !== undefined) &&<>
                            {isUserApproved ? 
                                <p className={styles.thnkPara}>You are now approved and can log in using the credentials you created during signup.</p> 
                                :
                                <>
                                    <p className={styles.thnkPara}>Your information is currently being reviewed.</p>
                                    <p>You will receive an email at the provided email address once review is complete.</p>
                                </>
                            }
                        </>}
                        <button className={styles.loginBtn} onClick={()=>{router.push("/login",{animate:true})}}>{isUserApproved ? 'Login' : 'Go back to login'}</button>
                    </div>
                </div>
            </IonContent>
        </IonPage>
    );
}
export default OnboardingThankYou;