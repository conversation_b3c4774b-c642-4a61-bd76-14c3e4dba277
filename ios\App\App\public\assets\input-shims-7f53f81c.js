import{bO as A,bV as k,bW as P,bX as B,bS as p,bR as F,bY as H,bZ as b,b_ as Y,b$ as $,c0 as U}from"./vendor-74ddad81.js";/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const D=new WeakMap,T=(e,s,t,o=0,i=!1)=>{D.has(e)!==t&&(t?q(e,s,o,i):G(e,s))},W=e=>e===e.getRootNode().activeElement,q=(e,s,t,o=!1)=>{const i=s.parentNode,r=s.cloneNode(!1);r.classList.add("cloned-input"),r.tabIndex=-1,o&&(r.disabled=!0),i.appendChild(r),D.set(e,r);const a=e.ownerDocument.dir==="rtl"?9999:-9999;e.style.pointerEvents="none",s.style.transform=`translate3d(${a}px,${t}px,0) scale(0)`},G=(e,s)=>{const t=D.get(e);t&&(D.delete(e),t.remove()),e.style.pointerEvents="",s.style.transform=""},N=50,z=(e,s,t)=>{if(!t||!s)return()=>{};const o=a=>{W(s)&&T(e,s,a)},i=()=>T(e,s,!1),r=()=>o(!0),c=()=>o(!1);return P(t,"ionScrollStart",r),P(t,"ionScrollEnd",c),s.addEventListener("blur",i),()=>{B(t,"ionScrollStart",r),B(t,"ionScrollEnd",c),s.removeEventListener("blur",i)}},I="input, textarea, [no-blur], [contenteditable]",j=()=>{let e=!0,s=!1;const t=document,o=()=>{s=!0},i=()=>{e=!0},r=c=>{if(s){s=!1;return}const a=t.activeElement;if(!a||a.matches(I))return;const f=c.target;f!==a&&(f.matches(I)||f.closest(I)||(e=!1,setTimeout(()=>{e||a.blur()},50)))};return P(t,"ionScrollStart",o),t.addEventListener("focusin",i,!0),t.addEventListener("touchend",r,!1),()=>{B(t,"ionScrollStart",o,!0),t.removeEventListener("focusin",i,!0),t.removeEventListener("touchend",r,!1)}},V=.3,X=(e,s,t,o)=>{var i;const r=(i=e.closest("ion-item,[ion-item]"))!==null&&i!==void 0?i:e;return Z(r.getBoundingClientRect(),s.getBoundingClientRect(),t,o)},Z=(e,s,t,o)=>{const i=e.top,r=e.bottom,c=s.top,a=Math.min(s.bottom,o-t),f=c+15,l=a-N-r,d=f-i,S=Math.round(l<0?-l:d>0?-d:0),v=Math.min(S,i-c),n=Math.abs(v)/V,y=Math.min(400,Math.max(150,n));return{scrollAmount:v,scrollDuration:y,scrollPadding:t,inputSafeY:-(i-f)+4}},x="$ionPaddingTimer",M=(e,s,t)=>{const o=e[x];o&&clearTimeout(o),s>0?e.style.setProperty("--keyboard-offset",`${s}px`):e[x]=setTimeout(()=>{e.style.setProperty("--keyboard-offset","0px"),t&&t()},120)},_=(e,s,t)=>{const o=()=>{s&&M(s,0,t)};e.addEventListener("focusout",o,{once:!0})};let g=0;const K="data-ionic-skip-scroll-assist",J=(e,s,t,o,i,r,c,a=!1)=>{const f=r&&(c===void 0||c.mode===H.None);let u=!1;const l=b!==void 0?b.innerHeight:0,d=h=>{if(u===!1){u=!0;return}O(e,s,t,o,h.detail.keyboardHeight,f,a,l,!1)},S=()=>{u=!1,b===null||b===void 0||b.removeEventListener("ionKeyboardDidShow",d),e.removeEventListener("focusout",S)},v=async()=>{if(s.hasAttribute(K)){s.removeAttribute(K);return}O(e,s,t,o,i,f,a,l),b===null||b===void 0||b.addEventListener("ionKeyboardDidShow",d),e.addEventListener("focusout",S)};return e.addEventListener("focusin",v),()=>{e.removeEventListener("focusin",v),b===null||b===void 0||b.removeEventListener("ionKeyboardDidShow",d),e.removeEventListener("focusout",S)}},C=e=>{document.activeElement!==e&&(e.setAttribute(K,"true"),e.focus())},O=async(e,s,t,o,i,r,c=!1,a=0,f=!0)=>{if(!t&&!o)return;const u=X(e,t||o,i,a);if(t&&Math.abs(u.scrollAmount)<4){C(s),r&&t!==null&&(M(t,g),_(s,t,()=>g=0));return}if(T(e,s,!0,u.inputSafeY,c),C(s),Y(()=>e.click()),r&&t&&(g=u.scrollPadding,M(t,g)),typeof window<"u"){let l;const d=async()=>{l!==void 0&&clearTimeout(l),window.removeEventListener("ionKeyboardDidShow",S),window.removeEventListener("ionKeyboardDidShow",d),t&&await U(t,0,u.scrollAmount,u.scrollDuration),T(e,s,!1,u.inputSafeY),C(s),r&&_(s,t,()=>g=0)},S=()=>{window.removeEventListener("ionKeyboardDidShow",S),window.addEventListener("ionKeyboardDidShow",d)};if(t){const v=await $(t),h=v.scrollHeight-v.clientHeight;if(f&&u.scrollAmount>h-v.scrollTop){s.type==="password"?(u.scrollAmount+=N,window.addEventListener("ionKeyboardDidShow",S)):window.addEventListener("ionKeyboardDidShow",d),l=setTimeout(d,1e3);return}}d()}},Q=!0,te=async(e,s)=>{if(A===void 0)return;const t=s==="ios",o=s==="android",i=e.getNumber("keyboardHeight",290),r=e.getBoolean("scrollAssist",!0),c=e.getBoolean("hideCaretOnScroll",t),a=e.getBoolean("inputBlurring",!1),f=e.getBoolean("scrollPadding",!0),u=Array.from(A.querySelectorAll("ion-input, ion-textarea")),l=new WeakMap,d=new WeakMap,S=await k.getResizeMode(),v=async n=>{await new Promise(m=>p(n,m));const y=n.shadowRoot||n,w=y.querySelector("input")||y.querySelector("textarea"),L=F(n),R=L?null:n.closest("ion-footer");if(!w)return;if(L&&c&&!l.has(n)){const m=z(n,w,L);l.set(n,m)}if(!(w.type==="date"||w.type==="datetime-local")&&(L||R)&&r&&!d.has(n)){const m=J(n,w,L,R,i,f,S,o);d.set(n,m)}},h=n=>{if(c){const y=l.get(n);y&&y(),l.delete(n)}if(r){const y=d.get(n);y&&y(),d.delete(n)}};a&&Q&&j();for(const n of u)v(n);A.addEventListener("ionInputDidLoad",n=>{v(n.detail)}),A.addEventListener("ionInputDidUnload",n=>{h(n.detail)})};export{te as startInputShims};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
