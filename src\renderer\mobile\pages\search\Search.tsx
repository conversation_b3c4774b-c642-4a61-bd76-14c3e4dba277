
import styles from './search.module.scss'
import { useEffect, useState,  useRef } from 'react';
import axios from 'axios';

import { ReactComponent as CloseIcon } from '../../../assets/mobile-images/close_Popup.svg';
import {  MinSearchDataLen, userRole } from '../../library/common';
import { useDebouncedValue } from '@mantine/hooks';
import { v4 as uuidv4 } from 'uuid';
import { IonContent, IonModal, IonPage,  useIonViewDidEnter, useIonViewWillEnter, useIonViewWillLeave } from '@ionic/react';
import SearchProductList from '../../components/searchProductList/SearchProductList';
import ShareApp from '../../components/ShareApp';
import clsx from 'clsx';
import { useSellerOrderStore, useGlobalStore, searchProducts, getValidSearchData, selectProduct, changeProductUnitOption, dataOpticsApi1, dataOpticsApi2, getFormattedProductPricing } from '@bryzos/giss-ui-library';
import {  ProductPricingModel, ProductQtyType } from '../../types/Search';
import SelectedProductPricing from './selectedProductPricing';
import { getProductWithPrices } from '../../library/helper';
import { ReferenceDataProduct } from '../../types/ReferenceDataProduct';
 
const Search = () => {
  const {userData, enableShareWidget, setEnableShareWidget, searchSessionId, setSearchSessionId, setShowLoader, setUserData, discountData, productData, productMapping} = useGlobalStore();
  const [searchData, setSearchData] = useState('');
  const [products, setProducts] = useState('');
  const initialSelectedProducts = userData?.selectedProducts ? userData.selectedProducts : [];
  const [selectedProducts, setSelectedProducts] = useState(initialSelectedProducts);
  const [showSearchPanel, setShowSearchPanel] = useState(true);
  const [showListPanel, setShowListPanel] = useState(false);
  const [selectedOption, setSelectedOption] = useState<ProductQtyType>('cwt');
  const [isUserChangedSelectedOption, setIsUserChangedSelectedOption] = useState(false);
  
  const [sessionId, setSessionId] = useState('');
  const [debouncedSearchData, cancelDebouncedSearchData] = useDebouncedValue(searchData, 400);
  const [enableRejectSearchAnalytic, setEnableRejectSearchAnalytic] = useState(true);
  const [showWidgetPanel, setShowWidgetPanel] = useState(false);
  const { resetFiltersInPurchaseOrders } = useSellerOrderStore();

  const searchProductInputRef = useRef<any>(null);
  const analyticRef = useRef<string>();
  analyticRef.current = sessionId;

  useIonViewWillEnter(() => {
    resetFiltersInPurchaseOrders();
    setShowLoader(false);
    if (searchSessionId) {
      setSessionId(searchSessionId)
    } else {
      const sessionId = uuidv4();
      setSessionId(sessionId);
    }
  }, []);

  useIonViewDidEnter(() => {
    searchProductInputRef.current?.focus();
  }, []);

  useEffect(() => {
    if (sessionId) {
      setSearchSessionId(sessionId);
    }
  }, [sessionId])

  useIonViewWillLeave(()=>{
      setSelectedProducts([]);
      // setFeedBackData({});
      setEnableShareWidget(false);
      const dataOpticsPayload = {
        "data": {
          "session_id": analyticRef.current,
          "move_to_screen": location.pathname.replace('/', "")
        }
      }
      dataOpticsApi2(dataOpticsPayload)
  },[])

  useEffect(() => {
    if (productData && selectedProducts.length > 0) {
      const productDataList = productMapping;
      setSelectedProducts(selectedProducts.map((product: ProductPricingModel)=>getProductWithPrices(productDataList[product.id], userData.data.type, userRole)));
      changeProductUnitOption(selectedProducts, isUserChangedSelectedOption, selectedOption, setSelectedOption);
    }
  }, [productData, productMapping]);

  useEffect(() => {
    if (enableShareWidget) {
      handleOpenWidget();
    }
    else {
      handleCloseWidget();
    }
  }, [enableShareWidget])

 
  useEffect(() => {
    if (productData) {
      setProducts(productData);
    }
  }, [productData]);

  useEffect(() => {
    if (debouncedSearchData?.length >= MinSearchDataLen) {
      searchAnalyticsApi(sessionId, null, debouncedSearchData)
    }
  }, [debouncedSearchData])


  useEffect(() => {
    const searchProductList = searchProducts(products, getValidSearchData(searchData), searchData);
    if (searchData.length === 1) {
      setEnableRejectSearchAnalytic(true)
    }
    if (searchData.length === 0 && searchProductList.length === 0 && debouncedSearchData && enableRejectSearchAnalytic) {
      searchAnalyticsApi(sessionId, 'Reject', debouncedSearchData)
    }
  }, [searchData])

  useEffect(() => {
    if (productMapping && userData?.selectedProducts?.length) {
        const selectedProductsCopy: ProductPricingModel[] = [];
        userData.selectedProducts.forEach((selectedProduct: ProductPricingModel) => {
            selectedProductsCopy.push(getFormattedProductPricing(productMapping[selectedProduct.id], userData.data.type));   
        });
        setSelectedProducts(selectedProductsCopy);
        setUserData({ ...userData, selectedProducts: selectedProductsCopy });
    }
}, [discountData, productMapping]);

  const selectProductFromSearch = (product: ReferenceDataProduct) => {
    setEnableRejectSearchAnalytic(false)
    searchAnalyticsApi(sessionId, 'Accept', debouncedSearchData)
    selectProduct(product, selectedProducts, setSelectedProducts, sessionId, selectedOption, isUserChangedSelectedOption, setSelectedOption);
    setSearchData('');
    if(searchProductInputRef.current)
    searchProductInputRef.current.focus();
  }

  const handleOpenWidget = () => {
    setShowSearchPanel(false);
    setShowWidgetPanel(true);
  };

  const handleCloseWidget = () => {
    setShowSearchPanel(true);
    setEnableShareWidget(false);
    setShowWidgetPanel(false);
  };

  const searchAnalyticsApi = (sessionId: string, status: string | null, searchKeyword: string) => {
    const payload = {
      "data": {
        "session_id": sessionId,
        "keyword": searchKeyword,
        "status": status,
        "source": 'search'

      }
    }
    axios.post(import.meta.env.VITE_API_SERVICE + '/user/create_po_search', payload)
      .catch(err => console.error(err))
  }

  const productSearchHandler = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchData(event.target.value);
    setShowListPanel(false);
  }

  const onShareProductPricing = async (emailTo: string, emailContent: string) => {
    let products = [];
    if (userData?.singleProduct?.length) {
      products = userData.singleProduct;
    } else if (selectedProducts?.length) {
      products = selectedProducts;
    }

    if (products?.length) {
      const apiRoute = import.meta.env.VITE_API_SERVICE + '/user/shareProductPrice';
      const productList = products.map((product: any) => {
        return {
          "product_id": product.id,
          "product_description": product.UI_Description,
          "price_ft": product.ft_price.trim().replace("$", ""),
          "price_lb": product.lb_price.trim().replace("$", ""),
          "price_cwt": product.cwt_price.trim().replace("$", ""),
          "price_share_type": selectedOption !== 'cwt,ft' ? selectedOption : product.product_type_pipe ? "ft" : "cwt",
        }
      });
      const dataOpticsPayload = {
        "data": products.map((product: any) => {
          return {
            "session_id": sessionId,
            "line_session_id": product.line_session_id,
            "product_id": product.id,
            "description": product.UI_Description,
            "price_shared": true,
            "search_price_unit": selectedOption !== 'cwt,ft' ? selectedOption : product.product_type_pipe ? "ft" : "cwt",
          }
        })
      };
      dataOpticsApi1(dataOpticsPayload);

      const payload = {
        data: {
          "user_id": userData.data.id,
          "from_email": userData.data.email_id,
          "to_email": emailTo,
          "email_content": emailContent.trim().length === 0 ? null : emailContent,
          "products": productList,
        }
      }
      const result = await axios.post(apiRoute, payload);
      return result
    }
  }

  return (
    <IonPage>
      <IonContent>
        <div className={styles.searchMain}>
          <div className={clsx(styles.searchPanel, showSearchPanel ? 'searchPanelMob' : 'searchPanelMob hidden')}>
            <SearchProductList
              searchString={searchData}
              selectedProducts={selectedProducts}
              showListPanel={showListPanel}
              productSearchHandler={productSearchHandler}
              searchProductInputRef={searchProductInputRef}
              products={products}
              selectProduct={selectProductFromSearch}
              setSearchString={setSearchData}
              excludeSafeProduct={false}
             />
            {!((searchData === '' && selectedProducts.length === 0) || showListPanel) &&
             <div className={clsx("listBody",(selectedProducts.length !== 0 && searchData === '') ? styles.selectedProductsMain : 'hidden')}>
              {selectedProducts.length > 0 && searchData &&
                <div className='lineH'></div>
              }
              {selectedProducts.length !== 0 && 
              <SelectedProductPricing 
                selectedProducts = {selectedProducts}
                setSelectedProducts = {setSelectedProducts}
                isUserChangedSelectedOption = {isUserChangedSelectedOption} 
                setIsUserChangedSelectedOption = {setIsUserChangedSelectedOption}
                selectedOption = {selectedOption} 
                setSelectedOption = {setSelectedOption}
                sessionId = {sessionId}
                setShowWidgetPanel={setShowWidgetPanel}
                dataOpticsApi1= {dataOpticsApi1} 
              />
              }
            </div>
            }
          </div>
        </div>
        <IonModal className={'shareAppPopup'} isOpen={showWidgetPanel} backdropDismiss={false}>
              <button className={styles.closePopupBtn} onClick={() => handleCloseWidget()}><CloseIcon /></button>
              <ShareApp 
                singleProduct= {userData.singleProduct}
                selectedProducts = {selectedProducts}
                onCancel={handleCloseWidget}
                selectedOption={selectedOption}
                sessionId={sessionId}
                dataOpticsApi1={dataOpticsApi1}
                shareProductPricing={onShareProductPricing}
                /> 
        </IonModal>
      </IonContent>
    </IonPage>

  )
}

export default Search