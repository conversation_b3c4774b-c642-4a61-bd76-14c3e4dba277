#if 0
#elif defined(__arm64__) && __arm64__
// Generated by Apple Swift version 5.8.1 (swiftlang-5.8.0.124.5 clang-1403.0.22.11.100)
#ifndef PUSHNOTIFICATIONS_SWIFT_H
#define PUSHNOTIFICATIONS_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#if __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
#endif
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import Foundation;
@import ObjectiveC;
@import UserNotifications;
#endif

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="PushNotifications",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)
@class NSString;

/// Authentication data that is provided to a <code>TokenProvider</code>, such as <code>BeamsTokenProvider</code>.
SWIFT_CLASS("_TtC17PushNotifications8AuthData")
@interface AuthData : NSObject
/// Create an <code>AuthData</code> instance based on some <code>headers</code> and <code>queryParams</code>.
/// \param headers A <code>Dictionary</code> of header key / value pairs.
///
/// \param queryParams A <code>Dictionary</code> of query parameters key / value pairs.
///
- (nonnull instancetype)initWithHeaders:(NSDictionary<NSString *, NSString *> * _Nonnull)headers queryParams:(NSDictionary<NSString *, NSString *> * _Nonnull)queryParams OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


/// TokenProvider protocol.
/// Conform to the TokenProvider protocol in order to generate the token for the user that you want to authenticate.
SWIFT_PROTOCOL("_TtP17PushNotifications13TokenProvider_")
@protocol TokenProvider
/// Method <code>fetchToken</code> will return the token on success or error on failure.
/// precondition:
/// <code>userId</code> should not be nil.
/// \param userId Id of the user that you want to generate the token for.
///
/// \param completion The block to execute when operation succeeds or fails.
///
- (BOOL)fetchTokenWithUserId:(NSString * _Nonnull)userId error:(NSError * _Nullable * _Nullable)error completionHandler:(void (^ _Nonnull)(NSString * _Nonnull, NSError * _Nullable))completion;
@end


/// Used to generate tokens for users to authenticate against.
SWIFT_CLASS("_TtC17PushNotifications18BeamsTokenProvider")
@interface BeamsTokenProvider : NSObject <TokenProvider>
/// Creates a <code>BeamsTokenProvider</code> instance.
/// \param authURL The authentication endpoint URL <code>String</code>.
///
/// \param getAuthData A closure that returns an <code>AuthData</code> object.
///
- (nonnull instancetype)initWithAuthURL:(NSString * _Nonnull)authURL getAuthData:(AuthData * _Nonnull (^ _Nonnull)(void))getAuthData OBJC_DESIGNATED_INITIALIZER;
/// Fetch a token for a given user to authenticate against.
/// \param userId The user ID <code>String</code>.
///
/// \param completion A closure containing a valid token <code>String</code> or an <code>Error</code>.
///
- (BOOL)fetchTokenWithUserId:(NSString * _Nonnull)userId error:(NSError * _Nullable * _Nullable)error completionHandler:(void (^ _Nonnull)(NSString * _Nonnull, NSError * _Nullable))completion;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


/// InterestsChangedDelegate protocol.
/// Method <code>interestsSetOnDeviceDidChange(interests:)</code> will be called when interests set changes.
SWIFT_PROTOCOL("_TtP17PushNotifications24InterestsChangedDelegate_")
@protocol InterestsChangedDelegate
/// Tells the delegate that the device’s interests subscriptions list has changed.
/// \param interests The new list of interests.
///
- (void)interestsSetOnDeviceDidChangeWithInterests:(NSArray<NSString *> * _Nonnull)interests;
@end

@class PushNotificationsStatic;
@class NSData;
enum RemoteNotificationType : NSInteger;

/// The top-level entrypoint to the Beams Swift SDK.
SWIFT_CLASS("_TtC17PushNotifications17PushNotifications")
@interface PushNotifications : NSObject
/// Creates a <code>PushNotifications</code> object with a given <code>instanceId</code>.
/// \param instanceId The instance identifier (from your app dashboard).
///
- (nonnull instancetype)initWithInstanceId:(NSString * _Nonnull)instanceId OBJC_DESIGNATED_INITIALIZER;
/// Returns a shared singleton PushNotifications object
/// that can be accessed from anywhere in your project.
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly) SWIFT_METATYPE(PushNotificationsStatic) _Nonnull shared;)
+ (SWIFT_METATYPE(PushNotificationsStatic) _Nonnull)shared SWIFT_WARN_UNUSED_RESULT;
/// Start PushNotifications service.
- (void)start;
/// Register to receive remote notifications via Apple Push Notification service.
/// Convenience method is using <code>.alert</code>, <code>.sound</code>, and <code>.badge</code> as default authorization options.
/// seealso:
/// <code>registerForRemoteNotifications(options:)</code>
- (void)registerForRemoteNotifications;
/// Register to receive remote notifications via Apple Push Notification service.
/// \param options The authorization options your app is requesting. You may combine the available constants to request authorization for multiple items. Request only the authorization options that you plan to use. For a list of possible values, see <a href="https://developer.apple.com/documentation/usernotifications/unauthorizationoptions">UNAuthorizationOptions</a>.
///
- (void)registerForRemoteNotificationsWithOptions:(UNAuthorizationOptions)options;
/// Set user id.
/// \param userId User id.
///
/// \param tokenProvider Token provider that will be used to generate the token for the user that you want to authenticate.
///
/// \param completion The block to execute after attempt to set user id has been made.
///
- (void)setUserId:(NSString * _Nonnull)userId tokenProvider:(id <TokenProvider> _Nonnull)tokenProvider completion:(void (^ _Nonnull)(NSError * _Nullable))completion;
/// Disable Beams service.
/// This will remove everything associated with the Beams from the device and Beams server.
/// \param completion The block to execute after the device has been deleted from the server.
///
- (void)stopWithCompletion:(void (^ _Nonnull)(void))completion;
/// Clears all the state on the SDK leaving it in the empty started state.
/// This will remove the current user and all the interests associated with it from the device and Beams server.
/// Device is now in a fresh state, ready for new subscriptions or user being set.
/// \param completion The block to execute after the device has been deleted from the server.
///
- (void)clearAllStateWithCompletion:(void (^ _Nonnull)(void))completion;
/// Register device token with PushNotifications service.
/// precondition:
/// <code>deviceToken</code> should not be nil.
/// \param deviceToken A token that identifies the device to APNs.
///
- (void)registerDeviceToken:(NSData * _Nonnull)deviceToken;
/// Subscribes the device to an interest.
/// precondition:
/// <code>interest</code> should not be nil.
/// \param interest Interest that you want to subscribe your device to.
///
///
/// throws:
/// An error of type <code>InvalidInterestError</code>
- (BOOL)addDeviceInterestWithInterest:(NSString * _Nonnull)interest error:(NSError * _Nullable * _Nullable)error;
/// Sets the subscriptions state for the device.
/// Any interests not in the set will be unsubscribed from, so this will replace the interest set by the one provided.
/// precondition:
/// <code>interests</code> should not be nil.
/// \param interests Interests that you want to subscribe your device to.
///
///
/// throws:
/// An error of type <code>MultipleInvalidInterestsError</code>
- (BOOL)setDeviceInterestsWithInterests:(NSArray<NSString *> * _Nonnull)interests error:(NSError * _Nullable * _Nullable)error;
/// Unsubscribe the device from an interest.
/// precondition:
/// <code>interest</code> should not be nil.
/// \param interest Interest that you want to unsubscribe your device from.
///
///
/// throws:
/// An error of type <code>InvalidInterestError</code>
- (BOOL)removeDeviceInterestWithInterest:(NSString * _Nonnull)interest error:(NSError * _Nullable * _Nullable)error;
/// Unsubscribes the device from all the interests.
- (BOOL)clearDeviceInterestsAndReturnError:(NSError * _Nullable * _Nullable)error;
/// Get the interest subscriptions that the device is currently subscribed to.
///
/// returns:
/// Array of interests
- (NSArray<NSString *> * _Nullable)getDeviceInterests SWIFT_WARN_UNUSED_RESULT;
/// Handle Remote Notification.
/// \param userInfo Remote Notification payload.
///
- (enum RemoteNotificationType)handleNotificationWithUserInfo:(NSDictionary * _Nonnull)userInfo;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end


/// A static equivalent of the <code>PushNotifications</code> type.
SWIFT_CLASS("_TtC17PushNotifications23PushNotificationsStatic")
@interface PushNotificationsStatic : NSObject
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
/// Start PushNotifications service.
/// precondition:
/// <code>instanceId</code> should not be nil.
/// \param instanceId PushNotifications instance id.
///
+ (void)startWithInstanceId:(NSString * _Nonnull)instanceId;
/// Register to receive remote notifications via Apple Push Notification service.
/// Convenience method is using <code>.alert</code>, <code>.sound</code>, and <code>.badge</code> as default authorization options.
/// seealso:
/// <code>registerForRemoteNotifications(options:)</code>
+ (void)registerForRemoteNotifications;
/// Register to receive remote notifications via Apple Push Notification service.
/// \param options The authorization options your app is requesting. You may combine the available constants to request authorization for multiple items. Request only the authorization options that you plan to use. For a list of possible values, see <a href="https://developer.apple.com/documentation/usernotifications/unauthorizationoptions">UNAuthorizationOptions</a>.
///
+ (void)registerForRemoteNotificationsWithOptions:(UNAuthorizationOptions)options;
/// Set user id.
/// \param userId User id.
///
/// \param tokenProvider Token provider that will be used to generate the token for the user that you want to authenticate.
///
/// \param completion The block to execute after attempt to set user id has been made.
///
+ (void)setUserId:(NSString * _Nonnull)userId tokenProvider:(id <TokenProvider> _Nonnull)tokenProvider completion:(void (^ _Nonnull)(NSError * _Nullable))completion;
/// Disable Beams service.
/// This will remove everything associated with the Beams from the device and Beams server.
/// \param completion The block to execute after the device has been deleted from the server.
///
+ (void)stopWithCompletion:(void (^ _Nonnull)(void))completion;
/// Clears all the state on the SDK leaving it in the empty started state.
/// This will remove the current user and all the interests associated with it from the device and Beams server.
/// Device is now in a fresh state, ready for new subscriptions or user being set.
/// \param completion The block to execute after the device has been deleted from the server.
///
+ (void)clearAllStateWithCompletion:(void (^ _Nonnull)(void))completion;
/// Register device token with PushNotifications service.
/// precondition:
/// <code>deviceToken</code> should not be nil.
/// \param deviceToken A token that identifies the device to APNs.
///
+ (void)registerDeviceToken:(NSData * _Nonnull)deviceToken;
/// Subscribes the device to an interest.
/// precondition:
/// <code>interest</code> should not be nil.
/// \param interest Interest that you want to subscribe your device to.
///
///
/// throws:
/// An error of type <code>InvalidInterestError</code>
+ (BOOL)addDeviceInterestWithInterest:(NSString * _Nonnull)interest error:(NSError * _Nullable * _Nullable)error;
/// Sets the subscriptions state for the device.
/// Any interests not in the set will be unsubscribed from, so this will replace the interest set by the one provided.
/// precondition:
/// <code>interests</code> should not be nil.
/// \param interests Interests that you want to subscribe your device to.
///
///
/// throws:
/// An error of type <code>MultipleInvalidInterestsError</code>
+ (BOOL)setDeviceInterestsWithInterests:(NSArray<NSString *> * _Nonnull)interests error:(NSError * _Nullable * _Nullable)error;
/// Unsubscribe the device from an interest.
/// precondition:
/// <code>interest</code> should not be nil.
/// \param interest Interest that you want to unsubscribe your device from.
///
///
/// throws:
/// An error of type <code>InvalidInterestError</code>
+ (BOOL)removeDeviceInterestWithInterest:(NSString * _Nonnull)interest error:(NSError * _Nullable * _Nullable)error;
/// Unsubscribes the device from all the interests.
+ (BOOL)clearDeviceInterestsAndReturnError:(NSError * _Nullable * _Nullable)error;
/// Get the interest subscriptions that the device is currently subscribed to.
///
/// returns:
/// Array of interests
+ (NSArray<NSString *> * _Nullable)getDeviceInterests SWIFT_WARN_UNUSED_RESULT;
/// Handle Remote Notification.
/// \param userInfo Remote Notification payload.
///
+ (enum RemoteNotificationType)handleNotificationWithUserInfo:(NSDictionary * _Nonnull)userInfo;
@end

/// Remote Notification Type provides an option to ignore Pusher initiated related features.
/// Whenever you receive push notification the <a href="https://docs.pusher.com/beams/reference/ios#handle-notification">handleNotification(userInfo:)</a> method should be called.
/// Sometimes, these notifications are just for Pusher SDK to handle.
/// <em>Values</em>
/// <code>ShouldIgnore</code> It’s safe to ignore Pusher initiated notification.
/// <code>ShouldProcess</code> Do not ignore notification as it may contain additional data.
typedef SWIFT_ENUM(NSInteger, RemoteNotificationType, closed) {
/// Ignore Pusher initiated notification.
  RemoteNotificationTypeShouldIgnore = 0,
/// Do not ignore notification.
  RemoteNotificationTypeShouldProcess = 1,
};


#endif
#if defined(__cplusplus)
#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#pragma clang diagnostic pop
#endif

#else
#error unsupported Swift architecture
#endif
