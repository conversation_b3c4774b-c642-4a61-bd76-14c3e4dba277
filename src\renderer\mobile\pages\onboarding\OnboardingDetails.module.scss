.onboardingDetailsMain {
    padding: 0px 24px 0px;
    position: relative;
    height: 100%;

    .onboardingLogo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 25px;
        pointer-events: none;

        img {
            width: 243px;
            height: 86.4px;
        }
    }

    .onboardingForm {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        position: relative;
        z-index: 99;
        width: 100%;

        .emailErrorContainer {
            position: relative;

            .errorBorder {
                position: absolute;
                right: -10px;
                top: 140px;
                border-radius: 3px;
                border: solid 2px #f00;
                width: 11px;
                height: 60px;
                border-left: 0;

                svg {
                    position: relative;
                    top: 19px;
                    right: -4px;
                    height: 12px;
                    width: 12px;
                }
            }
        }

        .passwordErrorContainer {
            position: relative;
            .confirmPasswordInput{
                position: relative;

                &:focus-within{
                    svg{
                        path{
                                fill: #000;
                        }
                    }
                }

                .showPassBtn{
                    padding: 0px;
                    display: flex;
                    position: absolute;
                    right: 10px;
                    top:9px;
                    &.errorPassIcon{
                            svg{
                                path{
                                        fill: #000;
                                }
                            }
                        }
                    }
            }
            .errorBorder {
                position: absolute;
                right: -10px;
                top: 19px;
                border-radius: 3px;
                border: solid 2px #f00;
                width: 11px;
                height: 60px;
                border-left: 0;

                svg {
                    position: relative;
                    top: 19px;
                    right: -4px;
                    height: 12px;
                    width: 12px;
                }
            }
        }

        .companyNameInput {
            margin-top: 20px;

            &.error input {
                border: solid 2px #f00;
                background-color: rgba(255, 255, 255, 0.75);
                color: rgba(0, 0, 0, 0.75);

                &:focus {
                    border: solid 2px #f00;
                    background-color: rgba(255, 255, 255, 0.75);
                }
            }

            input {
                &::placeholder {
                    font-weight: 300;
                    color: #fff;
                }

                &[aria-expanded="true"] {
                    background-color: #d9d9d9;
                    border-radius: 4px 4px 0px 0px;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
                    font-family: Noto Sans Display;
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 1.6;
                    text-align: left;
                    color: rgba(0, 0, 0, 0.75);
                }

                &:focus {
                    outline: none;
                    background-color: rgba(255, 255, 255, 0.75);
                    font-weight: 600;
                    color: rgba(0, 0, 0, 0.75);

                    &::placeholder {
                        font-weight: 600;
                        color: rgba(0, 0, 0, 0.75);
                    }
                }
            }
        }

        .inputOnboarding {
            width: 100%;
            height: 40px;
            margin: 20px 0 0 0;
            font-family: Noto Sans Display;
            font-size: 14px;
            font-weight: 300;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            padding: 10px 12px;
            border-radius: 4px;
            border: solid 0.5px #fff;
            display: flex;

            &.error {
                border: solid 2px #f00;
                background-color: rgba(255, 255, 255, 0.75);
                color: rgba(0, 0, 0, 0.75);

                &::placeholder {
                    color: rgba(0, 0, 0, 0.75);
                }

                &:focus {
                    border: solid 2px #f00;
                    background-color: rgba(255, 255, 255, 0.75);
                }
            }

            &.inputOnboarding1 {
                padding: 0px;
                border: 0px;
                border-radius: 0px;

                .input1 {
                    background-color: transparent;
                    border: 0px;
                    height: 100%;
                    font-family: Noto Sans Display;
                    font-size: 14px;
                    font-weight: 300;
                    line-height: 1.4;
                    text-align: left;
                    color: #fff;
                    padding: 10px 12px;
                    border-radius: 4px;
                    border: solid 0.5px #fff;
                    width: 50%;

                    &::placeholder {
                        opacity: 0.5;
                    }


                    &.error.error {
                        border: solid 2px #f00;
                        background-color: rgba(255, 255, 255, 0.75);
                        color: rgba(0, 0, 0, 0.75);

                        &:focus {
                            border: solid 2px #f00;
                            background-color: rgba(255, 255, 255, 0.75);
                        }
                    }


                    &:focus {
                        outline: none;
                        background-color: rgba(255, 255, 255, 0.75);
                        font-weight: 600;
                        color: rgba(0, 0, 0, 0.75);

                        &::placeholder {
                            font-weight: 600;
                            color: rgba(0, 0, 0, 0.75);
                        }
                    }
                }
            }

            &::placeholder {
                color: #fff;
                font-weight: 300;
            }

            &:focus {
                outline: none;
                border: solid 0.5px #fff;
                background-color: rgba(255, 255, 255, 0.75);
                font-weight: 600;
                color: rgba(0, 0, 0, 0.75);

                &::placeholder {
                    font-weight: 600;
                    color: rgba(0, 0, 0, 0.75);
                }
            }
        }

        .inputOnboarding1 {
            input {
                width: 100%;
                height: 40px;
                font-family: Noto Sans Display;
                font-size: 14px;
                font-weight: 600;
                line-height: 1.4;
                text-align: left;
                color: #fff;
                padding: 10px 12px;
                border-radius: 4px;
                border: solid 0.5px #fff;
                background-color: transparent;
                display: flex;
                &::placeholder{
                    opacity: 0.5;
                    font-weight: 300;
                }
            }
        }

        .inputOnboarding2 {
            margin-top: 20px;

            input {
                width: 100%;
                height: 40px;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                padding: 10px 12px;
                border-radius: 4px;
                border: solid 0.5px #fff;
                background-color: transparent;
                font-family: Noto Sans Display;
                font-size: 14px;
                font-weight: 600;
                line-height: 1.4;
                text-align: left;
                color: #fff;
            
                &:focus {
                    outline: none;
                }

                &::placeholder {
                    color: #fff;
                    opacity: 0.5;
                    font-weight: 300;
                  
                }
            }

            .error {
                border: solid 1px #f00;
                color: rgba(0, 0, 0, 0.75);
                background-color: #fff;

                &:focus {
                    border: solid 1px #f00;
                }
            }
        }

    }

    .btnSection {
        display: flex;
        align-items: center;
        justify-content: center;

        .nextBtn {
            width: 100%;
            height: 40px;
            // padding: 12px 20px;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.1);
            font-family: Noto Sans Display;
            font-size: 16px;
            font-weight: 300;
            line-height: 1.4;
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 20px;

            &.enableBtn {
                font-family: Noto Sans Display;
                font-weight: 600;
                color: rgba(0, 0, 0, 0.75);
                background-color: #70ff00;
                position: relative;
                z-index: 99;
            }

            &[disabled] {
                cursor: not-allowed;
            }
        }
    }

    .hideInput {
        filter: blur(4px);
    }

    .loginBtnSection {
        margin-top: 28px;
        opacity: 0.5;
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: 300;
        line-height: 1.4;
        text-align: center;
        color: #fff;

        button {
            cursor: pointer;
            background-color: transparent;
            padding: 0px;
            border: 0px;
            font-family: Noto Sans Display;
            font-size: 14px;
            line-height: 1.4;
            font-weight: normal;
            color: #fff;
        }
    }
}

.Dropdownpaper.Dropdownpaper {
    box-shadow: 0 0 25px 0 rgba(0, 0, 0, 0.5);
    background-color: #dadada;
    overflow: hidden;
    border-radius: 0px 0px 4px 4px;

    ul {
        overflow: auto;
        max-height: 250px;
        padding-right: 0px;
        padding-top: 0px;
        padding-bottom: 6px;

        li {
            font-family: Noto Sans Display;
            font-size: 14px;
            font-weight: 300;
            line-height: 1.6;
            text-align: left;
            color: rgba(0, 0, 0, 0.75);
            padding: 6px 12px;
            margin-bottom: 4px;
            width: calc(100% - 20px);
            margin-left: auto;
            margin-right: auto;
            min-height: 40px;

            &:first-child {
                min-height: 27px;
                padding: 4px 10px 4px 10px;
                background-color: rgba(0, 0, 0, 0.75);
                font-size: 12px;
                font-weight: 600;
                line-height: 1.6;
                text-align: center;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 0;
                width: 100%;
                margin-bottom: 8px;
            }


            &[aria-selected="true"] {
                border-radius: 4px;
                box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.25);
                background-color: rgba(255, 255, 255, 0.75);
                font-weight: 600;
            }
        }
    }
}

.autocompleteDescPanel {
    border-radius: 4px;
}

.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    background: #d9d9d9;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center;
    border-radius: 0px 0px 4px 4px;
    padding: 0px 8px 0px 10px;

    .listAutoComletePanel.listAutoComletePanel {
        width: 100%;
        padding-right: 8px;
        margin: 6px 0px;
        padding: 0px 8px 0px 0px;
        max-height: 343px;

        &::-webkit-scrollbar {
            width: 8px;
            height: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.3);
            ;
            border-radius: 40px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        span {
            font-family: Noto Sans Display;
            font-size: 16px;
            font-weight: 300;
            line-height: 1.6;
            text-align: left;
            color: rgba(0, 0, 0, 0.75);
            min-height: 46px;
            padding: 0px 10px;
            border-radius: 4px;

            &[aria-selected="true"] {
                font-weight: 600;
                background-color: rgba(255, 255, 255, 0.75);
                box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.25);
            }
        }
    }
}

input{
    &[disabled]{
     opacity: 0.5;
    }
}