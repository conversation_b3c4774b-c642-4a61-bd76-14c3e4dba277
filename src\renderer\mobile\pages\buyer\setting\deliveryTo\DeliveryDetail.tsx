// @ts-nocheck
import { IonPage, IonContent, useIonRouter, useIonViewWillEnter, useIonViewWillLeave, useIonLoading, BackButtonEvent, useIonViewDidEnter } from '@ionic/react';
import styles from './DeliveryDetail.module.scss';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { deliveryDetailSchema } from '../SettingSchema';
import { useEffect, useRef, useState } from 'react';
import { CustomMenu } from '../../../../components/CustomMenu';
import { buyerSettingPayLoadFormatter, checkStateZipValidation, emojiRemoverRegex, formatPhoneNumberWithCountryCode, saveUserSetting } from '../../../../library/helper';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import axios from 'axios';
import clsx from 'clsx';
import useBuyerSettingStore, { DeliveryDetails } from '../BuyerSettingStore';
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import useDialogStore from '../../../../components/Dialog/DialogStore';
import { mobileDiaglogConst, buyerSettingConst } from '../../../../library/common';
import { Toast } from '@capacitor/toast';

const DeliveryDetail = () => {

    
    const [states, setStates] = useState([]);
    const [deliveryDates, setDeliveryDates] = useState([]);
    const [isSaveDisable, setIsSaveDisable] = useState(true);
    const [validationInProgress, setValidationInProgress] = useState(true);
    const {userData, setShowLoader, referenceData} = useGlobalStore();
    const { deliveryInfo, setDeliveryInfo, buyerSetting, setBuyerSettingInfo } = useBuyerSettingStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();

    const addressRef = useRef<HTMLIonInputElement>();

    const {
        register,
        handleSubmit,
        getValues,
        clearErrors,
        setValue,
        watch,
        setError,
        reset,
        control,
        formState: { errors, dirtyFields, isDirty, isValid } } = useForm({
            resolver: yupResolver(deliveryDetailSchema)
        });    

    const { ref, ...rest } = register("deliverToAddress");

    const backToSetting = async () => {
        if(isDirty)
        showCommonDialog(mobileDiaglogConst.unsavedChangesMsg,[{name: mobileDiaglogConst.stay, action: resetDialogStore},{name: mobileDiaglogConst.goBackToSetting, action: routeBackToSetting}]);
        else
        routeBackToSetting();
    }

    useEffect(() => {
        if (isDirty)
            setIsSaveDisable(false);

        // const handleBackButton = (ev: BackButtonEvent) => {
        //     ev.detail.register(10, async () => {
        //         backToSetting();
        //     });
        // };

        // document.addEventListener('ionBackButton', handleBackButton);
        // return () => {
        //     document.removeEventListener('ionBackButton', handleBackButton);
        // };
    }, [isDirty])

    useIonViewWillEnter(() => {
        if (deliveryInfo) {
            setValue('deliverToAddress', deliveryInfo.delivery_address_line1);
            setValue('deliverToCity', deliveryInfo.delivery_address_city);
            setValue('deliverToState', deliveryInfo.delivery_address_state_id);
            setValue('deliverZipCode', deliveryInfo.delivery_address_zip);
            setValue('deliveryDate', deliveryInfo.delivery_days_add_value);
            setValue('sendInvoicesTo', deliveryInfo.send_invoices_to);
            setValue('shippingDocsTo', deliveryInfo.shipping_docs_to);

            clearErrors();
        }
    }, [deliveryInfo])

    useIonViewDidEnter(() => {
        addressRef.current?.focus();
    }, []);

    useEffect(()=>{
        if (referenceData){
            setStates(referenceData.ref_states);
            setDeliveryDates(referenceData.ref_delivery_date);
        } 
    },[referenceData])

    useEffect(() => {
        if (isDirty) {
            handleStateZipValidation('deliverZipCode', 'deliverToState')
        }
    }, [watch('deliverZipCode'), watch('deliverToState')])

    const showStateZipError = ()=> {
        setError("deliverZipCode", { message: "The zip code and state code do not match" });
        setError("deliverToState", { message: "The zip code and state code do not match" });
    }

    const handleStateZipValidation = async (zipCode, stateCode) => {
        setValidationInProgress(false)
        const checkStateZipResponse = await checkStateZipValidation(getValues(zipCode), getValues(stateCode))
        if (checkStateZipResponse?.data.data === true) {
            clearErrors(["deliverZipCode","deliverToState"]);
        } else {
            showStateZipError();
        }
        setValidationInProgress(true)
    };

    const router = useIonRouter()
    const [present, dismiss] = useIonLoading()

    useIonViewWillLeave(()=>{
        resetDialogStore();
        setIsSaveDisable(true);
        reset();
    })

    const onSubmit = async(data) => {
        setShowLoader(true)
        const detail : DeliveryDetails = {
            delivery_address_line1: data.deliverToAddress, 
            delivery_address_city: data.deliverToCity, 
            delivery_address_state_id: data.deliverToState, 
            delivery_address_zip: data.deliverZipCode,
            delivery_days_add_value: data.deliveryDate, 
            send_invoices_to: data.sendInvoicesTo, 
            shipping_docs_to: data.shippingDocsTo
        }   
        // const _buyerSetting = {...buyerSetting, ...detail};
        // const payload: any = buyerSettingPayLoadFormatter(_buyerSetting);
        try{
            await saveUserSetting(buyerSettingConst.apiRoutesForSave.deliveryInfo,detail, userData);
            // setBuyerSettingInfo(_buyerSetting);
            setDeliveryInfo(detail);
            router.push('/setting',{animate:true,direction:'forward'});
            setShowLoader(false);
        }catch(err){
            setShowLoader(false);
        }
    }

    const handleFormSubmit = () => {
        if (Object.keys(errors).length === 0 && validationInProgress) {
            handleSubmit(onSubmit)();
        } else {
            return;
        }
    }

    const routeBackToSetting = ()=>{
        router.push('/setting',{animate:true,direction:'forward'});
        resetDialogStore();
    }


    return (
        <IonPage>
            <IonContent>
                <div className={styles.profile}>
                    <h2 className={styles.heading}><BackBtnArrowIcon onClick={backToSetting}/><span>Deliver To</span></h2>
                    <div className={styles.settingsContent}>
                        <div className={styles.delivertoComponent}>
                            <label>Address</label>
                            <input type="text" className={clsx(styles.inputField, errors?.deliverToAddress?.message && styles.errorMsg)} {...rest} placeholder="Address"
                                ref={(e) => { ref(e); addressRef.current = e; }}
                                onBlur={(e) => {
                                    e.target.value = e.target.value.trim();
                                    rest.onBlur(e);
                                }} />
                        </div>
                        <div className={styles.delivertoComponent}>
                            <label > City </label>
                            <input type="text" className={clsx(styles.inputField, errors?.deliverToCity?.message && styles.errorMsg)} {...register("deliverToCity")} placeholder="City"
                                onBlur={(e) => {
                                    e.target.value = e.target.value.trim();
                                    register("deliverToCity").onBlur(e);
                                }} />
                        </div>
                        
                        <div className={styles.stateDropdown}>
                            <div className={styles.grid1}>
                                <label>State </label>
                                <span className={(errors?.deliverToState?.message && 'errorMsgDropdown')}>
                                    <CustomMenu
                                        control={control}
                                        name={'deliverToState'}
                                        placeholder={'State'}
                                        MenuProps={{
                                            classes: {
                                                paper: styles.Dropdownpaper,
                                                list: styles.muiMenuList,
                                            },
                                        }}
                                        items={states?.map(x => ({ title: x.code, value: x.id }))}
                                        className={`selectDropdown selectState ${styles.deliverInput}`}
                                    />
                                </span>
                            </div>
                            <div className={styles.grid1}>
                                <label >Zip </label>
                                <input type='tel' {...register("deliverZipCode")} onChange={(e) => {
                                    register("deliverZipCode").onChange(e)
                                    const zipCode = e.target.value.replace(/\D/g, '');
                                    setValue('deliverZipCode', zipCode);
                                }} maxLength="5" placeholder='Zip Code'
                                className={clsx(styles.inputField, errors?.deliverZipCode?.message && styles.errorMsg)}
                                />
                            </div>
                        </div>
                        <div className={styles.sendInvoiceInputMain}>
                            <label > Delivery Date </label>
                            <span className={(errors?.deliveryDate?.message && 'errorMsgDropdown')}>
                            <CustomMenu
                                control={control}
                                name={'deliveryDate'}
                                placeholder={'Choose One'}
                                MenuProps={{
                                    classes: {
                                        paper: clsx(styles.Dropdownpaper, styles.DropdownDeliveryDate),
                                        list: styles.muiMenuList,
                                    },
                                }}
                                items={deliveryDates.map(x => ({ title: x.delivery_date_string, value: x.days_to_add }))}
                                className={clsx(`selectDropdown ${styles.deliverInput}`)}

                            />
                            </span>
                           
                        </div>
                        <div className={styles.sendInvoiceInputMain}>
                            <label> Send Invoices to </label>
                            <textarea {...register("sendInvoicesTo")} type='text' placeholder='Enter AP email address (multiple separate with a comma)'
                                onBlur={(e) => {
                                    e.target.value = e.target.value.trim();
                                    register("sendInvoicesTo").onBlur(e);
                                }}
                                className={clsx(styles.sendInvoiceInput, errors?.sendInvoicesTo?.message && styles.errorMsg )}
                            ></textarea>
                        </div>
                        <div className={styles.sendInvoiceInputMain}>
                            <label> Shipping Docs to </label>
                            <textarea type='text' {...register("shippingDocsTo")} placeholder='Enter email address (multiple separate with a comma)'
                                className={clsx(styles.sendInvoiceInput, errors?.shippingDocsTo?.message && styles.errorMsg )}
                                onBlur={(e) => {
                                    e.target.value = e.target.value.trim();
                                    register("shippingDocsTo").onBlur(e);
                                }}></textarea>
                        </div>
                    </div>

                    <div className={styles.btnSection}>
                        <button className={styles.saveBtn} onClick={() => handleFormSubmit()} disabled={isSaveDisable}>Save</button>
                    </div>
                </div>
            </IonContent>
        </IonPage>
    )
}

export default DeliveryDetail;