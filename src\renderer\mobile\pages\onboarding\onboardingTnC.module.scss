.onboardingMobTnCMain {
    padding: 12px 24px 10px 24px;

    .onboarding<PERSON>ogo {
        width: 243px;
        height: 86.4px;
        margin-bottom: 24px;
        margin-left: auto;
        margin-right: auto;
    }

    .onboardingTncBox {
        width: 100%;
        height: 293px;
        margin: 0px 0 20px;
        padding: 16px 12px 16px 16px;
        border-radius: 4px;
        border: solid 0.6px #fff;

        .tncScrollClass {
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            max-height: 249px;

            &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
            }

            &::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.8);
                border-radius: 50px;
            }

            &::-webkit-scrollbar-track {
                background: transparent;
            }

            .termsofUseV1 {
                font-family: Noto Sans Display;
                font-size: 18px;
                font-weight: bold;
                line-height: 1.4;
                text-align: left;
                color: #fff;
                margin-top: 0px;
            }

            p {
                margin: 16px 1px 16px 0;
                font-family: Noto Sans Display;
                font-size: 14px;
                font-weight: 300;
                line-height: 1.4;
                text-align: left;
                color: #fff;
            }
        }
    }
    
    label{
        margin-bottom: 0px;
    }

    .GetStartedbtn {
        width: 100%;
        height: 40px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        font-family: Noto Sans Display;
        font-size: 14px;
        line-height: 1.4;
        text-align: center;
        background-color: #70ff00;
        font-family: Noto Sans Display;
        font-weight: 500;
        color: #000;
        margin-top: 24px;
        &[disabled]{
            background-color: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            font-weight: 300;
        }
    }


}

.onboardingTnCBody {

    .GetStartedbtn {
        background-color: #70ff00;
        font-family: Noto Sans Display;
        font-weight: 600;
        color: #000;
    }

    .submitBtn {
        height: 40px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 10px 24px;
        border-radius: 4px;
        border: solid 0.5px #fff;
        background-color: transparent;
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.4;
        text-align: center;
        color: #fff;
        margin-top: 20px;
        transition: all 0.1s;

        &:hover {
            background-color: #70ff00;
            border: solid 0.5px #70ff00;
            color: #000;
        }

        &:disabled {
            opacity: 0.5;
            cursor: not-allowed;

            &:hover {
                border: solid 0.5px #fff;
                background-color: transparent;
                color: #fff;
            }
        }
    }
}