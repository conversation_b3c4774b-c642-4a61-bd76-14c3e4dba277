export type ProductQtyType = 'cwt'| 'ft' | 'lb' | 'cwt,ft'

export type ProductPricingModel = {
    id: number,
    UI_Description: string,
    cwt_price: string,
    ft_price: string,
    lb_price: string,
    product_type_pipe: boolean,
    line_session_id: string,
    "cwt,ft_price"?: string,
    is_safe_product_code: boolean
}

export type SearchAnalyticDataModel = {
    session_id: string,
    line_session_id: string,
    product_id: number,
    description: string,
    price_shared: boolean,
    price_share_unit?:  string,
    search_price_unit?: string
}

export type HttpRequestPayload<T> = {
    data: T
}