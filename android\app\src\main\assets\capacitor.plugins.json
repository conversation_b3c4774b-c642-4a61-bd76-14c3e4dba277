[{"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/device", "classpath": "com.capacitorjs.plugins.device.DevicePlugin"}, {"pkg": "@capacitor/filesystem", "classpath": "com.capacitorjs.plugins.filesystem.FilesystemPlugin"}, {"pkg": "@capacitor/haptics", "classpath": "com.capacitorjs.plugins.haptics.HapticsPlugin"}, {"pkg": "@capacitor/keyboard", "classpath": "com.capacitorjs.plugins.keyboard.KeyboardPlugin"}, {"pkg": "@capacitor/local-notifications", "classpath": "com.capacitorjs.plugins.localnotifications.LocalNotificationsPlugin"}, {"pkg": "@capacitor/network", "classpath": "com.capacitorjs.plugins.network.NetworkPlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}, {"pkg": "@capacitor/toast", "classpath": "com.capacitorjs.plugins.toast.ToastPlugin"}, {"pkg": "@capgo/capacitor-updater", "classpath": "ee.forgr.capacitor_updater.CapacitorUpdaterPlugin"}, {"pkg": "capacitor-plugin-safe-area", "classpath": "com.capacitor.safearea.SafeAreaPlugin"}]