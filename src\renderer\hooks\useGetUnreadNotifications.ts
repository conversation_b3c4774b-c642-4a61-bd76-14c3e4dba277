

import { useQuery } from "@tanstack/react-query";
import { reactQueryKeys } from "../../common";
import axios from "axios";

const useGetUnreadNotifications = (email_id: string | null) => {

    return useQuery(
        [reactQueryKeys.getUnreadNotifications],
        async () => {
           
                const { data } = await axios.get(import.meta.env.VITE_API_NOTIFICATION_SERVICE + '/notification/getAllUnreadNotification ', {
                    data: {
                        email_id
                    }
                })

              return data.data;
                
        },
        {
            enabled: !!email_id,
            retry: false,
            refetchOnWindowFocus: false,
            refetchOnMount: false,
        }
    );
};

export default useGetUnreadNotifications;
