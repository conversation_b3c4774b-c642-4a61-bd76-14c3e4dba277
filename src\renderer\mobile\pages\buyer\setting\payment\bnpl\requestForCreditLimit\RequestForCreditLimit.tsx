import clsx from "clsx";
import styles from "./RequestForCreditLimit.module.scss";
import { formatCurrency, formatCurrencyWithComma } from "../../../../../../library/helper";
import { Dialog } from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { buyerSettingConst } from "../../../../../../library/common";
import axios from "axios";
import { IonButton, IonButtons, IonModal, useIonLoading, useIonViewWillLeave } from "@ionic/react";
import { ReactComponent as CloseIcon } from '../../../../../../../assets/mobile-images/close_Popup.svg';
import { ReactComponent as CheckIcon } from '../../../../../../../assets/mobile-images/check_icon.svg';
import { mobileDiaglogConst, useGlobalStore } from '@bryzos/giss-ui-library';
import Loader from "../../../../../../components/Loader/Loader";
import useDialogStore from "../../../../../../components/Dialog/DialogStore";

const RequestForCreditLimit = (props: any) => {
    const [requestCreditPopup, setRequestCreditPopup] = useState(false);
    const [successPopup, setSuccessPopup] = useState(false);
    const modal = useRef<HTMLIonModalElement>(null);
    const requestCreditLineRef = useRef<HTMLIonInputElement>(null);
    const [present, dismiss] = useIonLoading();
    const { setShowLoader } = useGlobalStore();
    const [disable, setDisable] = useState(props.watch('requestCreditLine')?false:true);
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const [showLoaderFromHere, setShowLoaderFromHere] = useState(false);

    useIonViewWillLeave(()=>{
        setRequestCreditPopup(false);
        setSuccessPopup(false);
    },[])

    const submitRequestCreditLine = () => {
        const reqCreditLine = props.getValues('requestCreditLine');
        if (reqCreditLine > buyerSettingConst.buyerCreditLineLimit) {
            props.setError('requestCreditLine', { message: buyerSettingConst.creditLimitErrorMessage }, { shouldFocus: true });
            setDisable(true)
            return;
        }
        props.clearErrors();
        if (reqCreditLine > 0) {
            setShowLoaderFromHere(true);

            const payload = {
                "data": {
                    "request_increase_credit": reqCreditLine
                }
            }
            axios.post(import.meta.env.VITE_API_SERVICE + '/user/increaseCreditLimitRequest', payload)
                .then(res => {
                    if(res?.data?.data?.error_message){
                        showCommonDialog(res.data.data.error_message,[{name: mobileDiaglogConst.ok, action: resetDialogStore}]);
                        props.setValue('requestCreditLine', '')
                    }else{
                        setSuccessPopup(true)
                    }
                    setRequestCreditPopup(false);
                    setShowLoaderFromHere(false);
                })
                .catch((err: any) => {
                    setShowLoaderFromHere(false);
                    console.error(err);
                }
                );
        } else {
            props.setError('requestCreditLine', 'field is not vaild', { shouldFocus: true });
        }
    }

    const handleCreditRequestCancel = () => {
        axios.post(import.meta.env.VITE_API_SERVICE + `/user/cancelIncreaseCreditLimitRequest`)
            .then(res => {
                props.setValue("creditStatus", "Approved")
                props.setValue("creditLine", props.getValues("creditLimit"))
            })
            .catch(err => {
                console.error(err);
            });
    }

    useEffect(()=>{
        console.log("WATCH",props.watch('requestCreditLine'));
    },[props.watch('creditLine')])
   
    const onClosingRequestCreditLimit = ()=>{
        setRequestCreditPopup(false); 
        props.setValue('requestCreditLine','');
        props.clearErrors('requestCreditLine');
    }

    const focusRequestCreditLine = () => {
        const inputElement = document.getElementById("requestCreditLine");
        if (inputElement) {
            inputElement.focus();
        }
    }

    const handleDoneOnClick = () =>{
        props.setValue("creditStatus", "Pending Increase")
        props.setValue('creditLine', formatCurrency(parseFloat(props.getValues('requestCreditLine'))))
        props.setValue('requestCreditLine', '')
        setSuccessPopup(false)

    }

    return (
        <div>
            <div className={styles.requestcreditlineTopSection}>
                <label className={styles.containerRadio}>
                    <input type='checkbox' {...props.register("net30CheckBox")} value={props.net30Default} checked={props.selectedPaymentMethod === props.net30Default} onChange={(e) => { props.register("net30CheckBox").onChange(e); props.handlePaymentMethodChange(props.net30Default); props.setIsSaveDisable(false) }} />
                    <span>Net 30 Terms</span>
                    <span className={styles.checkmark} />
                </label>
                {props.isApproved === null ?
                    <span className={styles.pendingText}>Pending</span>
                    : props.getValues("creditStatus") === 'Pending Increase' ?
                        <button className={styles.requestcreditlineIncreaseText} onClick={handleCreditRequestCancel} >
                            Cancel
                        </button>
                        :
                        <button className={styles.requestcreditlineIncreaseText} onClick={(e) => setRequestCreditPopup(true)}>
                            Request a credit line increase
                        </button>
                }
            </div>
            <div className={styles.requestcreditlineInputGrid}>
            <div className="w100">
                    <label> D&B Number </label>
                    <div className={styles.lblCreditLine}>{props.getValues("dnBNumber")}</div>
                </div>
                <div className="w100">
                    <label> EIN </label>
                    <div className={styles.lblCreditLine}>{props.getValues("einNumber")}</div>
                </div>
            </div>

            <div className={styles.requestcreditlineInputGrid}>
            <div className="w100">
                    <label> Credit Status </label>
                    <div className={styles.lblCreditLine}>{props.getValues("creditStatus")}</div>
                </div>
                <div className="w100">
                    <label> Credit Line {props.isApproved === null && "(Requested)"}  </label>
                    <div className={clsx(styles.lblCreditLine,styles.pendingReqlbl)}>{props.getValues("creditLine")} <span>{props.getValues("creditStatus") === 'Pending Increase' && '(Pending)'}</span></div>
                </div>
            </div>

            <div className={styles.requestcreditlineInputGrid}>
                <div className="w100">
                    <label> Outstanding </label>
                    <div className={styles.lblCreditLine}>{props.getValues("outstandingAmount")}</div>
                </div>
                <div className="w100">
                    <label> Available </label>
                    <div className={styles.lblCreditLine}>{props.getValues("availableBalance")}</div>
                </div>
            </div>
           
           
            <IonModal className={'popupMain'} ref={modal} isOpen={requestCreditPopup} backdropDismiss={false} onIonModalDidPresent={focusRequestCreditLine}>
                <button className={styles.closePopupBtn} onClick={() => onClosingRequestCreditLimit()}><CloseIcon/></button>
                <RequestIncreaseLimit 
                        setValue={props.setValue}
                        watch={props.watch}
                        clearErrors={props.clearErrors}
                        errors={props.errors}
                        handleSubmit={props.handleSubmit}
                        requestCreditLineChangeHandler={props.requestCreditLineChangeHandler}
                        setRequestCreditPopup={setRequestCreditPopup}
                        submitRequestCreditLine={submitRequestCreditLine}
                        onClosingRequestCreditLimit={onClosingRequestCreditLimit}
                        showLoaderFromHere={showLoaderFromHere}
                        disable={disable}
                        setDisable={setDisable}
                    />
            </IonModal>

            <IonModal className={'popupMain'} ref={modal} isOpen={successPopup} backdropDismiss={false}>
                <button className={styles.closePopupBtn} onClick={() => handleDoneOnClick()}><CloseIcon/></button>
                <SuccessComponent
                    setValue={props.setValue}
                    getValues={props.getValues}
                    handleDoneOnClick={handleDoneOnClick}
                />
            </IonModal>

        </div>
    )
}

export default RequestForCreditLimit;

export const RequestIncreaseLimit = (props: any) =>{

    

    const onChangeOfReqCreditLine = (e:any)=>{
        const value = e.target.value;
        props.requestCreditLineChangeHandler(e, "requestCreditLine");
        if(value)props.setDisable(false)
        else props.setDisable(true)
    }

    return(
        <div className={styles.popupContent}>
            <Loader showLoader={props.showLoaderFromHere} />
            <h1 className={styles.requestingCreditlineincreaseTitle}>Requesting Credit Line Increase</h1>
            <div>
                <label className={styles.lblCreditLine}>Amount</label>
                <input type="tel" id="requestCreditLine" value={formatCurrencyWithComma(props.watch('requestCreditLine'))} onChange={(e) => onChangeOfReqCreditLine(e)} className={clsx(styles.requestCreditLineInput, props.errors.requestCreditLine && styles.borderOfError)} placeholder='Enter Amount' />
                {props.errors?.requestCreditLine &&
                    <p className={styles.errorText1}>{props.errors?.requestCreditLine?.message}</p>
                }
            </div>
            <div className={styles.btnSectionGrid}>
                <button onClick={(e) => { props.onClosingRequestCreditLimit() }} className={styles.cancelBtn}>Cancel</button>
                <button onClick={() => { props.submitRequestCreditLine() }} className={clsx(styles.submitBtn)} disabled={props.disable}>Submit</button>
            </div>
        </div>
    )
}
export const SuccessComponent = (props: any) =>{
    
    return(
        <div className={styles.reqSentSuccessPopup}>
            <span className="dflex"><CheckIcon/></span>
            <h1>Request Sent</h1>
            <button onClick={() => { props.handleDoneOnClick() }} className={clsx(styles.doneBtn)}>Done</button>
        </div>
    )
}