.yourOrder{
    padding: 16px 0px 16px 16px;
    height: calc(100% - 75px);
    display: flex;
    flex-direction: column;
    .firstDiv{
        padding-right: 16px;
    }
    .secondDiv{
        overflow: auto;
        padding-right: 16px;
    }
    .fontWeight500{
        font-weight: 500;
    }
    .heading{
        border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
        padding-bottom: 11.5px;
        opacity: 0.8;
        font-family: Noto Sans Display;
        font-size: 16px;
        font-weight: 300;
        line-height: 1.4;
        text-align: left;
        color: #fff;
    }
    .yourOrderList{
        padding: 10px 0px;
        border-bottom: 0.5px solid rgba(255, 255, 255, 0.3);
        width: 100%;
        .buyerPoName{
            font-family: Noto Sans Display;
            font-size: 14px;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            padding-bottom: 4px;
        }
        .yourorderDetails{
            width: 100%;
            opacity: 0.8;
            font-family: <PERSON><PERSON> Sans Display;
            font-size: 12px;
            font-weight: 300;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: normal;
            text-align: left;
            color: #fff;
            .yourOrderLine{
                display: flex;
                width: 100%;
                .fistLine{
                    width: 50%;
                }
                .secondLine{
                    width: 50%;
                    display: flex;
                    justify-content: space-between;
                }
            }
        }
    }
}