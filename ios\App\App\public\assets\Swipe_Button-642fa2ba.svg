<svg width="168" height="62" viewBox="0 0 168 62" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_bd_158_6655)">
<path d="M5 5H151.504C158.963 5 162.737 11.6953 162.737 18C162.737 24.6953 159.314 30.3863 152.908 31C159.27 32.1717 163 37.8627 163 44.279C163 50.1373 158.481 57 151.504 57L5 57V44.279L152.908 44.279V18H5V5Z" fill="url(#paint0_linear_158_6655)" fill-opacity="0.65" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_bd_158_6655" x="-24.2857" y="-24.2857" width="216.571" height="110.571" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="14.6429"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_158_6655"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.36538"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_158_6655" result="effect2_dropShadow_158_6655"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_158_6655" result="shape"/>
</filter>
<linearGradient id="paint0_linear_158_6655" x1="84" y1="5" x2="84" y2="57" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
