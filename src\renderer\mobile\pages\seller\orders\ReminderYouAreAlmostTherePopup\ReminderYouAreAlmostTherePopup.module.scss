.companyInput {
  width: 100%;
  height: 100%;
  padding: 6px 6px 6px 10px;
  color: #fff;
  resize: none;
  font-family: Noto Sans;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.4;
  text-align: left;

  &.errorInput1.errorInput1{
    border: 1px solid #ff0000;
  }

  &::placeholder {
    color: #bbb;
  }

  &:focus-within {
    border: solid 1px #70ff00;
    outline: none;
    box-shadow: none;
    border-radius: 0px 2px 2px 0px;
  }
}

.closePopupBtn {
  position: absolute;
  right: 9px;
  top: 9px;
  z-index: 99;
}

.reminderPopupMain {
  padding: 40px 10px 24px 20px;
  height: 100%;

  .innerContent {
    height: calc(100% - 150px);
    overflow: auto;
    padding-right: 10px;
  }

  .reminderPopupTitle {
    font-family: Noto Sans Display;
    text-align: left;
    line-height: 1.4;
    color: #fff;

    .titleText {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .titleSmallText {
      opacity: 0.8;
      font-size: 14px;
      font-weight: 300;
      color: #fff;
      margin-bottom: 20px;
    }

  }

  .lblInput {
    opacity: 0.7;
    font-family: Noto Sans Display;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
  }

  .inputField {
    width: 100%;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 10px 8px 10px 12px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 0px;
    margin-bottom: 16px;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    margin-top: 4px;

    &::placeholder {
      color: rgba(255, 255, 255, 0.5);
    }

    &:focus {
      outline: none;
    }
  }

  .errorMsg{
    border: 1px solid #ff0000;
    &:focus{
        border: 1px solid #ff0000;
    }
  }


  .stateDropdown {
    display: flex;
    gap: 12px;

    .grid1 {
      display: flex;
      flex-direction: column;
      flex-grow: 1;
      width: auto;
      max-width: 50%;
    }
  }


  .noteText {
    font-family: Noto Sans Display;
    font-size: 14px;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    margin-bottom: 24px;
  }

  .saveSettingsBtn {
    width: 100%;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 9px 0;
    border-radius: 8px;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    text-align: center;
    background-color: #70ff00;
    color: #000;

    &[disabled] {
      color: rgba(0, 0, 0, 0.75);
      background-color: rgba(255, 255, 255, 0.3);
    }
  }

  .companyInput {
    resize: none;
    width: 100%;
    height: 40px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    outline: none;
    margin-top: 4px;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;

    &::placeholder {
      color: #bbb;
    }
  }

}

.lblState {
  margin-bottom: 4px;
}

// Dropdown Style
.Dropdownpaper.Dropdownpaper {
  padding: 6px 4px 6px 12px;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2);
  background-color: #636363;
  overflow: hidden;
  border-radius: 0px 0px 4px 4px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;

  ul {
    overflow: auto;
    padding-right: 4px;
    padding-top: 0px;
    padding-bottom: 0px;
    max-height: 239px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 50px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    li {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      padding: 6px 12px;
      border-radius: 4px;
      min-height: 32px;

      &[aria-selected="true"] {
        background-color: #fff;
        color: #000;
      }
    }
  }
}



.autocompleteDescInnerPanel.autocompleteDescInnerPanel {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  -webkit-backdrop-filter: blur(24px);
  backdrop-filter: blur(24px);
  background-color: #ffffff4c;
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.2);
  padding-right: 4px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
  border-radius: 0px 0px 4px 4px;
  margin-top: 1px;
}

.listAutoComletePanel.listAutoComletePanel {
  width: 100%;
  max-height: 300px;
  padding: 6px 4px 6px 10px;
  margin-top: 4px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 20px;

  }

  li {
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    box-shadow: none;
    padding: 6px 8px;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    border-radius: 2px;
    min-height: 36px;

    &:hover {
      background-color: #fff;
      color: #000;
    }

    &[aria-selected="true"] {
      background-color: #fff !important;
      color: #000;
    }
  }
}
.submitPopupSection {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.returnSearchBtn {
  opacity: 0.8;
  font-family: Noto Sans;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.4;
  text-align: left;
  color: #fff;
}

.doneBtn {
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: center;
  padding: 10px 24px;
  border-radius: 8px;
  border: solid 1px rgba(255, 255, 255, 0.5);
  background-color: #fff;
  font-family: Noto Sans Display;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.4;
  text-align: center;
  color: #000;
  margin-top: auto;
}
.invitationSentSuccessfully {
  font-family: Noto Sans Display;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
  text-align: left;
  color: #70ff00;
  margin-bottom: 8px;
}

.errorMsg{
  border: 1px solid #ff0000;
  &:focus{
      border: 1px solid #ff0000;
  }
}

