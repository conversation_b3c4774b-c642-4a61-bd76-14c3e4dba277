// @ts-nocheck
import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import { getSocketConnection } from "@bryzos/giss-ui-library";

const useSaveSellerViewedOrder = () => {
  return useMutation(
    async (payload) => {
      try {
        const response = await axios.post(
          `${import.meta.env.VITE_API_SERVICE}/order/seller-viewed-order`,
          payload
        );

        getSocketConnection().emit('poPreviewRefresh', payload.data);

        if (response.data && response.data.data) {
          if (
            typeof response.data.data === "object" &&
            "err_message" in response.data.data
          ) {
            throw new Error(response.data.data.err_message);
          } else {
            return response.data.data;
          }
        } else {
          return null;
        }
      } catch (error) {
        throw new Error(error?.message ?? error);
      }
    },
    {
      retry: false,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );
};

export default useSaveSellerViewedOrder;
