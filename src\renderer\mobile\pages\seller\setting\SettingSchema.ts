// @ts-nocheck
import * as yup from "yup";
import { emailPattern } from "../../../library/helper";
const isEmail = (email) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}
export const profileSchema = yup.object({
  first_name:yup.string().trim().required('Name is not valid'),
  last_name:yup.string().trim().required('Name is not valid'),
  email_id:yup.string().trim().matches(emailPattern,'Email/Phone is not valid').required('Email/Phone is not valid').max(50, 'please do not enter more than 50 characters'),
  phone:yup.string().matches(/^\(\d{3}\) \d{3}-\d{4}$/, "Email/Phone is not valid").required("Phone number is required"),
})

export const companyInfoSchema = yup.object({
    companyName: yup.string().trim().required('Company Name is not valid'),
    yourCompany: yup.string().trim().required('Your Company is not valid'),
    companyAddressLine: yup.string().trim().required('Company Address is not valid'),
    companyCity:yup.string().trim().required('Company Address is not valid'),
    companyState:yup.number().required('Company Address is not valid'),
    companyZipCode:yup.string().required('Company Address is not valid').min(5,'Company Address is not valid'),
})

export const stockingLocationSchema = yup.object({
    stocking_location: yup.string().trim(),
    send_invoices_to: yup.string(),
    shipping_docs_to: yup.string(),
})

export const paymentSettingSchema = yup.object({
    fundingRadioBtn: yup.string(),
    wirePgpmMappingId: yup.string(),
    bankName: yup.string().trim()
    // .test("isRequired", "Wire is not valid", function (value) {
    //   const fundingRadioBtn = this.parent.fundingRadioBtn;
    //   const wirePgpmMappingId = this.parent.wirePgpmMappingId;
    //   if(fundingRadioBtn === wirePgpmMappingId) return true;
    //   return !!value;
    // })
    ,
    routingNumber: yup.string().trim()
    // .test("isRequired", "Wire is not valid", function(value) {
    //   const fundingRadioBtn = this.parent.fundingRadioBtn;
    //   const wirePgpmMappingId = this.parent.wirePgpmMappingId;
    //   if(fundingRadioBtn === wirePgpmMappingId) return true;
    //   if(!/^x{7}\d{2}$|^\d{9}$/.test(value)){
    //     return false
    //   }
    //   return !!value;
    // })
    ,
    accountNumber: yup.string().trim()
    // .test("isRequired", "Wire is not valid", function(value) {
    //   const fundingRadioBtn = this.parent.fundingRadioBtn;
    //   const wirePgpmMappingId = this.parent.wirePgpmMappingId;
    //   if(fundingRadioBtn === wirePgpmMappingId) return true;
    //   if(!/^x+\d{2}$|^\d+$/.test(value)){
    //     return false
    //   }
    //   return !!value;
    // })
    ,
    achCheckBox: yup.string(),
    achPgpmMappingId: yup.string(),
    bankName1: yup.string().trim()
    // .test("isRequired", "ACH Credit is not valid", function(value){
    //   const fundingRadioBtn = this.parent.fundingRadioBtn;
    //   const achPgpmMappingId = this.parent.achPgpmMappingId;
    //   if(fundingRadioBtn === achPgpmMappingId) return true;
    //   console.log("cehck bsnlkname ", value, !!value, fundingRadioBtn, achPgpmMappingId,fundingRadioBtn === achPgpmMappingId)
    //   return false;
    // })
    ,
    routingNo: yup.string()
    // .test("isRequired", "ACH Credit is not valid", function(value){
    //   const fundingRadioBtn = this.parent.fundingRadioBtn;
    //   const achPgpmMappingId = this.parent.achPgpmMappingId;
    //   if(fundingRadioBtn === achPgpmMappingId) return true;
    //     if(!/^x{7}\d{2}$|^\d{9}$/.test(value)){
    //     return false
    //   }
    //   return false;
    // })
    ,
    accountNo: yup.string()
    // .test("isRequired", "ACH Credit is not valid", function(value){
    //   const fundingRadioBtn = this.parent.fundingRadioBtn;
    //   const achPgpmMappingId = this.parent.achPgpmMappingId;
    //   if(fundingRadioBtn === achPgpmMappingId) return true;
    //   if(!/^x+\d{2}$|^\d+$/.test(value)){
    //     return false
    //   }
    //   return false;
    // })
})

export const documentInfoSchema = yup.object({
  w9_form_s3_url: yup.string(),
})

export const yourProductsSchema = yup.object({
  products_s3_url: yup.string(),
})
