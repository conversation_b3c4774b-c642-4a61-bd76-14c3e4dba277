// @ts-nocheck
import React from "react";
import { routes } from "../../common";
import { Auth } from "aws-amplify";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useEffect, useState } from "react";
import { OTPInputComponent } from "../component/OTPInput";
import { useStore } from "../helper/store";
import { useNavigate } from "react-router";

function ForgotPassword() {
  const navigate = useNavigate();
  const [changeOtpScreen, setChangeOtpScreen] = useState(false);
  const [email, setEmail] = useState("");
  const [showPasswordInput, setShowPasswordInput] = useState(false);
  const [showConfirmPasswordInput, setShowConfirmPasswordInput] =
    useState(false);
  const [showConfirmPasswordScreen, setShowConfirmPasswordScreen] =
    useState(false);
  if (!changeOtpScreen) {
    const {
      register,
      watch,
      handleSubmit,
      setError,
      setValue,
      clearErrors,
      trigger,
      getValues,
      formState: { errors, isValid, submitCount },
    } = useForm({
      resolver: yupResolver(
        yup
          .object({
            email: yup
              .string()
              .matches(
                /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                {
                  message: "Please enter valid email",
                }
              )
              .required("Email is Required"),
          })
          .required()
      ),
      mode: "onSubmit",
    });

    useEffect(() => {
      if (errors.root?.serverError) clearErrors("root.serverError");
    }, [watch("email")]);

    const sendOtpHandler = async (data) => {
      try {
        await Auth.forgotPassword(data.email);
        setEmail(data.email);
        setChangeOtpScreen(true);
      } catch (error) {
        setChangeOtpScreen(false);
        setError("email", { message: error.message })
      }
    };
    return (
      <>
        <div className="resetPassword">
          <div className="emailDiv">
            <input
              type="email"
              name="email"
              {...register("email")}
              placeholder="Enter Email"
            />
            <p className='errorText'>{errors.email?.message}</p>
          </div>
          <div className="resetBtnDiv">
            <button
              className="resetPassBtn"
              onClick={handleSubmit(sendOtpHandler)}
            >
              Reset Password
            </button>
            <button
              className="backBtn"
              onClick={() => navigate(routes.loginPage)}
            >
              Back
            </button>
          </div>
        </div>
      </>
    );
  } else {
    const {
      register,
      watch,
      handleSubmit,
      setError,
      setValue,
      clearErrors,
      trigger,
      getValues,
      formState: { errors, isValid, submitCount },
    } = useForm({
      resolver: yupResolver(
        yup
          .object({
              newPassword: yup
              .array()
              .of(yup.number().typeError("").required("req").integer("integer"))
              .test(
                "len",
                "New Password must be 6 digits",
                (val) => val?.filter((v) => !isNaN(v)).length === 6
              ),
              confirmPassword: yup
              .array()
              .of(yup.number().typeError("").required("req").integer("integer"))
              .test(
                "len",
                "Confirm Password must be 6 digits",
                (val) => val?.filter((v) => !isNaN(v)).length === 6
              ),
          })
          .required()
      ),
      mode: "onSubmit",
    });

    const handleFocus = () => {
      setShowPasswordInput(true);
    };
    const handlePasswordFocus = () => {
      setShowPasswordInput(true);
    };
    const handleConfirmPasswordFocus = () => {
      setShowConfirmPasswordInput(true);
    };

    useEffect(() => {
      if (errors.root?.serverError) clearErrors("root.serverError");
    }, [watch("otp")]);

    const setNewPasswordHandler = async (data) => {
      try {
        const otp = data.otp?.join("");
        const newPassword = data.newPassword?.join("");
        const confirmPassword = data.confirmPassword?.join("");
        if(newPassword === confirmPassword){
          const res = await Auth.forgotPasswordSubmit(data.email, otp, confirmPassword);
          if(res === 'SUCCESS'){
            navigate(routes.loginPage)
          }
        }else{
          setError("confirmPassword", { message: 'Password does not match!' })
          return
        }
      } catch (error) {
        setShowConfirmPasswordScreen(false)
        setShowPasswordInput(false)
        setShowConfirmPasswordInput(false);
        setValue('otp', '')
        setValue('newPassword', '')
        setValue('confirmPassword', '')
        setError("otp", { message: error.message })
      }
    };

    return (
      <>
        {!showConfirmPasswordScreen ? (
          <div className="resetPassword">
            <div className="emailDiv">
              {!watch("otp") && !showPasswordInput ? (
                <input
                  id="inputPlaceholder"
                  placeholder="Enter OTP"
                  value=""
                  onFocus={handleFocus}
                  readOnly
                />
              ) : (
                <OTPInputComponent
                  register={register}
                  className="passwordBox"
                  autoFocus
                  inputType={"tel"}
                  registerName={"otp"}
                  disableOnPaste = {true}
                />
              )}
            <p className='errorText errorForgetPass'>{errors.otp?.message}</p>
            </div>
            <div className="resetBtnDiv">
              <button
                className="nextBtn"
                onClick={() => {
                  if(Array.isArray(getValues('otp')) && getValues('otp')?.join('').length >= 6){
                    setShowConfirmPasswordScreen(!showConfirmPasswordScreen);
                    setShowPasswordInput(false);
                    clearErrors("otp")
                  }else{
                    setError("otp", { message: 'OTP must be 6 digits' })

                  }
                }}
              >
                Next
              </button>
              <button
                className="backBtn"
                onClick={() => navigate(routes.loginPage)}
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <div className="resetPassword">
            <div className="passDiv">
              {!watch("newPassword") && !showPasswordInput ? (
                <input
                  placeholder="Enter New Password"
                  value=""
                  onFocus={handlePasswordFocus}
                  readOnly
                />
              ) : (
                <OTPInputComponent
                  register={register}
                  inputType={"password"}
                  registerName={"newPassword"}
                  className="passwordBox"
                  autoFocus
                />
              )}
              {(errors.newPassword && <p className='errorText errorForgetPass'>{errors.newPassword?.message}</p>)
              ||
              (errors.confirmPassword && <p className='errorText errorForgetPass'>{errors.confirmPassword?.message}</p>)
              }
            </div>
            <div className="passDiv">
              {!watch("confirmPassword") && !showConfirmPasswordInput ? (
                <input
                  placeholder="Enter Confirm Password"
                  value=""
                  onFocus={handleConfirmPasswordFocus}
                  readOnly
                />
              ) : (
                <OTPInputComponent
                  register={register}
                  inputType={"password"}
                  registerName={"confirmPassword"}
                  className="passwordBox"
                  disableOnPaste = {true}
                  autoFocus
                />
              )}
            </div>
            <div className="resetBtnDiv">
              <button
                className="nextBtn"
                onClick={() => handleSubmit(setNewPasswordHandler)()}
              >
                Confirm
              </button>
              <button
                className="backBtn"
                onClick={() => navigate(routes.loginPage)}
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </>
    );
  }
}

export default ForgotPassword;
