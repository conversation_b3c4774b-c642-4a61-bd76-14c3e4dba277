// @ts-nocheck
import { IonP<PERSON>, IonContent, useIonRouter, useIonViewWillEnter, useIonViewDidLeave, useIonViewWillLeave, useIonLoading, useIonViewDidEnter } from '@ionic/react';
import styles from './StockingLocation.module.scss';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { stockingLocationSchema } from '../SettingSchema';
import { useEffect, useRef, useState } from 'react';
import { sellerSettingPayLoadFormatter, formatPhoneNumber, formatPhoneNumberRemovingCountryCode, formatPhoneNumberWithCountryCode, saveUserSetting } from '../../../../library/helper';
import clsx from 'clsx';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import axios from 'axios';
import { ReactComponent as BackBtnArrowIcon } from '../../../../../assets/mobile-images/back_left_arrow.svg';
import useSellerSettingStore, { StockingLocation as StockingLocationModel } from '../SellerSettingStore';
import { mobileDiaglogConst, sellerSettingConst } from '../../../../library/common';
import useDialogStore from '../../../../components/Dialog/DialogStore';

const StockingLocation = () => {
    const {
        register,
        handleSubmit,
        getValues,
        clearErrors,
        setValue,
        watch,
        setError,
        reset,
        control,
        formState: { errors, dirtyFields, isDirty, isValid } } = useForm({
            resolver: yupResolver(stockingLocationSchema)
        });

    const { ref, ...rest } = register("stocking_location");

    const [isSaveDisable, setIsSaveDisable] = useState(true);
    const {userData, setShowLoader} = useGlobalStore();
    const { stockingLocationInfo, setStockingLocationInfo, sellerSetting, setSellerSettingInfo } = useSellerSettingStore();
    const router = useIonRouter();
    const [present, dismiss] = useIonLoading();
    const { showCommonDialog, resetDialogStore } = useDialogStore();

    const addressRef = useRef<HTMLIonInputElement>();

    console.log("stocking location", stockingLocationInfo)
    useIonViewWillEnter(() => {
        if (stockingLocationInfo) {
            setValue('stocking_location', stockingLocationInfo.stocking_location);
            clearErrors();
        }
    }, [stockingLocationInfo])

    
    useIonViewDidEnter(() => {
        addressRef.current!.focus();
    }, []);

    useIonViewWillLeave(() => {
        setIsSaveDisable(true);
        reset();
        resetDialogStore();
    }, [])

    useEffect(() => {
        if (isDirty)
            setIsSaveDisable(false);

            // const handleBackButton = (ev: BackButtonEvent) => {
            //     ev.detail.register(10, async () => {
            //         backToSetting();
            //     });
            // };
    
            // document.addEventListener('ionBackButton', handleBackButton);
            // return () => {
            //     document.removeEventListener('ionBackButton', handleBackButton);
            // };
    }, [isDirty])

    const onSubmit = async(data) => {
        setShowLoader(true);
        const detail : StockingLocationModel = data;  
        // const _sellerSetting = {...sellerSetting, ...detail};
        // const payload: any = sellerSettingPayLoadFormatter(_sellerSetting);
        try{
            await saveUserSetting(sellerSettingConst.apiRoutesForSave.stockInfo, detail, userData);
            // setSellerSettingInfo(_sellerSetting);
            setStockingLocationInfo(detail);
            router.push('/seller-setting',{animate:true,direction:'forward'});
            setShowLoader(false);
        }catch(err){
            console.error(err)
            setShowLoader(false);
        }
    }

    const handleFormSubmit = () => {
        console.log("CLICKED",Object.keys(errors))
        if (Object.keys(errors).length === 0) {
            handleSubmit(onSubmit)();
        } else {
            console.log("RETURN")
            return;
        }
    }

    const routeBackToSetting = ()=>{
        router.push('/seller-setting',{animate:true,direction:'forward'});
        resetDialogStore();
    }

    const backToSetting = () => {
        if(isDirty)
        showCommonDialog(mobileDiaglogConst.unsavedChangesMsg,[{name: mobileDiaglogConst.stay, action: resetDialogStore},{name: mobileDiaglogConst.goBackToSetting, action: routeBackToSetting}]);
        else
        routeBackToSetting();
    }

    return (
        <IonPage>
            <IonContent>
                <div className={styles.profile}>
                    <h2 className={styles.heading}><BackBtnArrowIcon onClick={backToSetting}/><span>Stocking Location</span></h2>
                    <div className={styles.settingsContent}>
                        <div className={styles.profileComponent}>
                            <label > Address</label>
                            <input className={styles.inputField} type='text' {...rest} placeholder='Address'
                                ref={(e) => { ref(e); addressRef.current = e; }}
                            onBlur={(e) => {
                                e.target.value = e.target.value.trim();
                                rest.onBlur(e);
                            }} />

                        </div>
                        <div className={styles.profileComponent}>
                            <label > Send Invoices to</label>
                            <input className={clsx(styles.inputField, errors?.send_invoices_to?.message && styles.errorMsg )} type='text' {...register("send_invoices_to")} value={'<EMAIL>'} placeholder='Enter AP email address (multiple separate with a comma)' readOnly />
                        </div>
                        <div className={styles.profileComponent}>
                            <label > Shipping Docs to</label>
                            <input className={clsx(styles.inputField, errors?.shipping_docs_to?.message && styles.errorMsg )} type='text' {...register("shipping_docs_to")} value={'<EMAIL>'} placeholder='Enter email address (multiple separate with a comma)' readOnly />
                        </div>
                    </div>
                    <div className={styles.btnSection}>
                        <button className={styles.saveBtn} onClick={() => handleFormSubmit()} disabled={isSaveDisable}>Save</button>
                    </div>

                </div>
            </IonContent>
        </IonPage>
    )
}

export default StockingLocation;