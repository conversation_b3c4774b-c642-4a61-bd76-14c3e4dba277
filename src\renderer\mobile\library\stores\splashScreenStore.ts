import { create } from 'zustand';

const commonSplashScreenStore = {
    showSplashScreen:false,
    capgoUpdateDisplayText:"",
    updatesFound:true,
    capgoUpdateProgess:0,
}
export const useSplashScreenStore = create((set,get)=>({
    ...commonSplashScreenStore,
    setShowSplashScreen:(showSplashScreen:boolean)=>{set({showSplashScreen})},
    setCapgoUpdateDisplayText:(capgoUpdateDisplayText:any)=>{set({capgoUpdateDisplayText})},
    setUpdatesFound:(updatesFound:boolean)=>{set({updatesFound})},
    setCapgoUpdateProgess:(capgoUpdateProgess:any)=>{set({capgoUpdateProgess})},
    resetSplashScreenStore:()=>set(state => ({
        capgoUpdateDisplayText:"",
        capgoUpdateProgess:0,
    }))
}))