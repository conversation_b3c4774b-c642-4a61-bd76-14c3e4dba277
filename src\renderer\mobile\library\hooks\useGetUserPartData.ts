// @ts-nocheck
import { useQuery } from "@tanstack/react-query";
import { reactQueryKeys } from "../common";
import axios from "axios";

const useGetUserPartData = () => {
  return useQuery(
    [reactQueryKeys.getUserPartData],
    async () => {
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_API_SERVICE}/user/getProductTagMapping`
        );

        if (response.data && response.data.data) {
          if (
            typeof response.data.data === "object" &&
            "err_message" in response.data.data
          ) {
            throw new Error(response.data.data.err_message);
          } else {
            return response.data.data;
          }
        } else {
          return null;
        }
      } catch (error) {
        throw new Error(error?.message ?? error);
      }
    },
    {
      retry: false,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      staleTime: 0,
    }
  );
};

export default useGetUserPartData;
