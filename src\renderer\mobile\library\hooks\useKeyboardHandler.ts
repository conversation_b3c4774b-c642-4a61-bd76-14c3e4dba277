import { useEffect, useState } from 'react';
import { Keyboard } from '@capacitor/keyboard';
import { Capacitor } from '@capacitor/core';

interface KeyboardInfo {
  keyboardHeight: number;
  isKeyboardOpen: boolean;
}

export const useKeyboardHandler = () => {
  const [keyboardInfo, setKeyboardInfo] = useState<KeyboardInfo>({
    keyboardHeight: 0,
    isKeyboardOpen: false,
  });

  useEffect(() => {
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    const keyboardWillShowListener = Keyboard.addListener('keyboardWillShow', (info) => {
      console.log('Keyboard will show with height:', info.keyboardHeight);
      setKeyboardInfo({
        keyboardHeight: info.keyboardHeight,
        isKeyboardOpen: true,
      });
      
      // Add keyboard-open class to body
      document.body.classList.add('keyboard-is-open');
      document.body.style.setProperty('--keyboard-height', `${info.keyboardHeight}px`);
    });

    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (info) => {
      console.log('Keyboard did show with height:', info.keyboardHeight);
      
      // Ensure proper viewport adjustment
      const viewport = document.querySelector('meta[name=viewport]');
      if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1, user-scalable=no, viewport-fit=cover');
      }
      
      // Scroll active input into view if needed
      const activeElement = document.activeElement as HTMLElement;
      if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
        setTimeout(() => {
          activeElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 300);
      }
    });

    const keyboardWillHideListener = Keyboard.addListener('keyboardWillHide', () => {
      console.log('Keyboard will hide');
      setKeyboardInfo({
        keyboardHeight: 0,
        isKeyboardOpen: false,
      });
      
      // Remove keyboard-open class from body
      document.body.classList.remove('keyboard-is-open');
      document.body.style.removeProperty('--keyboard-height');
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      console.log('Keyboard did hide');
      // Additional cleanup if needed
    });

    // Cleanup listeners on unmount
    return () => {
      keyboardWillShowListener.remove();
      keyboardDidShowListener.remove();
      keyboardWillHideListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Utility functions
  const hideKeyboard = async () => {
    if (Capacitor.isNativePlatform()) {
      await Keyboard.hide();
    }
  };

  const showKeyboard = async () => {
    if (Capacitor.isNativePlatform()) {
      await Keyboard.show();
    }
  };

  return {
    keyboardInfo,
    hideKeyboard,
    showKeyboard,
  };
};
