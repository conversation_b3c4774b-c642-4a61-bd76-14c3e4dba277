// @ts-nocheck
import { Dialog, Fade, Tooltip } from '@mui/material';
import { useContext, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router';
import { 
    userRole,
    routes,
    reactQueryKeys
} from '../../../common';
import { UserContext } from '../../UserContext';
 
import styles from './createPo.module.scss'
import { ReactComponent as DropdownIcon } from '../../assets/images/icon_Triangle.svg'; 
import { ReactComponent as QuestionIcon } from '../../assets/images/setting-question.svg';
import { ReactComponent as QuestionHoverIcon } from '../../assets/images/question-white-hover.svg';
import { ReactComponent as QuestionBlackIcon } from '../../assets/images/setting-black-question.svg';
import { ReactComponent as QuestionBlackHoverIcon } from '../../assets/images/question-black-hover.svg';
import { ReactComponent as AddLineIcon } from '../../assets/images/Add-line.svg';
import { ReactComponent as AddLineHoverIcon } from '../../assets/images/Add-line-hover.svg';
import { DeliveryDateTooltip, DeliveryToTooltip, JobPOTooltip, SalesTaxTooltip, chooseYourUnitTooltip } from '../../tooltip';
import clsx from 'clsx';
import { useHeightListener } from '../../hooks/useHeightListener';
import { useFieldArray, useForm, useWatch } from 'react-hook-form';
import { createPoSchema } from './createPoSchema';
import { yupResolver } from '@hookform/resolvers/yup';
import { addWorkingDaysToDate, formatToTwoDecimalPlaces, getFloatRemainder, } from '../../helper';
import axios from 'axios';
import { CustomMenu } from './CustomMenu';
import { useDebouncedValue } from '@mantine/hooks';
import { useImmer } from 'use-immer';
import dayjs, { Dayjs } from 'dayjs';

import { useStore } from '../../helper/store';
import useGetUserPartData from '../../hooks/useGetUserPartData';
import { useQueryClient } from '@tanstack/react-query';
import CreatePoTile from './CreatePoTile/CreatePoTile';
import { ReactComponent as WarningIcon } from '../../assets/images/warning-seller-icon.svg';
import { v4 as uuidv4 } from 'uuid';
import { CommonTooltip } from '../../component/Tooltip/tooltip';
import useSaveUserActivity from '../../hooks/useSaveUserActivity';




const CreatePo = () => {
    const navigate = useNavigate();
    const userContext = useContext(UserContext);
    const userId = userContext.user;
    const [products, setProducts] = useState([]);
    const [states, setStates] = useState([]);
    const [deliveryDates, setDeliveryDates] = useState([]);
    const [showLine1Tooltip, setShowLine1Tooltip] = useState(true);
    const [showCityTooltip, setShowCityTooltip] = useState(true);
    const [showPaymentOptionTooltip, setShowPaymentOptionTooltip] = useState(undefined);
    const [paymentMethods,] = useState([{
        title: 'Cash In Advance',
        value: 'ach_credit',
        available: true,
        changeTitle: <span className={styles.w100}>Cash In Advance</span>
    },
    {
        title: 'Net 30 Terms',
        value: 'bryzos_pay',
        available: true,
        changeTitle: <span className={styles.w100} onClick={() => { stroeInProgressPoData(); navigate(routes.buyerSettingPage) }} >Net 30 Terms (Setup)</span>
    }])
    const [openErrorDialog, setOpenErrorDialog] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    // const [openQtyErrorPop, setopenQtyErrorPop] = useState(false);
    // const [isDataLoad, setisDataLoad] = useState(true);
    const [qtyUnitM, setQtyUnitM] = useImmer([]);
    const [priceUnitM, setPriceUnitM] = useImmer([]);
    const [sendInvoicesToEmailData, SetSendInvoicesToEmailData] = useState('');
    const [apiCallInProgress, setApiCallInProgress] = useState(false);

    const queryClient = useQueryClient();
    const { data: userPartData, isLoading: isUserPartDataLoading } = useGetUserPartData();
    const { mutate: logUserActivity, } = useSaveUserActivity();

    const {
        control,
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        watch,
        clearErrors,
        formState: { errors, isValid }
    } = useForm({
        resolver: yupResolver(createPoSchema),
        defaultValues: {
            'descriptionLines': [{
                descriptionObj: '',
                qtyVal: '',
                qtyUnit: '',
                umUnit: '',
                partNumber: '',
                domesticMaterialOnly: null,
            }]
        }
    });

    const _descriptionLines = useWatch({ control, name: "descriptionLines" });

    const { fields, append, remove, } = useFieldArray({
        control,
        name: "descriptionLines"
    });

    const ref = useHeightListener()
    const [deboucedMatTotal] = useDebouncedValue(watch('materialTotal'), 400);
    const setCreatePoData = useStore(state => state.setCreatePoData);
    const createPoData = useStore(state => state.createPoData);
    const setShowLoader = useStore(state => state.setShowLoader);
    const backNavigation = useStore(state => state.backNavigation);
    const setBackNavigation = useStore(state => state.setBackNavigation);
    const setCreatePoSessionId = useStore(state => state.setCreatePoSessionId);
    const createPoSessionId = useStore(state => state.createPoSessionId);
    const resetHeaderConfig = useStore(state => state.resetHeaderConfig);
    const [showDepositForACH, setShowDepositForACH] = useState(false);
    const [handleSubmitValidation, setHandleSubmitValidation] = useState(false);
    const [sessionId, setSessionId] = useState('');
    const [lineSessionId, setLineSessionId] = useState('');
    const [disableCloseAnalytic, setDisableCloseAnalytic] = useState(true);
    const [salesTaxCounter, setSalesTaxCounter] = useState(0);
    const [deliveryStateFocusEnable, setDeliveryStateFocusEnable] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [searchStringData, setSearchString] = useState('');
    const [todayDate, setTodayDate] = useState('');
    const [disableDeliveryDate, setDisableDeliveryDate] = useState(true);
    const [deboucedSearchString, cancelDebouncedSearchString] = useDebouncedValue(searchStringData, 400);
    const analyticRef = useRef();
    analyticRef.current = disableCloseAnalytic && !resetHeaderConfig;


    useEffect(() => {
        setShowLoader(true)
        if (userId) {
            setProducts(userId.allProductsData)

            const referenceData = userId.referenceData;
            setStates(referenceData.ref_states);
            setDeliveryDates(referenceData.ref_delivery_date);

            axios.get(import.meta.env.VITE_API_SERVICE + '/user/buyingPreference')
            .then(res => {
                const buyingPreference = res.data.data;
                const deliveryReceivingHours = buyingPreference.user_delivery_receiving_availability_details;
                if(deliveryReceivingHours?.length !== 0){
                    setValue('recevingHours', deliveryReceivingHours)
                    const disableDeliveryDates = deliveryReceivingHours.every((deliveryReceivingHour)=> deliveryReceivingHour.from === "closed")
                    setDisableDeliveryDate(disableDeliveryDates)
                    
                }
                if (createPoData) {
                    setValue('poJobName', createPoData.poJobName);
                    setValue('deliverTo', createPoData.deliverTo);
                    setValue('deliveryDate', handleGetDeliveryDate(createPoData.deliveryDate,buyingPreference.user_delivery_receiving_availability_details));
                    setValue('descriptionLines', createPoData.descriptionLines);
                    createPoData.descriptionLines.forEach((product, index) => {
                        const descriptionObj = product.descriptionObj;
                        if (descriptionObj) {
                            const qtyUnitMData = descriptionObj.QUM_Dropdown_Options.split(",")
                            const priceUnitMData = descriptionObj.PUM_Dropdown_Options.split(",")
                            setQtyUnitM(prev => {
                                prev[index] = qtyUnitMData;
                                return prev
                            })
                            setPriceUnitM(prev => {
                                prev[index] = priceUnitMData;
                                return prev
                            })
                        }
                        updateValue(index)
                    });
                    setValue('selectedOptionPayment', createPoData.selectedOptionPayment);
                    setValue('materialSellerTotal', createPoData.materialSellerTotal);
                    setValue('materialTotal', createPoData.materialTotal);
                    setValue('salesTax', createPoData.salesTax);
                    setShowLoader(false);
                }else{
                        setValue('deliverTo.line1', buyingPreference.delivery_address_line1)
                        setValue('deliverTo.city', buyingPreference.delivery_address_city)
                        setValue('deliverTo.state_id', buyingPreference.delivery_address_state_id)
                        setValue('deliverTo.zip', buyingPreference.delivery_address_zip)
                        const convertedInDateFormat = addWorkingDaysToDate(buyingPreference.delivery_days_add_value ?? referenceData.ref_delivery_date[0].days_to_add);
                        setValue('deliveryDate', handleGetDeliveryDate(convertedInDateFormat,buyingPreference.user_delivery_receiving_availability_details))
                        SetSendInvoicesToEmailData(buyingPreference.send_invoices_to);
                        
                        if (buyingPreference.ach_credit?.is_approved === 1) {
                            paymentMethods[0].available = false;

                        }
                        if (buyingPreference.bnpl_settings?.is_approved === 1) {
                            paymentMethods[1].available = false;
                        }
                        if (buyingPreference.default_payment_method === 'ACH_CREDIT') {
                            setValue('selectedOptionPayment', 'ach_credit')
                            setValue('depositAmount',0);
                            // setShowDepositForACH(true);
                        } else if (buyingPreference.default_payment_method === 'BUY_NOW_PAY_LATER') {
                            setValue('selectedOptionPayment', 'bryzos_pay')
                        }
                        setValue('salesTax', 0);
                        setShowLoader(false);
                    }
                    setTodayDate(new Date());
                    })
                    .catch(err => {
                        setOpenErrorDialog(true);
                        setErrorMessage("Something went wrong. Please try again in sometime");
                        setShowLoader(false);
                        console.error(err)
                    })
        }
        const sessionId = uuidv4();
        setSessionId(sessionId);
        setCreatePoSessionId(sessionId)
        const payload = {
            "data": {
                "session_id": sessionId
            }
        }
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close', payload)
        .catch(err => console.error(err))
        return(() => {
            setBackNavigation(-1)
            cancelDebouncedSearchString()
            if(analyticRef.current  && sessionId){
                const payload = {
                    "data": {
                        "session_id": sessionId,
                        "close_status": "CANCEL"
                    }
                }
                axios.post(import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close', payload)
                .then(() => setCreatePoSessionId(null))
                .catch(err => console.error(err))
            }
        })
    }, []);

    useEffect(()=>{
        paymentMethodChangeHandler();
    },[watch('selectedOptionPayment')]);

    useEffect(() => {
        if (Array.isArray(_descriptionLines)) {
            let matTotal = 0;
            let matSellerTotal = 0;

            _descriptionLines.forEach((line, i) => {
                if (line.descriptionObj) {
                    const qtyVal = +getValues(`descriptionLines.${i}.qtyVal`);
                    const orderIncrement = +getOrderIncrement(
                        line.descriptionObj,
                        `descriptionLines.${i}.qtyUnit`
                    );

                    if ( parseFloat(qtyVal) && parseFloat(orderIncrement) &&
                        qtyVal > 0 &&
                         getFloatRemainder(qtyVal, orderIncrement) === 0
                    ) {
                        matTotal += line.totalVal;
                        matSellerTotal += line.totalSellerVal;
                    }
                }
            });

            setValue(`materialTotal`, matTotal);
            setValue(`materialSellerTotal`, matSellerTotal);
        }
    }, [_descriptionLines]);

    useEffect(() => {
        setHandleSubmitValidation(false);
        handleStateZipValidation()
    }, [deboucedMatTotal])
    
    useEffect(() => {
        setHandleSubmitValidation(false);
        handleStateZipValidation()
    },[watch('deliverTo.zip'), watch('deliverTo.state_id')])

    useEffect(() => {
        if(deboucedSearchString){
            handleCreatePOSearch(searchStringData, null, lineSessionId)
        }
    }, [deboucedSearchString])

    const paymentMethodChangeHandler = async() => {
        if(getValues('materialTotal')){
            let totalPurchaseValue = getValues('materialTotal')+getValues('salesTax');
            if(watch('selectedOptionPayment') === 'ach_credit'){
                setHandleSubmitValidation(false);
                const depositValue = await calculateDepositAmount();
                setValue('depositAmount', depositValue);
                totalPurchaseValue += depositValue;
                setHandleSubmitValidation(true);
            }
            setValue('totalPurchase',totalPurchaseValue);
        }
    }

    const updateValue = (index, selectedItem) => {
        // this will be only when dropdown value is seleted
        setSelectedProduct(selectedItem)
        if (selectedItem) {
            setValue(`descriptionLines.${index}.qtyUnit`, selectedItem.QUM_Dropdown_Options.split(',')[0])
            setValue(`descriptionLines.${index}.umUnit`, selectedItem.PUM_Dropdown_Options.split(',')[0])
            setValue(`descriptionLines.${index}.qtyVal`, null)

            const qtyUnitMData = selectedItem.QUM_Dropdown_Options.split(",")
            const priceUnitMData = selectedItem.PUM_Dropdown_Options.split(",")
            setQtyUnitM(prev => {
                prev[index] = qtyUnitMData;
                return prev
            })
            setPriceUnitM(prev => {
                prev[index] = priceUnitMData;
                return prev
            })

            if (userPartData?.length) {
                userPartData.forEach((partData) => {
                  partData.product_id.forEach((id) => {
                    if (id === selectedItem.Product_ID) {
                      setValue(`descriptionLines.${index}.partNumber`, partData.tag);
            }
                  });
                });
            }
        }

        const _selected = selectedItem ? selectedItem : getValues(`descriptionLines.${index}.descriptionObj`)
        if (!_selected) return;

        const umVal = getUnitVal(_selected, `descriptionLines.${index}.umUnit`)
        const updateUmVal = parseFloat(umVal.replace(/[\$,]/g, ''));
        const updateSellerVal = parseFloat(getSellerUnitVal(_selected, `descriptionLines.${index}.umUnit`).replace(/[\$,]/g, ''))


        const buyerPricePerUnit = parseFloat(getUnitVal(_selected, `descriptionLines.${index}.qtyUnit`,).replace(/[\$,]/g, ''))
        const sellerPricePerUnit = parseFloat(getSellerUnitVal(_selected, `descriptionLines.${index}.qtyUnit`).replace(/[\$,]/g, ''))
        const orderIncrement = getOrderIncrement(_selected, `descriptionLines.${index}.qtyUnit`)
        const qtyVal = selectedItem === null ? null : getValues(`descriptionLines.${index}.qtyVal`)

        if (qtyVal > 0 &&  getFloatRemainder(qtyVal, orderIncrement)=== 0) {
            setValue(`descriptionLines.${index}.umVal`, updateUmVal)
            setValue(`descriptionLines.${index}.buyerPricePerUnit`, buyerPricePerUnit)
            setValue(`descriptionLines.${index}.sellerVal`, updateSellerVal)
            setValue(`descriptionLines.${index}.sellerPricePerUnit`, sellerPricePerUnit)
            const totalVal = formatToTwoDecimalPlaces(buyerPricePerUnit * qtyVal).replaceAll(",","");
            const totalSellerVal = formatToTwoDecimalPlaces(sellerPricePerUnit * qtyVal).replaceAll(",","");
            setValue(`descriptionLines.${index}.totalVal`, +totalVal)
            setValue(`descriptionLines.${index}.totalSellerVal`, +totalSellerVal)

            const items = getValues(`descriptionLines`)
            const matTotal = items.map(x => x.totalVal ?? 0).reduce((c, p) => c + p, 0)
            const matSellerTotal = items.map(x => x.totalSellerVal ?? 0).reduce((c, p) => c + p, 0)
            setValue(`materialTotal`, matTotal)
            setValue(`materialSellerTotal`, matSellerTotal)
            clearErrors(`descriptionLines.${index}.qtyVal`);
        } else {
            if (!selectedItem && selectedItem !== null)
                setError(`descriptionLines.${index}.qtyVal`, { message: `Quantity can only be multiples of ${orderIncrement}` })

            if (qtyVal === null)
                setValue(`descriptionLines.${index}.qtyVal`, null)

            setValue(`descriptionLines.${index}.umVal`, 0)
            setValue(`descriptionLines.${index}.totalVal`, 0)
            setValue(`descriptionLines.${index}.totalSellerVal`, 0)
            setValue(`materialTotal`, 0)
            setValue(`materialSellerTotal`, 0)
            setValue(`descriptionLines.${index}.sellerVal`, 0)
            setValue(`totalPurchase`, 0)
            setValue(`totalSellerPurchase`, 0)
            setValue(`depositAmount`, 0)
            setValue(`salesTax`, 0)
            setHandleSubmitValidation(false);

            return;

        }
    }

    const getOrderIncrement = (selectedProd, unitKey) => {
        const unit = getValues(unitKey);
        if (!unit)
            return 0
        if (userId.data.type === userRole.buyerUser) {
            return selectedProd[`Order_Increment_${unit}`]

        }

    }
    const getUnitPostScript = (unit) => {
        if (unit.toLowerCase() === 'cwt') {
            return "CWT"
        } else if (unit.toLowerCase() === 'ea') {
            return "Ea"
        } else if (unit.toLowerCase() === 'ft') {
            return "Ft"
        } else if (unit.toLowerCase() === 'lb') {
            return "LB"
        } else if (unit.toLowerCase() === 'net ton') {
            return "Net_Ton"
        }
    }

    const getUnitVal = (selectedProd, unitKey) => {
        const unit = getValues(unitKey);
        if (!unit)
            return '0'
        const updatedUnit = getUnitPostScript(unit);
        if (userId.data.type === userRole.buyerUser) {

            return selectedProd[`Buyer_Pricing_${updatedUnit}`]

        } else if (userId.data.type === userRole.sellerUser) {
            return selectedProd[`Seller_Pricing_${updatedUnit}`]

        } else if (userId.data.type === userRole.neutralUser) {
            return selectedProd[`Neutral_Pricing_${updatedUnit}`]
        }

    }
    const getSellerUnitVal = (selectedProd, unitKey) => {
        const unit = getValues(unitKey);

        if (!unit)
            return '0'
        const updatedUnit = getUnitPostScript(unit);
        return selectedProd[`Seller_Pricing_${updatedUnit}`];
    }


    const handleSalesTax = async () => {
        try{
            setHandleSubmitValidation(false);
            const matTotal = getValues('materialTotal')
            const matSellerTotal = getValues('materialSellerTotal')
            if (!matTotal) {
                setApiCallInProgress(false);
                return;
            }
    
            const deliveryDate = getValues('deliveryDate') ? getValues('deliveryDate') : null;
    
            let cartItems = formatCartItems(getValues('descriptionLines'));
            const taxCounter = salesTaxCounter + 1;
            setSalesTaxCounter(taxCounter)
            const payload = {
                data: {
                    internal_po_number: getValues('poJobName'),
                    shipping_details: getValues('deliverTo'),
                    delivery_date: deliveryDate,
                    payment_method: getValues('selectedOptionPayment'),
                    seller_price: matSellerTotal.toFixed(2),
                    price: String(matTotal),
                    sales_tax: getValues('salesTax'),
                    freight_term: 'Delivered',
                    cart_items: cartItems,
                    salesTaxCounter: salesTaxCounter + 1
                },
            };
            const salesResponse = await axios.post(import.meta.env.VITE_API_SERVICE + '/order/sales_tax_calculate', payload);
            const salesTaxCounterResponse = salesResponse.data.data.salesTaxCounter;
            if (salesTaxCounterResponse === taxCounter && watch('materialTotal')) {
              setValue("salesTax", parseFloat(salesResponse.data.data.tax));
              let totalPurchaseValue =
                matTotal + parseFloat(salesResponse.data.data.tax);
              let depositValue = 0;
              if (getValues("selectedOptionPayment") === "ach_credit") {
                depositValue = await calculateDepositAmount();
              }
              setValue("depositAmount", depositValue);
              totalPurchaseValue += depositValue;
              setValue(`totalPurchase`, totalPurchaseValue);
              setValue(`totalSellerPurchase`, matSellerTotal);

              setHandleSubmitValidation(true);
            }
        }catch(error){

        }
        setApiCallInProgress(false);
    };

    const onSubmit = (data) => {
        const date = new Date()
        if(dayjs(date).format('M/D/YYYY') === dayjs(todayDate).format('M/D/YYYY')){
            setShowLoader(true);
            const totalPurchaseValue = getValues('materialTotal');
            const totalSellerPurchaseValue = getValues('materialSellerTotal');
            const payload = {
                "data": {
                    "internal_po_number": data.poJobName,
                    "shipping_details": data.deliverTo,
                    "delivery_date": data.deliveryDate,
                    "payment_method": getValues('selectedOptionPayment'),
                    "seller_price": totalSellerPurchaseValue.toFixed(2), 
                    "price": String(totalPurchaseValue),
                    "sales_tax": data.salesTax,
                    "freight_term": "Delivered",
                    "cart_items": formatCartItems(getValues('descriptionLines'))
                }
            };
            setDisableCloseAnalytic(false)
            axios.post(import.meta.env.VITE_API_PHP_SERVICE + '/api/v1/widget/checkout', payload)
                .then(res => {
                    if (res.data.data.error_message) {
                        setOpenErrorDialog(true);
                        setErrorMessage(res.data.data.error_message);
                        setShowLoader(false);
                        saveUserActivity(null, res.data.data.error_message)
                        setDisableCloseAnalytic(true)
                    } else {
                        queryClient.invalidateQueries([reactQueryKeys.getUserPartData]);
                        const payload = {
                            "data": {
                                "session_id": sessionId,
                                "close_status": "ACCEPT"
                            }
                        }
                        axios.post(import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close', payload)
                        .catch(err => console.error(err))
                        navigate(routes.orderConfirmationPage, { state: { poNumber: res.data.data, jobNumber: data.poJobName, sendInvoicesTo: sendInvoicesToEmailData, selectedOptionPayment: data.selectedOptionPayment, } })
                        setCreatePoData(null);
                        saveUserActivity(res.data.data, null);
                    }
                })
                .catch(err => {
                    saveUserActivity(null, (err?.message ?? err));
                    setDisableCloseAnalytic(true)
                    setOpenErrorDialog(true);
                        setErrorMessage("Something went wrong. Please try again in sometime");
                        setShowLoader(false);
                    console.error(err)
                })
        }else{
            setOpenErrorDialog(true);
            setErrorMessage('Selected delivery date is incorrect, please select correct delivery date.');
            setValue('deliveryDate', null)
            handleDeliveryDateList()
            setTodayDate(new Date());
        }

        
    }

    const addNewRow = () => {
        append({
            descriptionObj: '',
            qtyVal: '',
            qtyUnit: '',
            umUnit: '',
            partNumber: '',
            domesticMaterialOnly: null,
        });
        const t = setTimeout(() => {
            clearTimeout(t);
            const element = document.getElementById(`combo-box-demo${fields.length}`);
            if (element) {
                element.focus()
            }
        }, (100));
    }

    // const delivaryDateCalculator = (numberofDays) => {
    //     if (isNaN(numberofDays)) {
    //         return "";
    //     }
    //     const date = new Date();
    //     date.setDate(date.getDate() + numberofDays);
    //     const days = date.getDate();
    //     const months = date.getMonth() + 1;
    //     const year = date.getFullYear() % 100;
    //     return months + "/" + days + "/" + year;
    // };

    // const datepickerChangeHandler = (value) => {
    //     const date = new Date(value);
    //     const days = date.getDate();
    //     const months = date.getMonth() + 1;
    //     const year = date.getFullYear() % 100;
    //     return months + "/" + days + "/" + year;
    // }

    const stroeInProgressPoData = () => {
        setCreatePoData(getValues())
    }

    const calculateDepositAmount = async () => {
      try {
        const payload = {
          data: {
            payment_method: getValues("selectedOptionPayment"),
            price: getValues("materialTotal").toFixed(2),
          },
        };
        const depositResponse = await axios.post(
          import.meta.env.VITE_API_PHP_SERVICE + "/api/v1/widget/deposit",
          payload
        );
        return depositResponse.data.data.deposit_amount;
      } catch (error) {
        setHandleSubmitValidation(false);
      }
    };

    const formatCartItems = (products) => {
        return products.map((item, index) => {
            if (item.descriptionObj) {
                const cartItem = {
                    "line_id": index + 1,
                    "description": item.descriptionObj.UI_Description,
                    "qty": item.qtyVal,
                    "qty_unit": item.qtyUnit,
                    "product_tag": item.partNumber,
                    "product_tag_mapping_id": item.descriptionObj.partNumber,
                    "price": item.umVal,
                    "price_unit": item.umUnit,
                    "extended": item.totalVal,
                    "product_id": item.descriptionObj.Product_ID,
                    "reference_product_id": item.descriptionObj.id,
                    "shape": item.descriptionObj.Key2,
                    "seller_price": item.sellerVal,
                    "seller_extended": item.totalSellerVal.toFixed(2),
                    "buyer_pricing_lb": item.descriptionObj.Buyer_Pricing_LB.replace("$", ""),
                    "domestic_material_only": item.domesticMaterialOnly,
                    "buyer_calculation_price": item.buyerPricePerUnit,
                    "seller_calculation_price": item.sellerPricePerUnit,
                };
                return cartItem
            }
            return null
        }).filter(Boolean);
    }

    const handleGetDeliveryDate = (deliveryDate,recevingHours) => {
        const date = dayjs(deliveryDate)
        const convertDate = date.format('dddd')
        let checkDeliveryDate = null;
        recevingHours?.forEach((recevingHour)=>{
            if(recevingHour.is_user_available === 1 && convertDate.toLowerCase() === recevingHour.day.toLowerCase()){
                checkDeliveryDate = deliveryDate
            }
        })
        return checkDeliveryDate;
    }

    const handleDeliveryDateList = () => {
        return deliveryDates.map(x => {
            const isDeliveryDateDisable = handleGetDeliveryDate(addWorkingDaysToDate(x.days_to_add),getValues('recevingHours'));
            return { title: x.delivery_date_string, value: addWorkingDaysToDate(x.days_to_add), disabled: !isDeliveryDateDisable }
        })
    }

    const handleStateZipValidation = async () => {
        const zipCode = watch("deliverTo.zip");
        const stateCode = watch("deliverTo.state_id");
        if (zipCode) {
            if (zipCode.length > 4 && stateCode) {
            const payload = {
                data: {
                state_id: stateCode,
                zip_code: parseInt(zipCode),
                },
            };
            setApiCallInProgress(true);
            const checkStateZipResponse = await axios.post(import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",payload);
            if (checkStateZipResponse.data.data === true) {
                handleSalesTax();
                clearErrors(["deliverTo.state_id", "deliverTo.zip"]);
            } else {
                setApiCallInProgress(false);
                setError("deliverTo.state_id", {
                message: "The zip code and state code do not match",
                });
                setError("deliverTo.zip", {
                message: "The zip code and state code do not match",
                });
            }
            } else {
            setError("deliverTo.zip", { message: "zip is not valid" });
            }
        }
    };

    const saveUserActivity = (checkOutPoNumber, checkOutError) => {
        const payload = {
            "data": {
                "session_id": sessionId,
                "po_number": checkOutPoNumber ?? null,
                "response": checkOutError ?? null,
                "internal_po_number": getValues("poJobName") ? getValues("poJobName") : null,
                "shipping_details": getValues("deliverTo") ? getValues("deliverTo") : null,
                "delivery_date": getValues("deliveryDate") ? getValues("deliveryDate") : null,
                "payment_method": getValues("selectedOptionPayment") ? getValues("selectedOptionPayment") : null,
            }
        }
        logUserActivity({ url: `${import.meta.env.VITE_API_SERVICE}/user/create_po_open_close`, payload });
      }

      const handleCreatePOSearch = (searchStringKeyword, status, descriptionLineSessionId) => {

        const payload = {
          "data": {
              "session_id": sessionId,
              "keyword" : searchStringKeyword,
              "status" : status,
              "source" : 'create-po',
              "line_session_id": descriptionLineSessionId,
          }
        }
        setSelectedProduct(null)
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/create_po_search', payload)
        .catch(err => console.error(err))
      }

    return (
                <div className={clsx(styles.createPoContent, 'bgBlurContent')} ref={ref}>
                    <div className={styles.formInnerContent}>
                                <div className={styles.headerNoteCreatePO}>
                                    <span className={styles.leftIcon}><WarningIcon /></span>

                                    <span className={styles.headerNoteText}>All delivered material will be new (aka “prime”), fulfilled to the defined specification,
                                        loaded/packaged for forklift and/or magnetic offloading and have mill test reports.</span>
                                    <span className={clsx(styles.headerNoteText, styles.marginTop8)}>The maximum bundle weight is 5,000 pounds.</span>
                                    <span className={styles.rightIcon}><WarningIcon /></span>
                                </div>
                                <div className='w100'>
                                    <div >
                                        <div className={styles.formInputGroup}>
                                            <span className={styles.lblInput}>
                                                Job / PO#
                                                <Tooltip
                                                    title={JobPOTooltip()}
                                                    arrow
                                                    placement={'right'}
                                                    disableInteractive
                                                    TransitionComponent={Fade}
                                                    TransitionProps={{ timeout: 200 }}
                                                    classes={{
                                                        tooltip: 'inputTooltip',
                                                    }}
                                                >
                                                    <span className={styles.questionIcon}>
                                                        <QuestionIcon className={styles.questionIcon1} />
                                                        <QuestionHoverIcon className={styles.questionIcon2} />
                                                        <QuestionBlackIcon className={styles.questionIcon4} />
                                                        <QuestionBlackHoverIcon className={styles.questionIcon3} />
                                                    </span>

                                                </Tooltip>
                                            </span>
                                            <span className={styles.inputSection}>
                                                <input maxLength={20} type='text' {...register('poJobName')} placeholder='Enter Job Name or PO# for this purchase'
                                                    onBlur={(e) => {
                                                        e.target.value = e.target.value.trim();
                                                        register("poJobName").onBlur(e);
                                                        saveUserActivity();
                                                    }} />
                                            </span>
                                        </div>
                                        <div className={styles.formInputGroup}>
                                            <span className={styles.lblInput}>
                                                Delivery To
                                                <Tooltip
                                                    title={DeliveryToTooltip()}
                                                    arrow
                                                    placement={'right'}
                                                    disableInteractive
                                                    TransitionComponent={Fade}
                                                    TransitionProps={{ timeout: 200 }}
                                                    classes={{
                                                        tooltip: 'inputTooltip',
                                                    }}
                                                >
                                                    <span className={styles.questionIcon}>
                                                        <QuestionIcon className={styles.questionIcon1} />
                                                        <QuestionHoverIcon className={styles.questionIcon2} />
                                                        <QuestionBlackIcon className={styles.questionIcon4} />
                                                        <QuestionBlackHoverIcon className={styles.questionIcon3} />
                                                    </span>

                                                </Tooltip>

                                            </span>
                                            <span className={clsx(styles.inputSection, styles.bdrRadius0, styles.bdrRight0)}>
                                                <Tooltip
                                                    title={showLine1Tooltip && watch("deliverTo.line1")}
                                                    arrow
                                                    placement={'bottom'}
                                                    disableInteractive
                                                    TransitionComponent={Fade}
                                                    TransitionProps={{ timeout: 200 }}
                                                    classes={{
                                                        tooltip: 'addressTooltip',
                                                    }}
                                                >
                                                    <input type='text' {...register("deliverTo.line1")} placeholder='Address' onFocus={() => setShowLine1Tooltip(false)}
                                                        onBlur={(e) => {
                                                            setShowLine1Tooltip(true);
                                                            e.target.value = e.target.value.trim();
                                                            register("deliverTo.line1").onBlur(e);
                                                            saveUserActivity();
                                                        }} />
                                                </Tooltip>
                                            </span>
                                            <span className={clsx(styles.inputSection, styles.cityInput, styles.bdrRadius0, styles.bdrRight0)}>
                                            <Tooltip
                                                    title={showCityTooltip && watch("deliverTo.city")}
                                                    arrow
                                                    placement={'bottom'}
                                                    disableInteractive
                                                    TransitionComponent={Fade}
                                                    TransitionProps={{ timeout: 200 }}
                                                    classes={{
                                                        tooltip: 'addressTooltip',
                                                    }}
                                                >
                                                    <input type='text' {...register("deliverTo.city")} placeholder='City' onFocus={() => setShowCityTooltip(false)}
                                                        onBlur={(e) => {
                                                            setShowCityTooltip(true);
                                                            e.target.value = e.target.value.trim();
                                                            register("deliverTo.city").onBlur(e);
                                                            saveUserActivity();
                                                        }} />
                                                </Tooltip>
                                               
                                            </span>
                                            <Tooltip
                                                    title={errors?.deliverTo?.state_id?.message}
                                                    arrow
                                                    placement={'bottom'}
                                                    disableInteractive
                                                    TransitionComponent={Fade}
                                                    TransitionProps={{ timeout: 200 }}
                                                    classes={{
                                                        tooltip: 'addressTooltip',
                                                    }}
                                                >
                                                <span className={clsx(styles.inputSection,styles.paddingLR0, styles.stateInput, styles.bdrRadius0, styles.bdrRight0, errors?.deliverTo?.state_id?.message && styles.errorInput )}>
                                                    <CustomMenu
                                                        control={control}
                                                        name={'deliverTo.state_id'}
                                                        placeholder={'State'}
                                                        MenuProps={{
                                                            classes: {
                                                                paper: styles.Dropdownpaper,
                                                                list: styles.muiMenuList,
                                                            },
                                                        }}
                                                        onChange={() => saveUserActivity()}
                                                        items={states.map(x => ({ title: x.code, value: x.id }))}
                                                        className={'selectDropdown SelectDeliveryDate'}
                                                        
                                                        />

                                                </span>
                                            </Tooltip>
                                            <Tooltip
                                                    title={ errors?.deliverTo?.zip?.message}
                                                    arrow
                                                    placement={'bottom'}
                                                    disableInteractive
                                                    TransitionComponent={Fade}
                                                    TransitionProps={{ timeout: 200 }}
                                                    classes={{
                                                        tooltip: 'addressTooltip',
                                                    }}
                                                >
                                            <span className={clsx(styles.inputSection, styles.zipCodeInput, errors?.deliverTo?.zip?.message && styles.errorInput )}>
                                                <input type='text' {...register("deliverTo.zip")} placeholder='Zip Code' 
                                                  onChange={(e) => {
                                                    register("deliverTo.zip").onChange(e);
                                                    const zipCode = e.target.value.replace(/\D/g, '');
                                                    setValue("deliverTo.zip", zipCode);
                                                }} maxLength="5"
                                                onBlur={(e) => {
                                                    e.target.value = e.target.value.trim();
                                                    register("deliverTo.zip").onBlur(e);
                                                    saveUserActivity();
                                                }}/>
                                            </span>
                                                </Tooltip>
                                        </div>
                                        <div className={clsx(styles.formInputGroup,deliveryStateFocusEnable && styles.formInputGroupFocus)} id='delivery-date-div'>
                                            <span className={styles.lblInput}>
                                                Delivery Date
                                                <Tooltip
                                                    title={DeliveryDateTooltip()}
                                                    arrow
                                                    placement={'right'}
                                                    disableInteractive
                                                    TransitionComponent={Fade}
                                                    TransitionProps={{ timeout: 200 }}
                                                    classes={{
                                                        tooltip: 'inputTooltip',
                                                    }}
                                                >
                                                    <span className={styles.questionIcon}>
                                                        <QuestionIcon className={styles.questionIcon1} />
                                                        <QuestionHoverIcon className={styles.questionIcon2} />
                                                        <QuestionBlackIcon className={styles.questionIcon4} />
                                                        <QuestionBlackHoverIcon className={styles.questionIcon3} />
                                                    </span>

                                                </Tooltip>
                                            </span>
                                            {/* <span className={clsx(styles.inputSection, 'datePickerCreatePO')}>
                                                <Controller
                                                    control={control}
                                                    rules={{
                                                        required: true,
                                                    }}
                                                    name='deliveryDate'
                                                    render={({ field: { value, onChange }, fieldState: { error } }) => (
                                                        <>
                                                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                                                <DatePicker
                                                                    views={['day']}
                                                                    format="MM/DD/YYYY"
                                                                    value={dayjs(value)}
                                                                    control={control}
                                                                    slotProps={{
                                                                        textField: {
                                                                            error: error?.message ? true : false,
                                                                            readOnly: true
                                                                        }
                                                                    }}
                                                                    minDate={dayjs(delivaryDateCalculator(2))}
                                                                    maxDate={dayjs(delivaryDateCalculator(10))}
                                                                    onChange={(newValue) => onChange(datepickerChangeHandler(newValue))
                                                                    }
                                                                />
                                                            </LocalizationProvider>
                                                        </>
                                                    )}
                                                />
                                            </span> */}
                                            <span className={clsx(styles.inputSection,styles.paddingLR0)}>
                                                <CustomMenu
                                                    control={control}
                                                    name={'deliveryDate'}
                                                    placeholder={'Choose One'}
                                                    MenuProps={{
                                                        classes: {
                                                            paper: styles.Dropdownpaper,
                                                            list: styles.muiMenuList,
                                                        },
                                                    }}
                                                    onfocus= {() => {
                                                        setDeliveryStateFocusEnable(true)
                                                    }}
                                                    onBlur= {() => {
                                                        setDeliveryStateFocusEnable(false)
                                                    }}
                                                    onChange={() => saveUserActivity()}
                                                    disabled = {disableDeliveryDate}
                                                    onClick={() => {
                                                        if(disableDeliveryDate){
                                                            setOpenErrorDialog(true)
                                                            setErrorMessage("No dates available. Please add at least one day as receiving hours in settings.")
                                                        }
                                                    }}
                                                    renderValue={(val) => val ? val : 'Choose One'}
                                                    items={handleDeliveryDateList()}
                                                    className={'selectDropdown SelectDeliveryDate'}
                                                />
                                            </span>

                                        </div>
                                        <div className={styles.addPoLineTable}>
                                            <table >
                                                <thead>
                                                    <tr>
                                                        <th><span>LN</span></th>
                                                        <th><span>DESCRIPTION</span></th>
                                                        <th><span>QTY</span></th>
                                                        <th>    
                                                        
                                                            <span className='questionIconPriceChange'>
                                                                $/UM 
                                                                <Tooltip
                                                                    title={chooseYourUnitTooltip()}
                                                                    arrow
                                                                    placement={'bottom'}
                                                                    disableInteractive
                                                                    TransitionComponent={Fade}
                                                                    TransitionProps={{ timeout: 600 }}
                                                                    classes={{
                                                                        tooltip: 'chooseYourUnitTooltip',
                                                                    }}
                                                                >
                                                                    <label>
                                                                        <QuestionIcon className='questionIcon1' />
                                                                        <QuestionHoverIcon className='questionIcon2' />
                                                                    </label>
                                                                    </Tooltip>
                                                                    </span>
                                                           
                                                        </th>
                                                        <th colSpan={2}><span>EXT ($)</span></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {
                                                        fields.map((item, index) => (
                                                            <CreatePoTile key={item.id}
                                                                index={index}
                                                                register={register}
                                                                fields={fields}
                                                                updateValue={updateValue}
                                                                products={products}
                                                                setValue={setValue}
                                                                watch={watch}
                                                                errors={errors}
                                                                control={control}
                                                                qtyUnitM={qtyUnitM}
                                                                priceUnitM={priceUnitM}
                                                                getValues={getValues}
                                                                remove={remove}
                                                                item={item}
                                                                userPartData={userPartData}
                                                                sessionId={sessionId}
                                                                selectedProduct={selectedProduct}
                                                                searchStringData={searchStringData}
                                                                setSearchString={setSearchString}
                                                                setLineSessionId={setLineSessionId}
                                                                lineSessionId={lineSessionId}
                                                                handleCreatePOSearch={handleCreatePOSearch}
                                                                setHandleSubmitValidation={setHandleSubmitValidation}
                                                                handleStateZipValidation={handleStateZipValidation}
                                                                apiCallInProgress={apiCallInProgress}
                                                            />
                                                        ))}
                                                </tbody>
                                            </table>
                                        </div>
                                        
                                        <CommonTooltip
                                                title={'Click this + to add more products to your purchase order'}
                                                tooltiplabel={<>
                                                    <div className={styles.addMoreLine}>
                                                        <button onClick={() => { addNewRow(); }}>
                                                            <AddLineIcon className={styles.addLine} />
                                                            <AddLineHoverIcon className={styles.addLineHover} />
                                                        </button>
                                                        <span></span>
                                                    </div>
                                                
                                                </>
                                                   
                                                }
                                                placement={'bottom-start'}
                                                classes={{
                                                    popper: 'tooltipPopper',
                                                    tooltip: 'tooltipMain tooltipSearch tooltipAddRow',
                                                    arrow: 'tooltipArrow'
                                                }}
                                                localStorageKey="addMoreLineTooltip"
                                            />
                                           
                                         
                                        {/* <div className={styles.totalWeight}>Total Weight: {formatToTwoDecimalPlaces(watch(`totalWeight`))}</div> */}
                                        <div className={styles.totalAmt}>
                                            <table>
                                                <tbody>
                                                    <tr>
                                                        <td><span className={styles.saleTax}>Material Total</span></td>
                                                        <td><span className={styles.saleTax}>$</span></td>
                                                        <td><span className={styles.saleTax}>{formatToTwoDecimalPlaces(!apiCallInProgress && watch(`materialTotal`))}</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><span className={styles.saleTax}>
                                                            Sales Tax
                                                            <Tooltip
                                                                title={SalesTaxTooltip()}
                                                                arrow
                                                                placement={'top-end'}
                                                                disableInteractive
                                                                TransitionComponent={Fade}
                                                                TransitionProps={{ timeout: 200 }}
                                                                classes={{
                                                                    tooltip: 'inputTooltip',
                                                                }}
                                                            >
                                                                <span className={styles.questionIcon}>
                                                                    <QuestionIcon className={styles.questionIcon1} />
                                                                    <QuestionHoverIcon className={styles.questionIcon2} />
                                                                </span>

                                                            </Tooltip>

                                                        </span>
                                                        </td>
                                                        <td><span className={styles.saleTax}>$</span></td>
                                                        <td><span className={styles.saleTax}>{formatToTwoDecimalPlaces(!apiCallInProgress && watch(`salesTax`))}</span></td>
                                                    </tr>
                                                    <tr className={clsx(watch('selectedOptionPayment') !== 'ach_credit' && 'displayNone')}>
                                                        <td><span className={styles.saleTax}>Deposit</span></td>
                                                        <td><span className={styles.saleTax}>$</span></td>
                                                        <td><span className={styles.saleTax}>{formatToTwoDecimalPlaces(!apiCallInProgress && watch(`depositAmount`))}</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><span className={styles.totalPurchase}>Total Purchase</span></td>
                                                        <td><span className={styles.totalPurchase}>$</span></td>
                                                        <td><span className={styles.totalPurchase}>{formatToTwoDecimalPlaces(!apiCallInProgress && watch(`totalPurchase`))}</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div className={styles.btnOfCreatePo}>
                                            <div className={styles.selectedpayment}>

                                            <CommonTooltip
                                                title={'Click this drop down box to select what method of payment you want to use for this order.'}
                                                tooltiplabel={
                                                    <CustomMenu
                                                        control={control}
                                                        name={'selectedOptionPayment'}
                                                        placeholder={'Method of Payment'}
                                                        className={styles.selectPaymentMethod}
                                                        MenuProps={{
                                                            classes: {
                                                                paper: styles.selectPaymentMethodPaper,
                                                            },
                                                        }}
                                                        onOpen={() => setShowPaymentOptionTooltip(false)}
                                                        onClose={() => setShowPaymentOptionTooltip(undefined)}
                                                        items={paymentMethods}
                                                        IconComponent={DropdownIcon}
                                                        onChange={() => saveUserActivity()}
                                                    />
                                                }
                                                open={showPaymentOptionTooltip}
                                                placement={'bottom-start'}
                                                classes={{
                                                    popper: 'tooltipPopper',
                                                    tooltip: 'tooltipMain tooltipSearch tooltipPayment',
                                                    arrow: 'tooltipArrow'
                                                }}
                                                localStorageKey="selectPaymentTooltip"
                                            />

                                            </div>
                                            <button
                                                onClick={handleSubmit(onSubmit)}
                                                disabled={!isValid || Object.keys(errors).length || !handleSubmitValidation }
                                                // onClick={() => navigate(routes.orderConfirmationPage)} 
                                                className={styles.placeOrder}>Place Order</button>
                                        </div>
                                    </div>

                                    <div className={styles.textOfCreatePo}>
                                        <p>After clicking “Place Order,” the next screen will be your order confirmation.</p>
                                        <p>Additionally, you will receive an email confirmation with full order details.</p>
                                    </div>
                                    <div className={styles.backBtnMain}>
                                        <button onClick={() => { setCreatePoData(null); navigate(backNavigation) }} className={styles.cancelPOGoBack}>Cancel PO & Go Back</button>
                                    </div>
                                </div>
                            <Dialog
                                open={openErrorDialog}
                                onClose={(event) => setOpenErrorDialog(false)}
                                transitionDuration={200}
                                hideBackdrop
                                classes={{
                                    root: styles.ErrorDialog,
                                    paper: styles.dialogContent
                                }}

                            >
                                <p>{errorMessage}</p>
                                <button className={styles.submitBtn} onClick={(event) => {setOpenErrorDialog(false); disableDeliveryDate && navigate(routes.buyerSettingPage)}}>Ok</button>
                            </Dialog>

                    </div>
                </div>
    );
}

export default CreatePo;
