package bryzos.extended.pricing.widget;

import android.annotation.SuppressLint;
import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;

import org.json.JSONException;
import org.json.JSONObject;

@CapacitorPlugin(name = "FileDownloadPlugin")
public class FileDownloadPlugin extends Plugin {
    private static final String TAG = "FileDownloadPlugin";

    @PluginMethod()
    public void downloadFile(PluginCall call) {
        String fileUrl = call.getString("url");
        String title = call.getString("title", "File Download");
        String fileName = call.getString("fileName", "");
        String accessToken = call.getString("accessToken", "");
        String mimeType = call.getString("mimeType");
        String description = call.getString("description", "Downloading file...");
        JSONObject headers = call.getObject("headers");

        if (TextUtils.isEmpty(fileName)) {
            call.reject("File name is required");
            return;
        }

        if (TextUtils.isEmpty(fileUrl)) {
            call.reject("File URL must be provided");
            return;
        }

        try {
            DownloadManager.Request request = new DownloadManager.Request(Uri.parse(fileUrl));
            if (headers != null) {
                for (int i = 0; i < headers.names().length(); i++) {
                    String key = headers.names().getString(i);
                    String value = headers.getString(key);
                    request.addRequestHeader(key, value);
                }
            }
            request.setTitle(title);
            request.setDescription(description);
            request.setMimeType(mimeType);
            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
            request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, fileName);

            DownloadManager downloadManager = (DownloadManager) getContext().getSystemService(Context.DOWNLOAD_SERVICE);
            long downloadID = downloadManager.enqueue(request);
            boolean finishDownload = false;

            while (!finishDownload) {
                Cursor cursor = downloadManager.query(new DownloadManager.Query().setFilterById(downloadID));
                if (cursor.moveToFirst()) {
                    @SuppressLint("Range")
                    int status = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS));

                    switch (status) {
                        case DownloadManager.STATUS_FAILED: {
                            finishDownload = true;
                            call.reject("unable to download file");
                            break;
                        }
                        case DownloadManager.STATUS_PAUSED:
                            break;
                        case DownloadManager.STATUS_PENDING:
                            break;
                        case DownloadManager.STATUS_RUNNING: {
                            @SuppressLint("Range")
                            final long total = cursor.getLong(cursor.getColumnIndex(DownloadManager.COLUMN_TOTAL_SIZE_BYTES));
                            if (total >= 0) {
                                @SuppressLint("Range")
                                final long downloaded = cursor.getLong(cursor.getColumnIndex(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR));
                                if (downloaded == total) {
                                    finishDownload = true;
                                }
                            }
                            break;
                        }
                        case DownloadManager.STATUS_SUCCESSFUL: {
                            finishDownload = true;
                            break;
                        }
                    }
                }
            }

            BroadcastReceiver receiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    long id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1);
                    String action = intent.getAction();

                    if (downloadID == id && DownloadManager.ACTION_DOWNLOAD_COMPLETE.equals(action)) {
                        DownloadManager.Query query = new DownloadManager.Query();
                        query.setFilterById(id);

                        Cursor cursor = downloadManager.query(query);

                        if (!cursor.moveToFirst()) {
                            cursor.close();
                            call.reject("unable to move cursor to first position");
                            return;
                        }

                        @SuppressLint("Range")
                        int statusId = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS));

                        if (statusId == DownloadManager.STATUS_SUCCESSFUL) {
                            @SuppressLint("Range")
                            Uri uri = Uri.parse(cursor.getString(cursor.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI)));

                            JSObject result = new JSObject();
                            result.put("status", "completed");
                            result.put("filePath", uri.toString());
                            result.put("downloadId", downloadID);
                            call.resolve(result);
                        } else {
                            call.reject("unable to download file");
                        }

                        cursor.close();
                    } else {
                        call.reject("unable to download file");
                        return;
                    }
                }
            };

            getContext().registerReceiver(receiver, new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE));
                
            Cursor cursor = downloadManager.query(new DownloadManager.Query().setFilterById(downloadID));
            if (cursor.moveToFirst()) {
                @SuppressLint("Range")
                int status = cursor.getInt(cursor.getColumnIndex(DownloadManager.COLUMN_STATUS));

                if (status == DownloadManager.STATUS_FAILED) {
                    call.reject("unable to download file");
                }
                cursor.close();
            }

        } catch (JSONException e) {
            call.reject("Error parsing headers: " + e.getMessage());
        } catch (Exception e) {
            call.reject("Error initiating download: " + e.getMessage());
        }

    }
}
