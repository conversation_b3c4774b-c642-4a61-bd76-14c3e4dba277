// .loader {
//     width: 100px;
//     height: 100px;
//     position: relative;
//     background: transparent;
//     border-radius: 4px;
//     perspective: 500px;
//   }
  
//   .loader:before {
//     content: "";
//     position: absolute;
//     left: 2px;
//     top: 2px;
//     width: 48px; /* Adjust to the width of your custom image */
//     height: 48px; /* Adjust to the height of your custom image */
//     background-image: url('../../../assets/mobile-images/loaderLogo.svg'); /* Path to your custom image */
//     background-size: cover; /* This will ensure that your image covers the entire area of the circle */
//     background-repeat: no-repeat; /* This will prevent the image from repeating within the element */
//     background-position: center; /* This will center your image within the element */
//     transform-origin: 100% 100%;
//     animation: flip 2s linear infinite;
//   }
  
  
//   @keyframes flip {
//     0% , 100%{ transform: rotateX(0deg) rotateY(0deg) }
//     25%{ transform: rotateX(0deg) rotateY(-180deg) }
//     50%{ transform: rotateX(-180deg) rotateY(-180deg) }
//     75%{ transform: rotateX(-180deg) rotateY(0deg) }
//   }
.backDrop{
  background-color: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
}
.LoaderMain{
  height: 100%;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.dotPulse {
  position: relative;
  left: -9999px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #70ff00;
  color: #70ff00;
  box-shadow: 9999px 0 0 -5px;
  animation: dotPulse 1.5s infinite linear;
  animation-delay: 0.25s;
  z-index: 99999;
}
.dotPulse::before, .dotPulse::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #70ff00;
  color: #70ff00;
}
.dotPulse::before {
  box-shadow: 9984px 0 0 -5px;
  animation: dotPulse-before 1.5s infinite linear;
  animation-delay: 0s;
}
.dotPulse::after {
  box-shadow: 10014px 0 0 -5px;
  animation: dotPulse-after 1.5s infinite linear;
  animation-delay: 0.5s;
}

@keyframes dotPulse-before {
  0% {
    box-shadow: 9984px 0 0 -5px;
  }
  30% {
    box-shadow: 9984px 0 0 2px;
  }
  60%, 100% {
    box-shadow: 9984px 0 0 -5px;
  }
}
@keyframes dotPulse {
  0% {
    box-shadow: 9999px 0 0 -5px;
  }
  30% {
    box-shadow: 9999px 0 0 2px;
  }
  60%, 100% {
    box-shadow: 9999px 0 0 -5px;
  }
}
@keyframes dotPulse-after {
  0% {
    box-shadow: 10014px 0 0 -5px;
  }
  30% {
    box-shadow: 10014px 0 0 2px;
  }
  60%, 100% {
    box-shadow: 10014px 0 0 -5px;
  }
}