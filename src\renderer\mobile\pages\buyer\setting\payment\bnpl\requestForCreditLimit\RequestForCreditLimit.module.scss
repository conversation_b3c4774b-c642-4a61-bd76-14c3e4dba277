.requestcreditlineTopSection {
    display: flex;
    margin-bottom: 12px;
}

.requestcreditlineIncreaseText {
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #70ff00;
}

.pendingText {
    font-family: Noto Sans Display;
    font-size: 14px;
    line-height: 1.6;
    text-align: left;
    color: #ff5d47;
}

label {
    margin-bottom: 4px;
    display: block;
}

.requestcreditlineInputGrid {
    display: flex;
    grid-gap: 12px;
    width: 100%;
    margin-bottom: 8px;

    .lblCreditLine {
        padding: 10px 8px 10px 12px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.1);
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.6;
        text-align: left;
        color: #fff;
        height: 40px;
        span{
                font-size: 14px;
                font-weight: 300;
                line-height: 1.2;
                color: #ffaea3;
                margin-left: 4px;
                @media (max-width:360px) {
                    font-size: 12px;
                }
        }
        &.pendingReqlbl{
            padding-right: 2px;
        }
    }
}

// Radio Button Style
.containerRadio.containerRadio {
    display: inline-block;
    position: relative;
    cursor: pointer;
    padding-left: 25px;
    text-align: left;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: 300;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    margin-right: auto;

    input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }

    .checkmark {
        position: absolute;
        top: 3px;
        left: 0;
        z-index: 1;
        width: 15px;
        height: 15px;
        border: solid 1px #fff;
        background-color: #6a6a6a;
        border-radius: 50%;
    }

    .checkmark:after {
        content: "";
        position: absolute;
        display: none;
    }

    input:checked~.checkmark {
        background-color: transparent;
        border: solid 1px #70ff00;
    }

    input:checked~.checkmark:after {
        display: block;
    }

    .checkmark:after {
        left: 3px;
        top: 3px;
        width: 6.7px;
        height: 6.7px;
        background-color: #70ff00;
        border-radius: 50%;
    }
}

.popupContent {
    padding: 40px 16px 24px 16px;
    height: 100%;
}

.requestingCreditlineincreaseTitle {
    font-family: Noto Sans Display;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    margin-top: 0px;
    margin-bottom: 12px;
}

.closePopupBtn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 9;
}

.lblCreditLine {
    display: block;
    opacity: 0.7;
    font-family: Noto Sans Display;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
}

.requestCreditLineInput {
    width: 100%;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 10px 8px 10px 12px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 0px;
    box-shadow: none;
    font-family: Noto Sans Display;
    font-size: 14px;
    line-height: 1.6;
    letter-spacing: normal;
    text-align: left;
    color: #fff;

    &:focus{
        outline: none;
        background-color: rgba(0, 0, 0, 0.5);
    }

    &::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }
}

.btnSectionGrid{
    display: flex;
    gap:12px;
    margin-top: 40px;
}

.cancelBtn {
    height: 40px;
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 10px 24px;
    border-radius: 8px;
    border: solid 1px #fff;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    text-align: center;
    color: #fff;
}

.submitBtn {
    height: 40px;
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 10px 24px;
    border-radius: 8px;
    background-color: #70ff00;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    text-align: center;
    color: #000;
    &:disabled{
        background-color: rgba(255, 255, 255, 0.1);
        opacity: 0.3;
    }
}

.reqSentSuccessPopup{
    padding: 54px 16px 24px 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    h1{
        font-family: Noto Sans Display;
        font-size: 18px;
        font-weight: 600;
        line-height: 1.4;
        color: #70ff00;
        margin-top: 8px;
    }
    .doneBtn {
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px 24px;
        border-radius: 8px;
        background-color: #fff;
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.4;
        text-align: center;
        color: #000;
        margin-top: auto;
    }
}

.errorText1{
    font-family: Noto Sans Display;
    font-size: 13px;
    font-weight: normal;
    line-height: 1;
    text-align: left;
    color: #ff0000;
    position: absolute;
    padding-top: 10px;
}