import styles from './instantPurchasingSteps.module.scss';
import { useEffect, useRef, useState } from "react";
import SearchProductList from "../../../../components/searchProductList/SearchProductList";
import { formatToTwoDecimalPlaces,  formatDollarPerUnit, removeCommaFromCurrency, formatCurrencyWithComma, useGlobalStore, useSaveProductSearchAnaytic, MinSearchDataLen } from "@bryzos/giss-ui-library";
import useGetUserPartData from "../../../../library/hooks/useGetUserPartData";
import { CustomMenu } from "../../../../components/CustomMenu";
import clsx from "clsx";
import { Fade, Tooltip } from "@mui/material";
import { ReactComponent as AddLineIcon } from '../../../../../assets/mobile-images/Icon_Add.svg';
import { ReactComponent as DeleteLineIcon } from '../../../../../assets/mobile-images/close_Popup.svg';
import DisplayProductDescription from "../../../../components/DisplayProductDescription";
import { v4 as uuidv4 } from 'uuid';
import { useDebouncedValue } from "@mantine/hooks";

let cartItem = {
    descriptionObj: '',
    description: '',
    qty: '',
    qty_unit: '',
    price_unit: '',
    product_tag: '',
    domestic_material_only: false,
    qtyUnitM:[],
    priceUnitM:[],
    line_session_id:'',
}

const SAME_TAG_ERROR_MESSAGE = "Same part? <br /> You have applied this part number to another product already.";

function InstantPurchasingSteps2(props: any) {
    const [searchString, setSearchString] = useState('');
    const [selectedProducts, setSelectedProducts] = useState([]);
    const [lineSessionId, setLineSessionId] = useState('');
    const [handleSubmitValidation, setHandleSubmitValidation] = useState(false);
    const [enableRejectSearchAnalytic, setEnableRejectSearchAnalytic]= useState(false);
    const [sameTagErrorMsg, setSameTagErrorMsg] = useState<any>(null);
    const { data: userPartData } = useGetUserPartData();
    const [products, setProducts] = useState([]);
    const { userData , productData }: any = useGlobalStore();
    const ordersContainerRef = useRef<any>();
    const searchProductInputRef = useRef<any>(null);
    const [deboucedSearchString] = useDebouncedValue(searchString, 400);
    const saveProductSearch = useSaveProductSearchAnaytic();

    useEffect(() => {
        setProducts(productData)
    },[productData]);

    useEffect(()=>{
        checkBtnStatus()
    }, [props.watch("price")])

    useEffect(()=>{
        if(props.watch("cart_items").length === 0 && Object.keys(props.errors ?? {}).length === 0){
            addToCartItem(0);
            props.setMaxStepsEnabled(2);
        }
        checkBtnStatus()
    }, [props.watch("cart_items").length])

    useEffect(() => {
        if(deboucedSearchString && (searchString.length >= MinSearchDataLen) || (searchString.length >= MinSearchDataLen)){
            handleCreatePOSearch(searchString, null, lineSessionId)
        }
    }, [deboucedSearchString])

    useEffect(() => {
        if((searchString.length >= MinSearchDataLen)){
            props.hideHeader && setLineSessionId(props.getValues(`cart_items.${props.hideHeader}.line_session_id`))
        }
        if(searchString.length === 1 ){
          setEnableRejectSearchAnalytic(true)
        }
        if(searchString.length === 0 && deboucedSearchString.length !== 0 && deboucedSearchString.length >= MinSearchDataLen && enableRejectSearchAnalytic){
          handleCreatePOSearch(deboucedSearchString, 'Reject', lineSessionId)
          setEnableRejectSearchAnalytic(false)
        }
      },[searchString])

      const handleCreatePOSearch = (searchStringKeyword: string, status: string | null, descriptionLineSessionId: string) => {

        const payload = {
          "data": {
              "session_id": props.sessionId,
              "keyword" : searchStringKeyword,
              "status" : status,
              "source" : 'create-po',
              "line_session_id": descriptionLineSessionId,
          }
        }
        saveProductSearch.mutateAsync(payload)
        .catch(err => console.error(err))
      }

    const addToCartItem = (index: any) => {
        cartItem.line_session_id = uuidv4();
        props.setValue(`cart_items.${index}`, cartItem);
        setHandleSubmitValidation(false);
        props.setMaxStepsEnabled(2);
        setTimeout(() => {
            scrollToBottomOfOrdersList()
        }, 0);
        if (index > 0) saveUserLineWiseActivity(false, index)
    }

    const checkBtnStatus = () => {
        const isQtyFilled = props.watch("cart_items").some((item: {
            description: string; qty: string;
        }) => item.qty?.length === 0 || item.description?.length === 0)
        setHandleSubmitValidation(!(Object.keys(props.errors ?? {}).length > 0) && !isQtyFilled);
    }

    const productSearchHandler = (e: any) => {
        setSearchString(e.target.value);
    }

    const selectProduct = (product: any) => {
        const selectedItemData = {
            line_id: parseInt(props.hideHeader) + 1,
            product_id: product.Product_ID,
            reference_product_id: product.id,
            buyer_pricing_lb: product.Buyer_Pricing_LB.replace("$", ""),
            description: product.UI_Description,
            shape: product.Key2,
            descriptionObj: product,
            qty:'',
            line_session_id: lineSessionId, 
        }
        props.setValue(`cart_items.${props.hideHeader}`, selectedItemData)
        if (userPartData && Object.keys(userPartData)?.length) {
            props.setValue(`cart_items.${props.hideHeader}.product_tag`, userPartData[product?.Product_ID])
            setAndCheckProductTag(product?.Product_ID)
        }
        if (!product.domestic_material_only) {
            props.setValue(`cart_items.${props.hideHeader}.domestic_material_only`, null);
        } else {
            props.setValue(`cart_items.${props.hideHeader}.domestic_material_only`, false);
        }
        setEnableRejectSearchAnalytic(false)
        handleCreatePOSearch(deboucedSearchString, 'Accept', lineSessionId)
        saveUserLineWiseActivity(false, props.hideHeader)
        resetQtyAndPricePerUnitFields(parseInt(props.hideHeader), product);
        props.setHideHeader(false)
        setHandleSubmitValidation(false);
        setSearchString('')
    }

    const resetQtyAndPricePerUnitFields = (index: any, product: any) => {
        const qtyUnitMData = product.QUM_Dropdown_Options.split(",")
        const priceUnitMData = product.PUM_Dropdown_Options.split(",")
        props.setValue(`cart_items.${index}.qty_um`,qtyUnitMData);
        props.setValue(`cart_items.${index}.price_um`,priceUnitMData);
        props.setValue(`cart_items.${index}.qty_unit`, qtyUnitMData[0])
        props.setValue(`cart_items.${index}.price_unit`, priceUnitMData[0])
        props.pricePerUnitChangeHandler(index, product);
    }

    const setAndCheckProductTag = (productId: any) => {
        let isMappingExist = false;
        const i = Object.keys(userPartData ?? {}).findIndex((key) => {
            if (userPartData[key] === userPartData[productId]) {
                isMappingExist = true;
                if (+key === productId) {
                    return true;
                }
            }
        })
        setSameTagErrorMsg(i > -1 ? null : SAME_TAG_ERROR_MESSAGE);
        if (!isMappingExist) {
            setSameTagErrorMsg(null);
        }
    }

    const updateValue = (index: number)=>{
        if(props.getValues(`cart_items.${index}`).descriptionObj){
            props.updateLineItem(index);
            props.calculateMaterialTotalPrice();
        }
    }

    const dollerPerUmFormatter = (umVal: any, index: any) => {
        const umUnit = props.getValues(`cart_items.${index}.price_unit`);
        return formatDollarPerUnit(umUnit,umVal,index);
    }

    const quantityChangeHandler = (event:any, index:number) => {
        let value = removeCommaFromCurrency(event.target.value);
        event.target.value = value;
        props.setValue(`cart_items.${index}.qty`, value);
        props.quantitySizeValidator(event,index);
        updateValue(index);
    }

    const scrollToBottomOfOrdersList = () => {
        if(ordersContainerRef.current)
        ordersContainerRef.current.scrollTop = ordersContainerRef.current.scrollHeight;
    }
    const clearOrRemoveLineItem = (index: number) => {
        if (props.getValues('cart_items')?.length !== 1)
            props.removeLineItem(index);
        else{
            addToCartItem(0)
            props.setValue('price', null)
        }
        if (props.errors?.cart_items) {
            props.errors.cart_items.splice(index, 1);
            if (props.errors.cart_items.every((x: undefined) => x === undefined)) props.clearErrors('cart_items');
        }
        saveUserLineWiseActivity(true, index)
    }


    const saveUserLineWiseActivity = (isRemovingLine:boolean = false, index: number) => {
        props.setMaxStepsEnabled(2)
        props.saveUserLineActivity(props.sessionId, isRemovingLine, +index);
    }

    return (
            <>
                            <div className={clsx(styles.step2Content, props.hideHeader && styles.hide)} ref={ordersContainerRef}>
                                {props.getValues("cart_items").map((item: any, index: any) => (
                                    <div className={styles.stepsContent} key={index}>
                                        <div className={styles.lineNoHeader}>
                                            <div className={styles.stepsTitle}>Line {index + 1}</div>
                                            <div className={styles.btnDelLineNo}>
                                                <button onClick={() => clearOrRemoveLineItem(index)}>
                                                    {(index !== 0 || props.getValues('cart_items')[0].description || props.getValues('cart_items')[0].qty) &&
                                                        <DeleteLineIcon/>
                                                    }
                                                </button>
                                            </div>
                                        </div>

                                        <div className={styles.stepsDescription}>Description</div>
                                        <div className={styles.DescriptionsInput}>
                                            <div onClick={() => {props.setHideHeader(index.toString()); searchProductInputRef.current.focus();}}>
                                                {item.description ?
                                                    <div className={clsx(styles.liBody,styles.selectedDescriptionInputProduct)}>
                                                        <DisplayProductDescription
                                                        description={item?.description}/>
                                                    </div>
                                                    :
                                                    <>
                                                        <p className={styles.descriptionText}>Tap <a>here</a> to get started</p>
                                                        <div className={styles.descriptionInputProduct}>Enter your product description and choose the required product from the dropdown.</div>
                                                    </>
                                                }
                                            </div>
                                            <Tooltip
                                                title={
                                                    sameTagErrorMsg && (
                                                        <span
                                                            dangerouslySetInnerHTML={{ __html: sameTagErrorMsg }}
                                                        ></span>
                                                    )
                                                }
                                                arrow
                                                placement={"bottom-end"}
                                                disableInteractive
                                                TransitionComponent={Fade}
                                                TransitionProps={{ timeout: 200 }}
                                                classes={{
                                                    tooltip: "partNumberTooltip",
                                                }}
                                            >
                                                <div>
                                                    <input
                                                        type="textg"
                                                        {...props.register(`cart_items.${index}.product_tag`)}
                                                        onChange={(e) => {
                                                            props.register(`cart_items.${index}.product_tag`).onChange(e)
                                                            setAndCheckProductTag(item.product_id)
                                                        }}
                                                        onBlur={()=>saveUserLineWiseActivity(false, index)}
                                                        placeholder="Tag description with your part #"
                                                    />
                                                </div>
                                            </Tooltip>
                                        </div>
                                        <div>
                                            <div className={styles.qtyGrid}>
                                                <div className={styles.stepsDescription}>Quantity</div>
                                                <div className={styles.stepsDescription}>Price/UM</div>
                                            </div>
                                         
                                            <div className={styles.qtyGrid}>
                                                <div className={clsx(styles.qtyInput, props.errors?.cart_items?.[index]?.qty?.message &&
                                                            item.qty !== null &&
                                                            styles.errorInput)}>
                                                    <input
                                                        type="tel"
                                                        inputMode="decimal"
                                                        placeholder="Ex: 1,000"
                                                        value={+item.qty ? formatCurrencyWithComma(item.qty) : item.qty}
                                                        onChange={(e) => {
                                                            quantityChangeHandler(e, index);
                                                        }}
                                                        onBlur={(e) => {
                                                            props.register(`cart_items.${index}.qty`).onBlur(e);
                                                              saveUserLineWiseActivity(false, index);
                                                        }}
                                                    />
                                                    <CustomMenu
                                                        name={props.register(`cart_items.${index}.qty_unit`).name}
                                                        control={props.control}
                                                        disabled={!!!props.getValues(`cart_items.${index}.qty_um`)}
                                                        onChange={() => {
                                                            updateValue(index);
                                                            saveUserLineWiseActivity(false, index);
                                                        }}
                                                        items={
                                                            props.getValues(`cart_items.${index}.qty_um`)?.map((x: any) => ({ title: x, value: x })) ?? []
                                                        }
                                                        className='selectDropdown uomDrodown'
                                                        MenuProps={{
                                                            classes: {
                                                                paper: clsx(styles.Dropdownpaper,styles.priceUnitDropdown),
                                                            },
                                                        }}
                                                        placeholder={"Ft"}
                                                    />
                                                    {props.errors?.cart_items?.[index]?.qty?.message && item.qty !== null &&
                                                      <span className={styles.qtyError}>{props.errors?.cart_items?.[index]?.qty?.message}</span>
                                                    }
                                                </div>
                                                <div className={clsx(styles.qtyInput, styles.priceDropMain)}>
                                                    <div className={styles.inputPrice}>
                                                        $ {dollerPerUmFormatter(props.watch(`cart_items.${index}.price`) ?? 0, index)}
                                                    </div>
                                                    <span className={styles.selectUom1}>
                                                        <CustomMenu
                                                            name={props.register(`cart_items.${index}.price_unit`).name}
                                                            control={props.control}
                                                            disabled={!!!props.getValues(`cart_items.${index}.price_um`)}
                                                            onChange={() => {
                                                                props.pricePerUnitChangeHandler(index, undefined);
                                                                saveUserLineWiseActivity(false, index);
                                                            }}
                                                            items={
                                                                props.getValues(`cart_items.${index}.price_um`)?.map((x: any) => ({ title: x, value: x })) ?? []
                                                            }
                                                            className='selectDropdown uomDrodown priceDropdown'
                                                            MenuProps={{
                                                                classes: {
                                                                    paper: clsx(styles.Dropdownpaper,styles.priceUnitDropdown),
                                                                },
                                                            }}
                                                            placeholder={"Ft"}
                                                        />
                                                    </span>
                                                </div>
                                            </div>
                                          
                                        </div>

                                        <div className={styles.calcGrid}>
                                            <div className={clsx(styles.grid1, styles.domesticMaterialGrid)}>
                                                {props.watch(`cart_items.${index}.domestic_material_only`) !== null && (
                                                    <>
                                                        <label className={styles.switch}>
                                                            <input
                                                                type="checkbox"
                                                                checked={props.watch(`cart_items.${index}.domestic_material_only`)}
                                                                {...props.register(`cart_items.${index}.domestic_material_only`)}
                                                                onChange={(e) => {
                                                                    props.register(`cart_items.${index}.domestic_material_only`).onChange(e);
                                                                    saveUserLineWiseActivity(false, index);
                                                                }}
                                                            />
                                                            <span className={clsx(styles.slider, styles.round)}></span>
                                                        </label>
                                                        <span className={styles.domesticMaterialTex}>
                                                            Domestic (USA)<br /> Material Only
                                                        </span>
                                                    </>

                                                )}
                                            </div>
                                            <div className={styles.grid1}>
                                                <label className={styles.extendedLbl}> Extended </label>
                                                <div className={styles.cartitemsPrice}>
                                                    $ {formatToTwoDecimalPlaces(props.watch(`cart_items.${index}.extended`) ?? 0)}
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                ))}
                                
                            </div>
                            <div className={clsx(styles.totalAmountGrid, props.hideHeader && styles.hide)}>
                                <div className={styles.totalPriceGrid}>
                                    <div className={styles.totalAmount}>
                                        <span>Total</span>
                                        <span>$ {formatToTwoDecimalPlaces(props.watch(`price`))}</span>
                                    </div>
                                </div>
                                <div className={styles.addLineAndProceedBtn}>
                                    <button className={styles.textLeft} onClick={() => addToCartItem(props.getValues('cart_items').length)}><AddLineIcon/>Add Line</button>
                                    <span className={styles.lineGrid}></span>
                                    <button className={clsx(styles.textRight,styles.proceedToStepBtn)} onClick={() => props.setSelectedInstantPricingStep(3)} disabled={!handleSubmitValidation} > Proceed to<br/>Step 3</button>
                                </div>
                            </div> 
                    <div className={styles.searchProdInstantPrice}>
                    <SearchProductList
                        lineNo={parseInt(props.hideHeader) + 1}
                        setHideHeader={props.setHideHeader}
                        setSearchString={setSearchString}
                        searchProductInputRef={searchProductInputRef}
                        searchString={searchString}
                        selectedProducts={selectedProducts}
                        productSearchHandler={productSearchHandler}
                        products={products}
                        userPartData={userPartData}
                        selectProduct={selectProduct}
                        excludeSafeProduct={true}
                    />
                    </div>

            </>
    );
}
export default InstantPurchasingSteps2;
