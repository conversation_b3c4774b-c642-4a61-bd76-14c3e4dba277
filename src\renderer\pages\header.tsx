// @ts-nocheck
import axios from 'axios';
import { useContext, useEffect, useState } from 'react';
import { ReactComponent as CloseIcon } from '../assets/images/Icon_Close.svg';
import { ReactComponent as MinimizeAppIcon } from '../assets/images/Minimize_App.svg';
import { ReactComponent as PinIcon } from '../assets/images/icon-pin.svg';
// import {ReactComponent as NotificationIcon} from '../assets/images/notification.svg';
import { ReactComponent as SettingsIcon } from '../assets/images/settings.svg';
import { ReactComponent as ActiveSettingsIcon } from '../assets/images/settings-active.svg';
import { ReactComponent as PinSettingsIcon } from '../assets/images/Pin-Active.svg';
import { ReactComponent as ShareIcon } from '../assets/images/Share.svg';
import { UserContext } from '../UserContext';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Tooltip } from '@mui/material';
import Fade from '@mui/material/Fade';
import {
    localStorageStickyItemKey,
    toggleStickyBtnId,
    routes,
    userRole,
    channelWindowList,
} from "../../common";
import { pinTooltip, shareappTooltip, signupTooltip } from '../tooltip';
import { Auth } from 'aws-amplify';
import { createSocket, getSocketConnection, resetConnection } from '../helper/socket';
import { useStore } from '../helper/store';
import clearPageSessionData from '../utility/clearPageSessionData';
import { CommonTooltip } from '../component/Tooltip/tooltip';
import { getChannelWindow, navigatePage } from '../helper';
import useSnackbarStore from '../component/Snackbar/snackbarStore';

function handleToggleStickyButtonStyle(isSticky, toggleStickyBtn) {
    if (isSticky) {
        toggleStickyBtn.classList.add('pinFocus');
    } else {
        toggleStickyBtn.classList.remove('pinFocus');
    }
}

function Header(props) {
    const navigate = useNavigate();
    const location = useLocation();
    const userContext = useContext(UserContext);
    const userData = userContext.user.data;
    const showAlertForNewPo = useStore(state => state.showAlertForNewPo);
    const setEnableShareWidget = useStore(state => state.setEnableShareWidget);
    const enableShareWidget = useStore(state => state.enableShareWidget);
    const setForbiddenTooltips = useStore(state => state.setForbiddenTooltips);
    const channelWindow = Object.keys(getChannelWindow()).length ? getChannelWindow() : channelWindowList;
    const {setBackdropOverlay} = useStore();
    const {resetSnackbarStore } = useSnackbarStore();

    const closeBtnClick = () => {
        if(channelWindow?.close){
            window.electron.send({ channel: channelWindow.close })
        }
    }

    const minimizeBtnClick = () => {
        console.log('minimize dd ', channelWindow)
        if(channelWindow?.minimize){
            window.electron.send({ channel: channelWindow.minimize })
        }
    }
    const toggleAlwaysOnTop = () => {
        let isSticky;
        if(channelWindow?.sticky){
            isSticky = window.electron.sendSync({ channel: channelWindow.sticky, data: null });
        }
        // Save the isSticky value in localStorage
        sessionStorage.setItem(localStorageStickyItemKey, isSticky ?? null);

        return isSticky;
    }
    const setResetHeaderConfig = useStore(state => state.setResetHeaderConfig);
    const setShowLoader = useStore(state => state.setShowLoader);
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const setDisableBryzosNavigation = useStore(state => state.setDisableBryzosNavigation);
    const createPoSessionId = useStore(state => state.createPoSessionId);
    const setCreatePoSessionId = useStore(state => state.setCreatePoSessionId);


    useEffect(() => {
        const toggleStickyBtn = document.getElementById(toggleStickyBtnId);
        if (toggleStickyBtn) {
            toggleStickyBtn.addEventListener('click', () => {
                const isSticky = toggleAlwaysOnTop();
                handleToggleStickyButtonStyle(isSticky, toggleStickyBtn);
            });
        }
    }, []);

    useEffect(() => {
        const toggleStickyBtn = document.getElementById(toggleStickyBtnId);
        if (toggleStickyBtn) {
            let isSticky = JSON.parse(sessionStorage.getItem(localStorageStickyItemKey));
            handleToggleStickyButtonStyle(isSticky, toggleStickyBtn);
        }
    }, []);


    useEffect(() => {
        if(props.forceLogout){
            handleLogout(true);
            props.setForceLogout(false);
        }
    }, [props.forceLogout])

    const handleLogout = async (isForceLogout) => {
        if (userData && !isLoggingOut) {
            userContext.setUser({ data: null }); //Doing this to avoid flicker while navigating away
            resetSnackbarStore();
            setBackdropOverlay(false)
            navigatePage(location.pathname, {path: routes.loginPage})
            setForbiddenTooltips([]);
            setIsLoggingOut(true);
            if(!isForceLogout){
            let payload = {
                data: {
                    'email_id': userData.email_id
                }
            }
            const pageSessionData = {
                createPoSessionId: createPoSessionId,
            } 
            await clearPageSessionData(location.pathname, pageSessionData);
            if(channelWindow?.systemVersion){
                const systemVersion = window.electron.sendSync({ channel: channelWindow.systemVersion, data: null})
                payload.data.os_version = systemVersion;
                payload.data.last_login_app_version = props.appVersion;
            }
            axios.post(import.meta.env.VITE_API_SERVICE + '/user/logout', payload)
                .finally(async () => {
                    clearOnLogout();
                })
            }
            else clearOnLogout();
        }
    }

    const clearOnLogout = async() => {
        try {
            await Auth.signOut();
            setCreatePoSessionId(null)
        } catch (error) {
            console.error('error signing out: ', error);
        }
        if(channelWindow?.logout)
        window.electron.send({ channel: channelWindow.logout });
        if(channelWindow?.badgeCountHandler)
        window.electron.send({ channel: channelWindow.badgeCountHandler, data: {type:'reset'}});
        setIsLoggingOut(false);
        userContext.setUser({ data: null });
        if (getSocketConnection()) {
            console.log("LOGOUT");
            createSocket()?.disconnect();
        }
        for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            if (key !== "isSticky" && key !== "localStorageStickyItemKey") {
                sessionStorage.removeItem(key);
            }
        }
        document.cookie.split(";").forEach((c) => {
            document.cookie = c.replace(/^ +/, "").replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);
        });
        resetConnection();
        setResetHeaderConfig(true);
        setDisableBryzosNavigation(true);
        setShowLoader(false);
    }

    const handleGoToBuyerSettingPage = (event) => {
        if(location.pathname !== routes.buyerSettingPage && location.pathname !== routes.sellerSettingPage){
            setShowLoader(true);
            if (userData.type === userRole.buyerUser) {
                navigatePage(location.pathname, {path: routes.buyerSettingPage})
            } else if (userData.type === userRole.sellerUser) {
                navigatePage(location.pathname, {path: routes.sellerSettingPage})
            }
        }
    }

    const gotoSearchPage = () => {
        setEnableShareWidget(false);
        if (userData && !props.disableBryzosNavigation) {
            navigatePage(location.pathname, {path: routes.homePage})
            if (props.handleCloseWidget) {
                props.handleCloseWidget()
            }
        }
    }
    const gotoShareWidgetPage = () => {
        setEnableShareWidget(true);
        navigatePage(location.pathname, {path:routes.homePage, state: {enableShareWidget: true}});
    }

    if(location.pathname === routes.newUpdate){
        return <></>
    }

    return (
        <> 
            <div className={`bryzos ${location.pathname !== routes.loginPage ? 'resetPosition':'' }`}>
                <div className={'headerTitle'}
                >
                    <span className={`bryzosTitle ${(userData && !props.disableBryzosNavigation) ? 'addCursorPointer' : ''}`} onClick={gotoSearchPage}> 
                    
                        <CommonTooltip
                            title={"Click the Bryzos icon to return to Price Search screen at any time"}
                            tooltiplabel={'Bryzos'}
                            placement={'bottom-start'}
                            classes={{
                                popper: 'tooltipPopper',
                                tooltip: 'tooltipMain',
                                arrow: 'tooltipArrow'
                            }}
                            localStorageKey="bryzosTextTooltip"
                        />
                    </span>

                        <CommonTooltip
                            title={'Click here to log out of your account'}
                            tooltiplabel={<span onClick={() =>handleLogout(false)} className={`${userContext.user.data !== null ? 'bedgeLogin' : 'bedgeLogout'}`}></span>}
                            placement={'bottom-start'}
                            classes={{
                                popper: 'tooltipPopper',
                                tooltip: 'tooltipMain tooltipMainLogout',
                                arrow: 'tooltipArrow'
                            }}
                            localStorageKey="logoutBtnTooltip"
                        />
                    
                    <span className='drag dragMain'>
                    {(import.meta.env.VITE_ENVIRONMENT === 'staging' || import.meta.env.VITE_ENVIRONMENT === 'dev') ? <span className='stagingEnv'>Staging &nbsp; v{props.appVersion}</span> : 
                    (import.meta.env.VITE_ENVIRONMENT === 'demo' && <span className='demoEnv'>Demo &nbsp; v{props.appVersion}</span>)}
                    </span>
                    {location.pathname === routes.loginPage && 
                        <Tooltip
                            title={signupTooltip()}
                            arrow
                            placement={'left'}
                            disableInteractive
                            TransitionComponent={Fade}
                            TransitionProps={{ timeout: 600 }}
                            classes={{
                                tooltip: 'signupTooltip',
                            }}
                        >
                            <span className='signUpbtn' onClick={()=>navigatePage(location.pathname, {path:routes.onboardingDetails})}>Sign up</span>
                        </Tooltip>
                    }
                </div>
                <div className='buttoncut'>
                    {userData !== null && location.pathname !== routes.TnCPage &&
                        <>
                         {
                                location.pathname === routes.orderPage ? 
                                <CommonTooltip
                                title={'Click here to see current available orders. When "Orders" is highlighted in a green box, that means there are new orders that you have not seen yet.'}
                                tooltiplabel={<span onClick={() => navigatePage(location.pathname, {path: routes.orderPage})} className='headerTextActive'>Orders</span>}
                                placement={'bottom'}
                                classes={{
                                    popper: 'tooltipPopper',
                                    tooltip: 'tooltipMain tooltipCenter',
                                    arrow: 'tooltipArrow'
                                }}
                                localStorageKey="OrderPageTooltip"
                            /> :
                                    userData.type === userRole.buyerUser ? '' :
                                        userData.type === userRole.neutralUser ? <span className='headerDisabled'>Orders</span> : 

                                        <CommonTooltip
                                        title={'Click here to see current available orders. When "Orders" is highlighted in a green box, that means there are new orders that you have not seen yet.'}
                                        tooltiplabel={<span onClick={() => navigatePage(location.pathname, {path: routes.orderPage})} className={`headerText ${showAlertForNewPo && 'showAlertForNewPo'}`}>Orders</span>}
                                        placement={'bottom'}
                                        classes={{
                                            popper: 'tooltipPopper',
                                            tooltip: 'tooltipMain tooltipCenter',
                                            arrow: 'tooltipArrow'
                                        }}
                                        localStorageKey="OrderPageTooltip"
                                    />
                                            
                            }
                            {
                                location.pathname === routes.createPoPage ? <span onClick={() => navigatePage(location.pathname, {path: routes.createPoPage})} className='headerTextActive'>Create PO</span> :
                                    userData.type === userRole.sellerUser ? '' :
                                        userData.type === userRole.neutralUser ? <span className='headerDisabled'>Create PO</span> :
                                            <span onClick={() => navigatePage(location.pathname, {path: routes.createPoPage})} className='headerText'>Create PO</span>
                            }
                            {
                                location.pathname === routes.disputePage ? <span  onClick={() => navigatePage(location.pathname, {path: routes.disputePage})} className='headerTextActive'>Disputes</span> :
                                userData.type === userRole.neutralUser ? <span className='headerDisabled'>Disputes</span> :
                                <span className='headerDisabled'>Disputes</span>
                                // <span  onClick={() => navigatePage(location.pathname, {path: routes.disputePage})} className='headerText'>Disputes</span>
                            }
                            {
                                <Tooltip
                                    title={shareappTooltip()}
                                    arrow
                                    placement={'top'}
                                    disableInteractive
                                    TransitionComponent={Fade}
                                    TransitionProps={{ timeout: 600 }}
                                    classes={{
                                        tooltip: 'shareappTooltip',
                                    }}
                                >
                                    <button onClick={gotoShareWidgetPage} className={enableShareWidget ? 'activeShareAppIcon' : 'shareAppIcon'}>

                                        <ShareIcon />

                                    </button>
                                </Tooltip>
                            }
                           
                            <CommonTooltip
                                title={'Click here to access General Settings. Here, you can enter personal and company information, attach documents, add funding information, etc.'}
                                tooltiplabel={
                                    
                                    <span onClick={handleGoToBuyerSettingPage}>
                                    {
                                        location.pathname === routes.buyerSettingPage || location.pathname === routes.sellerSettingPage ?
                                            <ActiveSettingsIcon className='minimizeAppIcon' /> :
                                            userData.type === userRole.neutralUser ?
                                                <SettingsIcon className='minimizeAppIconDisabled' /> :
                                                <SettingsIcon className='minimizeAppIcon' />
                                    }
                                </span>
                                
                                }
                                placement={'bottom-end'}
                                classes={{
                                    popper: 'tooltipPopper',
                                    tooltip: 'tooltipMain tooltipRight',
                                    arrow: 'tooltipArrow'
                                }}
                                localStorageKey="settingPageTooltip"
                            />
                        </>
                    }
                    <Tooltip
                        title={pinTooltip()}
                        arrow
                        placement={'top-end'}
                        disableInteractive
                        TransitionComponent={Fade}
                        TransitionProps={{ timeout: 200 }}
                        classes={{
                            popper: 'pinTooltipPopper',
                            tooltip: 'pinTooltip',
                            arrow:'arrowTooltip'
                        }}
                    >
                        <span id='toggle-sticky-btn'>
                            <PinIcon className='minimizeAppIcon pinIcon' />
                            <PinSettingsIcon className='activePIn' />
                        </span>

                    </Tooltip>

                    <CommonTooltip
                                title={'Click here to minimize this window to your task bar'}
                                tooltiplabel={<span onClick={minimizeBtnClick}><MinimizeAppIcon className='minimizeAppIcon' /></span>}
                                placement={'top-end'}
                                classes={{
                                    popper: 'tooltipPopper',
                                    tooltip: 'tooltipMain tooltipRight tooltipRight1',
                                    arrow: 'tooltipArrow'
                                }}
                                localStorageKey="miniMizeTooltip"
                            />

                    
                    <span onClick={closeBtnClick}><CloseIcon /></span>
                </div>
            </div>
        </>
    );
}

export default Header;
