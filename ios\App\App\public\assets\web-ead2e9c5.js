import{bJ as t}from"./vendor-74ddad81.js";class s extends t{constructor(){super(),this.handleVisibilityChange=()=>{const e={isActive:document.hidden!==!0};this.notifyListeners("appStateChange",e),document.hidden?this.notifyListeners("pause",null):this.notifyListeners("resume",null)},document.addEventListener("visibilitychange",this.handleVisibilityChange,!1)}exitApp(){throw this.unimplemented("Not implemented on web.")}async getInfo(){throw this.unimplemented("Not implemented on web.")}async getLaunchUrl(){return{url:""}}async getState(){return{isActive:document.hidden!==!0}}async minimizeApp(){throw this.unimplemented("Not implemented on web.")}}export{s as AppWeb};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
