.searchList {
    display: flex;
    align-items: center;
    padding: 16px 16px 8px 16px;
    .searchInput {
        display: flex;
        gap: 8px;
        flex-grow: 1;

        input {
            width: 100%;
            height: 40px;
            flex-grow: 0;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            border-radius: 100px;
            background-color: rgba(0, 0, 0, 0.2);
            border: 1px solid transparent;
            color: #fff;
            font-family: Noto Sans Display;
            font-size: 14px;
            font-weight: 300;
            text-indent: 22px;
            background-image: url(../../../assets/mobile-images/icons_search_rounded.svg);
            background-repeat: no-repeat;
            background-position: 16px 13px;

            &::placeholder {
                color: #fff;
                opacity: 0.5;
            }

            &:focus {
                outline: none;
                border: solid 1px rgba(255, 255, 255, 0.7);
                background-color: rgba(0, 0, 0, 0.5);
            }
        }
    }
}

.cancelBtn {
    font-family: Noto Sans Display;
    opacity: 0.8;
    font-size: 14px;
    font-weight: 300;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    margin-left: 16px;
}

.searchInstantPrice {
    width: 100%;
    height: 40px;
    border-radius: 4px;
    display: flex;
    align-items: center;

    .lineNoLbl {
        width: 74px;
        height: 40px;
        padding: 10px 10px 10px 12.3px;
        border: solid 1px rgba(255, 255, 255, 0.7);
        background-color: rgba(255, 255, 255, 0.7);
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: 600;
        line-height: 1.4;
        text-align: left;
        color: #000;
        border-radius: 4px 0px 0px 4px;
    }

    .searchInput{
        border: solid 1px rgba(255, 255, 255, 0.7);
        background-color: rgba(0, 0, 0, 0.5);
        height: 40px;
        flex: 1;
        border-radius: 0px 4px 4px 0px;
    }

    input {
        background-color: transparent;
        border: 0px;
        font-family: Noto Sans Display;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: #fff;
        padding: 6px 12px;
        height: 100%;
        width: 100%;

        &:focus {
            outline: none;
        }
    }
}