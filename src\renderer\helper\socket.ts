// @ts-nocheck
import io from 'socket.io-client';
import { snackbarMessageContent } from '../../common';

let socketInstance = null;
const MAX_RETRIES = 100;
const RETRY_INTERVAL = 3000;

const cleanUpEvent = () => {
    socketInstance.off("connect");
    socketInstance.off("disconnect");
    socketInstance.off("connect_error");
}

export const resetConnection = () => socketInstance = null;

export const getSocketConnection = () => socketInstance;

export const createSocket = (url, extraHeaders, socketConnectionErrorHandler, removeSocketDisconnectToaster) => {
  if (!socketInstance) {
    socketInstance = io(url, {
      extraHeaders,
      autoConnect: false,
    });

    let retryCount = 0;

    const connectSocket = () => {
        console.log("Trying to connect to socket ....");
      socketInstance.connect();

      socketInstance.on('connect', () => {
        console.log('Socket connected');
        retryCount = 0;
        removeSocketDisconnectToaster();
      });

      socketInstance.on('disconnect', (reason) => {
        console.log(`Socket disconnected: ${reason}`);
        // console.log(retryCount +" < " + MAX_RETRIES +" = "+(retryCount < MAX_RETRIES));
        // if (reason !== 'io client disconnect') {
        //   // If disconnection was not initiated by client,
        //   // retry connection after some delay
        //     if (retryCount < MAX_RETRIES) {
        //         setTimeout(() => {
        //         console.log(`Retrying connection (${retryCount + 1}/${MAX_RETRIES})...`);
        //         connectSocket();
        //         retryCount++;
        //         }, RETRY_INTERVAL);
        //     }   
        //     else{
                
        //         console.log(`Max retries (${MAX_RETRIES}) exceeded, giving up...`);
        //     }
        // }
      });

      socketInstance.on('connect_error', (error) => {
        console.log(retryCount + " < " + MAX_RETRIES + " = " + (retryCount < MAX_RETRIES));
        if (retryCount < MAX_RETRIES) {
          if (retryCount === 0)
            socketConnectionErrorHandler(snackbarMessageContent.socketRefreshMessage);
          setTimeout(() => {
            console.log(`Retrying connection (${retryCount + 1}/${MAX_RETRIES})...`);
            cleanUpEvent();
            connectSocket();
            retryCount++;
          }, RETRY_INTERVAL);
        } else {
          console.log(`Max retries (${MAX_RETRIES}) exceeded, giving up...`);
        }
        // handle connection error
      });
    };

    connectSocket();
  }

  return socketInstance;
};