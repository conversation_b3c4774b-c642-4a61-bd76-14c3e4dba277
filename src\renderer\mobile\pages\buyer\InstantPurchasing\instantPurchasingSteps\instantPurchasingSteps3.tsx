import { IonItem, IonItemOption, IonItemOptions, IonItemSliding, IonModal, useIonViewWillLeave } from "@ionic/react";
import { useDebouncedValue } from "@mantine/hooks";
import { useEffect, useRef, useState } from "react";
import { useGlobalStore, formatToTwoDecimalPlaces, formatDollarPerUnit, formatCurrencyWithComma, useBuyerCheckOutNode, useSaveUserActivity, ueGetBuyingPreference, checkoutBnplErrorData, mobileRoutes, snackbarSeverityType, purchaseOrder, dateTimeFormat, formatCurrency } from "@bryzos/giss-ui-library";
import { CustomMenu } from "../../../../components/CustomMenu";
import styles from './instantPurchasingSteps.module.scss';
import { ReactComponent as DropdownIcon } from '../../../../../assets/mobile-images/icon_Triangle.svg';
import dayjs from "dayjs";
import SwipeButton from "../../../../components/SwipeButton/ClaimSwipeButton";
import useSnackbarStore from "../../../../../mobile/library/component/Snackbar/snackbarStore";
import { ReactComponent as DeleteLineIcon } from '../../../../../assets/mobile-images/icon_delete_row.svg';
import { ReactComponent as RefreshNet30Icon } from '../../../../../assets/mobile-images/net30refresh.svg';
import DocumentInfo from "../../setting/documentsLibrary";
import { BuyerSetting } from "../../setting/BuyerSettingStore";
import { BNPL_STATUS } from "../../../../library/common";

function display(data: any) {
    const lines = data.split('\n');
    const firstLine = lines[0];
    const restLines = lines.slice(1);
  
    return (
      <div>
        <p className="liHead">{firstLine}</p>
        {restLines.map((line: any, index: any) => (
          <p key={index}>{line}</p>
        ))}
      </div>
    );
}

function InstantPurchasingSteps3(props: any) {
    const [openDocumentInfo, setOpenDocumentInfo] = useState(false);
    const [openPlaceOrderScreen, setOpenPlaceOrderScreen] = useState(false);
    const [debouncedMatTotal] = useDebouncedValue(props.watch('price'), 400);
    const {showToastSnackbar, setSnackbarOpen}: any = useSnackbarStore();
    const {setShowLoader}: any = useGlobalStore();
    const swipeButtonRef = useRef<any>();
    const modal = useRef<HTMLIonModalElement>(null);
    const ionItemSlidingRef = useRef<any>(null);
    const container = useRef<any>(null);
    const saveBuyerCheckout = useBuyerCheckOutNode();
    const logUserActivity = useSaveUserActivity();
    const getBuyingPreference = ueGetBuyingPreference();
    const [bnplStatus, setBnplStatus] = useState(BNPL_STATUS.enabled);
    const maxRestrictedAmount = props?.buyingPreference?.bnpl_settings?.max_restricted_amount ?? "0";
    
    const isNet30Valid = props.watch("payment_method") === purchaseOrder.paymentMethodBNPL ? (!props?.buyingPreference?.bnpl_settings?.is_approved || bnplStatus === BNPL_STATUS.onHold || (bnplStatus === BNPL_STATUS.restricted && Number(props.watch(`totalPurchase`)) > Number(maxRestrictedAmount))) : false;
    
    const MenuPropsTop = {
        classes: {
            paper: styles.selectPaymentDropdownPanel,
        },
        anchorOrigin: {
            vertical: 0,
            horizontal: "left"
        },
        transformOrigin: {
            vertical: "bottom",
            horizontal: "left"
        },
    }

    useIonViewWillLeave(() => {
        setOpenPlaceOrderScreen(false)
        setOpenDocumentInfo(false)
    },[])

    useEffect(() => {
        if(props?.buyingPreference?.bnpl_settings?.bnpl_status){
            setBnplStatus(props?.buyingPreference?.bnpl_settings?.bnpl_status);
        }
    },[props?.buyingPreference?.bnpl_settings?.bnpl_status])

    useEffect(() => {
        if(props.getValues('price')){
            setShowLoader(true);
            props.calculateTotalPricing(true).then(()=>{
                setShowLoader(false);
            });            
        }
        else{
            props.setValue("sales_tax", parseFloat('0'));
            props.setValue("depositAmount", parseFloat('0'));
        }
    }, [debouncedMatTotal])

    useEffect(()=>{
        const materialTotal = +(props.getValues('price') ?? 0);
        let totalPurchaseOrderPrice: string = '0';
        if (materialTotal) {
            totalPurchaseOrderPrice += materialTotal + (+(props.getValues('sales_tax') ?? 0)) + (+(props.getValues('depositAmount') ?? 0))
        }
        props.setValue('totalPurchase',parseFloat(totalPurchaseOrderPrice));
    }, [props.watch('price'), props.watch('sales_tax'), props.watch('depositAmount')])

    useEffect(()=>{
        if(props.watch("payment_method") === purchaseOrder.paymentMethodBNPL){
            let message = "";
            if(bnplStatus === BNPL_STATUS.onHold){
                message = "Your BNPL payment method is on hold because payment for your previous order(s) has not been received. Please choose different payment method. Please contact Bryzos support for more information."
            }
            if(bnplStatus === BNPL_STATUS.restricted){
                message = `Your BNPL payment credit limit is restricted upto ${formatCurrency(maxRestrictedAmount)}. Please contact Bryzos support for more information.`
            }
            if(message){
                showToastSnackbar(message, snackbarSeverityType.alert, null, handleSnackbarClose, "  ", null)
            }
        }
    },[bnplStatus, props.watch("payment_method")])

    const dollerPerUmFormatter = (umVal: any, i: any) => {
        const umUnit = props.getValues(`cart_items.${i}.price_unit`);
        return formatDollarPerUnit(umUnit,umVal,i);
    }

    const onPaymentMethodChange = () => {
        props.saveUserActivity()
        props.paymentMethodChangeHandler();
    }

    const handleSnackbarClose = async () => {
        setSnackbarOpen(false);
      };

    const onSubmit = (data: any) => {
        setOpenPlaceOrderScreen(false)
        const date = new Date()
        if(dayjs(date).format('M/D/YYYY') === dayjs(props.todayDate).format('M/D/YYYY')){
            setShowLoader(true);
            let payloadData = data;
            payloadData.cart_items = payloadData.cart_items.map((item: any) =>{
                const itemCopy = {...item}
                itemCopy.descriptionObj = undefined;
                itemCopy.sessionId = undefined;
                itemCopy.domestic_material_only = itemCopy.domestic_material_only ?? false;
                return itemCopy
            })
            payloadData.recevingHours= undefined;
            payloadData.depositAmount= undefined;
            payloadData.totalPurchase= undefined;
            const localDateTime = dayjs().format(dateTimeFormat.isoDateTimeWithTFormat);
            payloadData.checkout_local_timestamp= localDateTime
            const payload = {
                "data": payloadData
            };
            saveBuyerCheckout.mutateAsync(payload)
                .then(res => {
                    if (res.data.data.error_message) {
                        setShowLoader(false);
                        let errorMessageHeader;
                        let errorMessageContent = res.data.data.error_message;
                        if(res.data.data.mobile_error_message === checkoutBnplErrorData.creditLimitExceed.tag){
                            errorMessageHeader = checkoutBnplErrorData.creditLimitExceed.title;
                            errorMessageContent = checkoutBnplErrorData.creditLimitExceed.body;
                        }
                        showToastSnackbar(errorMessageContent, snackbarSeverityType.alert, null, null, errorMessageHeader, 5000);
                        props.saveUserActivity(null, res.data.data.error_message)
                        props.setDisableCloseAnalytic(true)
                        setTimeout(()=>{
                            const divElement = document.getElementById('routeToPayment');
                            divElement?.addEventListener('click', ()=>{
                                handleSnackbarClose()
                                props.router.push(mobileRoutes.payment,{animate:true})
                            });
                        },0)
                    } else {
                        setShowLoader(false);
                        const payload = {
                            "data": {
                                "session_id": props.sessionId,
                                "close_status": "ACCEPT"
                            }
                        }
                        logUserActivity.mutateAsync({url:import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close' ,payload})
                        .catch(err => console.error(err))
                        props.setOrderConfirmationData({ poNumber: res.data.data, jobNumber: data.internal_po_number, sendInvoicesTo: props.sendInvoicesToEmailData, selectedOptionPayment: data.payment_method });
                        props.saveUserActivity(res.data.data, null);
                        props.setDisableCloseAnalytic(false)
                        props.reset()
                        props.setHideHeader(true)
                        props.setSelectedInstantPricingStep(4)
                        props.setCreatePoData(null);
                    }
                })
                .catch(err => {
                    setShowLoader(false);
                    props.saveUserActivity(null, (err?.message ?? err));
                    showToastSnackbar("An error has occurred. Please wait a moment before trying again.", snackbarSeverityType.alert, null, handleSnackbarClose, null, 5000);
                    props.setDisableCloseAnalytic(true)
                    console.error(err)
                })
        }else{
            showToastSnackbar('Selected delivery date is incorrect, please select correct delivery date.', snackbarSeverityType.alert, null, handleSnackbarClose, null, 5000)
            props.setTodayDate(new Date());
            props.setSelectedInstantPricingStep(1);
        }

        
    }

    const removeLineItem = (index: number) => {
        ionItemSlidingRef.current?.closeOpened();

        props.removeLineItem(index);
    }

    const openDocumentLibrary = () =>{
        setOpenDocumentInfo(true);
    }

    const checkNet30TermStatus = async () => {
        try{
            setShowLoader(true);
            props.setValue("payment_method", purchaseOrder.paymentMethodBNPL);
            const setting:any = await getBuyingPreference.mutateAsync();
            const buyingPreference:BuyerSetting = setting.data.data;
            props.setBuyingPreference(buyingPreference);
            props.setDefaultPaymentOption(buyingPreference, props.paymentMethods, false);
            setShowLoader(false);
        }catch(err){
            setShowLoader(false);
        }
    }

    return (
        <div className={styles.steps3Content}>
            <div className={styles.content}>
                <div className={styles.addPoLineTable}>
                <table className={styles.tableGrid}>
                    <thead>
                        <tr>
                            <th>LN</th>
                            <th>DESCRIPTION</th>
                            <th>QTY @ $/UM</th>
                            <th>TOTAL</th>
                        </tr>
                    </thead>
                    <tbody>
                        {props.watch("cart_items").map((product: any, i: any) => (
                            <tr key={i}>
                            <IonItemSliding ref={ionItemSlidingRef}>
                                <IonItem className="step3TableRowItem">
                                <td>{i+1}</td>
                                <td>
                                    <span>{display(product.description)}</span>
                                    <span className={styles.tagLbl}>{product.product_tag && "Your Part #: " + product.product_tag}</span>
                                    {product.domestic_material_only && (
                                        <span className={styles.domesticMaterialLbl}>
                                            Domestic (USA) Material Only
                                        </span>
                                    )}

                                </td>
                                <td>
                                    {formatCurrencyWithComma(product.qty)} {product.qty_unit}<br/>${dollerPerUmFormatter(product.price, i)} / {product.price_unit}
                                </td>
                                <td>
                                    $ {formatToTwoDecimalPlaces(product.extended ?? 0)}
                                </td>
                                </IonItem>
                                {props.getValues("cart_items").length > 1 &&
                                    <IonItemOptions className="removeRowBtn">
                                        <IonItemOption color="danger">
                                            <button onClick={() => removeLineItem(i)}>
                                                    <DeleteLineIcon /> 
                                            </button>
                                        </IonItemOption>
                                    </IonItemOptions>}
                                </IonItemSliding>
                            </tr>
                        ))}
                    </tbody>
                </table>
                <table className={styles.totalAmt}>
                        <tr>
                            <td><div><span></span></div></td>
                            <td className={styles.materialTotal}><div><span>Material Total</span></div></td>
                            <td className={styles.materialTotal}><div><span>$</span></div></td>
                            <td className={styles.materialTotal}><div><span>{formatToTwoDecimalPlaces(props.getValues("price"))}</span></div></td>
                        </tr>
                        <tr>
                                <td></td>
                                <td ><span className={styles.uploadCertBtn} onClick={()=>{openDocumentLibrary()}}>Buy Tax Free - Upload Cert</span><span className={styles.saleTax}>Sales Tax</span></td>
                                <td><span className={styles.txtSign}>$</span></td>
                                <td><span className={styles.txtNumber}>{formatToTwoDecimalPlaces(props.watch(`sales_tax`))}</span></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td><span className={styles.saleTax}>Freight Charge</span></td>
                                <td><span className={styles.txtSign}>$</span></td>
                                <td><span className={styles.txtNumber}>{formatToTwoDecimalPlaces('0')}</span></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td><span className={styles.saleTax}>Expedited Delivery Fee</span></td>
                                <td><span className={styles.txtSign}>$</span></td>
                                <td><span className={styles.txtNumber}>{formatToTwoDecimalPlaces('0')}</span></td>
                            </tr>
                            {props.watch("payment_method") === "ach_credit" &&
                                <tr>
                                     <td></td>
                                    <td><span className={styles.saleTax}>Deposit</span></td>
                                    <td><span className={styles.txtSign}>$</span></td>
                                    <td><span className={styles.txtNumber}>{formatToTwoDecimalPlaces(props.watch(`depositAmount`))}</span></td>
                                </tr>
                            }
                             <tr>
                             <td></td>
                                <td><span className={styles.totalPurchase}>Total Purchase</span></td>
                                <td><span className={styles.totalPurchaseSign}>$</span></td>
                                <td><span className={styles.totalPurchaseNumber}>{formatToTwoDecimalPlaces(props.watch(`totalPurchase`))}</span></td>
                            </tr>
                    </table>
                </div>
            </div>
           
            <div className={styles.step3BtmSection}>
                <div>
                    <CustomMenu
                        control={props.control}
                        name={'payment_method'}
                        placeholder={'Choose Method of Payment'}
                        className='selectPaymentMethod'
                        MenuProps={MenuPropsTop}
                        items={props.paymentMethods}
                        IconComponent={DropdownIcon}
                        defaultValue={props.getValues('payment_method')}
                        onChange={onPaymentMethodChange}
                    />
                </div>
                <div className={styles.placeBtnOrderSection}>
                    {   (props.watch("payment_method") === purchaseOrder.paymentMethodBNPL && props?.buyingPreference?.bnpl_settings?.is_approved === null) ?
                        <button onClick={() => {checkNet30TermStatus()}}><RefreshNet30Icon/> Click to check status (Net 30 Terms)</button> :
                        <button onClick={() => {setOpenPlaceOrderScreen(true)}} disabled={!props.isValid || isNet30Valid}>Continue</button>
                        
                    }
                </div>

            </div>
            <div ref={container}>
            <IonModal className={'placeOrderPopup'} ref={modal} isOpen={openPlaceOrderScreen} backdropDismiss={false}>
                <PlaceOrderScreen {...props}  onSubmit={onSubmit} setOpenPlaceOrderScreen={setOpenPlaceOrderScreen} swipeButtonRef={swipeButtonRef} maxWidth={container}/>
            </IonModal>
            </div>



            <IonModal className={'reminderPopup'} ref={modal} isOpen={openDocumentInfo} backdropDismiss={false}>
                <DocumentInfo documentLibInfo={props.resaleCertificateData} setOpenDocumentInfo={setOpenDocumentInfo} setUserDetailForPurchase={props.setUserDetailForPurchase}  />
            </IonModal>

        </div>
    );
}
export default InstantPurchasingSteps3;

export const PlaceOrderScreen = (props: any) =>{
    const stateName = props.referenceData.ref_states.find((stateData:any)=> stateData.id === props.watch('shipping_details.state_id'))?.code;
    return(
        <div className={styles.purchaseOrderPop}>
            <div> 
                <p className={styles.total}> Total: </p>
                <p className={styles.totalAmt1}>$ {formatToTwoDecimalPlaces(props.watch(`totalPurchase`))} </p>
            </div>
            <div className={styles.deliverTO}>
                <p className={styles.total}> Delivering to: </p>
                <p className={styles.total}> {props.watch('shipping_details.city')}, {stateName} </p>
            </div>
            <div className={styles.slideTo}>
                <SwipeButton {...props} ref={props.swipeButtonRef} isValid={props.isValid} onSuccess={props.handleSubmit(props.onSubmit)} topTextMessage="To Place Order" bollTitle="Swipe Ball to Right"/>
            </div>
            <div className={styles.slideBack}>
                <button className={styles.total} onClick={()=>{props.setOpenPlaceOrderScreen(false)}}> BACK </button>
            </div>
        </div>
    )
}