.disputeHeader{
  background-color: rgba(0, 0, 0, 0.9);
  color: #fff;
  font-size: 19px;
  padding: 12px 16px;
}
.acceptOrderContent {
  padding: 12px 16px;
  border-radius: 0px 0px 10px 10px;
  // -webkit-backdrop-filter: blur(60px);
  // backdrop-filter: blur(60px);
  // background-color: rgba(0, 0, 0, 0.75);
  margin: 0px auto;
  max-width: 600px;
  height: 700px;
  width: 100%;
  text-align: center;
  position: relative;
  .acceptOrderHead {
    width: 100%;
    display: flex;
    gap: 12px;
    margin-bottom: 12px;

    button {
      &:disabled {
        color: rgb(255, 255, 255);
        border: solid 0.5px rgb(255, 255, 255);
        background-color: rgba(0, 0, 0, 0.5);
        opacity: 0.2;
        font-weight: 300;
        cursor: not-allowed;

        &:hover {
          border: solid 0.5px rgb(255, 255, 255);
        }
      }
    }

    .btnPreNextPo {
      font-family: Noto Sans;
      font-size: 16px;
      font-weight: 300;
      border-radius: 4px;
      background-color: rgba(0, 0, 0, 0.25);
      color: #fff;
      height: 56px;
      width: 56px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 0;

      &:hover,
      &:focus {
        border: solid 1px #70ff00;
      }
    }

    .acceptOrderBtn {
      font-family: Noto Sans;
      font-size: 20px;
      font-weight: 300;
      line-height: 1.2;
      letter-spacing: 4px;
      color: #fff;
      border-radius: 4px;
      background-color: rgba(0, 0, 0, 0.25);
      width: 432px;
      height: 56px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      border: 0;

      &:hover {
        border: solid 1px #70ff00;
        font-weight: 600;
        background-color: #70ff00;
        color: #000;
      }
      &:focus {
        border: solid 1px #70ff00;
      }
    }

    .orderPreviewBtn {
      border-radius: 4px;
      width: 432px;
      height: 56px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: not-allowed;
      border: 0;
      background-color: rgba(255, 255, 255, 0.75);
      font-family: Noto Sans;
      font-size: 20px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: 4px;
      text-align: center;
      color: #000;
      flex-direction: column;
      position: relative;

      .leftIcon {
        position: absolute;
        left: 8px;
      }

      .rightIcon {
        position: absolute;
        right: 8px;
      }

      .acceptReview {
        font-size: 14px;
        letter-spacing: normal;
      }
    }
  }

  .acceptOrderInformation {
    display: flex;
    width: 100%;
    justify-content: left;
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    white-space: nowrap;

    .acceptOrderInformationCol1 {
      width: 100%;
      text-align: left;

      tr {
        td {
          padding-bottom: 4px;

          &:nth-child(2) {
            padding-left: 8px;
            font-weight: 300;
            white-space: break-spaces;
          }
        }
      }
    }

    .acceptOrderInformationCol2 {
      width: 100%;
      text-align: left;

      tr {
        td {
          padding-bottom: 4px;

          &:nth-child(2) {
            padding-left: 8px;
            font-weight: 300;
            white-space: break-spaces;
          }
        }
      }
    }
  }

  .acceptOrderInformationCol3 {
    width: 100%;
    text-align: left;
    margin-bottom: 8px;

    tr {
      td {
        font-family: Noto Sans;
        font-size: 14px;
        font-weight: 600;
        line-height: 1.4;
        text-align: left;
        color: #fff;

        &:nth-child(2) {
          padding-left: 8px;
          font-weight: 300;
          white-space: break-spaces;
        }
      }
    }
  }

  .addPoLineTable {
    width: 100%;
    border-top: solid 1px rgba(255, 255, 255, 0.6);
    padding-top: 6px;
    position: relative;

    .scrollDownImage {
      position: absolute;
      right: 0;
      top: 255px;

      svg {
        cursor: pointer;
      }
    }

    tbody {
      display:block;
      overflow-y: auto;
      overflow-x: hidden;
      height: 100%;
      max-height: 270px;
      &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
      }
  
      &::-webkit-scrollbar-thumb {
        background:
          #9da2b2;
        border-radius: 50px;
      }
  
      &::-webkit-scrollbar-track {
        background: transparent;
      }

    }

    thead,
    tbody tr {
      display: table;
      width: 100%;
    }

    table {
      width: 100%;
      border-spacing: 1px;
      border-collapse: collapse;

      tr {
        // &::after {
        //   border: 1px solid #fff;
        // }

        th {
          font-family: Noto Sans;
          font-size: 16px;
          font-weight: 600;
          line-height: 1.6;
          text-align: left;
          color: #fff;
          padding: 4px 0px 6px 0px;
          border-top:solid 1px rgba(255, 255, 255, 0.6);
          border-bottom: solid 1px rgba(255, 255, 255, 0.6);

          &:nth-child(1) {
            width: 100%;
          }

          &:nth-child(2) {
            width: 100%;
            min-width: 248px
          }

          &:nth-child(3) {
            width: 100%;
            text-align: right;
            min-width: 88px
          }

          &:nth-child(4) {
            width: 100%;
            text-align: right;
            min-width: 88px
          }

          &:nth-child(5) {
            width: 100%;
            min-width: 104px;
            text-align: right;
          }

          span {
            border-bottom: solid 1px rgba(255, 255, 255, 0.6);
            display: block;
            padding-bottom: 3px;
          }
        }
      }

      tr {
        border-top: solid 1px #fff;

        &:first-child{
          border-top: 0px;
        }

        td {
          font-family: Noto Sans;
          font-size: 18px;
          font-weight: normal;
          line-height: 1.6;
          text-align: left;
          vertical-align: top;
          color: #fff;
          padding: 16px 4px 4px 4px;

          &:nth-child(2) {
            font-family: Noto Sans;
            font-size: 14px;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            font-weight: normal;
          }

          .prodId {
            display: inline-block;
          }

          .stripLoad{
            padding-top: 4px;
            opacity: 0.8;
          }
          .domesticMaterial{
            color: #70ff00;
            padding-top: 4px;
          }

          .poDescription {
            width: 100%;
            height: 120px;
            padding: 8px;
            border-radius: 4px;
            border: solid 0.5px #000;
            background-color: rgba(255, 255, 255, 0.2);
            color: #fff;
            resize: none;
            font-family: Noto Sans;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: normal;
            text-align: left;

            &::placeholder {
              color: #bbb;
            }

            &:focus-within {
              border: solid 1px #70ff00;
              outline: none;
              box-shadow: none;
            }
          }

          .poQty,
          .poPerUm {
            width: 80px;

            input {
              padding: 2px 7px;
              border-radius: 4px;
              border: solid 0.5px #000;
              background-color: rgba(255, 255, 255, 0.2);
              height: 29px;
              width: 100%;
              color: #fff;
              text-align: right;

              &:focus-within {
                border: solid 1px #70ff00;
                outline: none;
                box-shadow: none;
              }
            }

            .selectUom {
              .uomDrodown {
                width: 100%;
                border: 0;
                font-family: Noto Sans;
                font-size: 16px;
                line-height: 1.6;
                text-align: left;
                color: #969696;
                font-weight: normal;

                .MuiSelect-select {
                  padding: 20px 6px 6px 6px;
                }

                svg {
                  transform: unset;
                }

                fieldset {
                  border: 0;
                }
              }
            }
          }

          .poPerUm {
            color: rgba(255, 255, 255, 0.6);
            font-weight: normal;
          }

          &:nth-child(5) {
            color: #fff;
            font-weight: normal;
          }

        }
      }

      tbody {
        tr {
          height: 135px;

          td {
            &:nth-child(1) {
              width: 100%;
            }

            &:nth-child(2) {
              width: 100%;
              min-width: 248px;
            }

            &:nth-child(3) {
              width: 100%;
              min-width: 88px;
              text-align: right;
            }

            &:nth-child(4) {
              width: 100%;
              min-width: 88px;
              text-align: right;
            }

            &:nth-child(5) {
              width: 100%;
              min-width: 104px;
              text-align: right;
              max-width: 104px;
              word-wrap: break-word;
              display: flex;

              .div1 {
                width: 20%;
                text-align: left;
                padding-left: 5px;
              }

              .div2 {
                word-break: break-all;
                width: 90%;
              }
            }
          }
        }
      }
    }
  }

  .totalAmt {
    margin-top: 8px;
    border-top:solid 1px #fff;
    padding-top: 8px;

    .saleTax {
      font-family: Noto Sans;
      font-size: 18px;
      line-height: 1.6;
      text-align: right;
      color: #fff;
    }

    .totalPurchase {
      font-family: Noto Sans;
      font-size: 24px;
      font-weight: 600;
      line-height: 1.6;
      text-align: right;
      color: #fff;
    }

    table {
      width: 70%;
      float: right;

      tr {
        td {
          text-align: right;
        }
      }
    }
  }

  .acceptOrderBottom {
    display: flex;
    position: absolute;
    bottom: 12px;
    right: 13px;
    left: 13px;
    gap: 8px;

    .returnToSearch {
      button {
        padding: 12px;
        border-radius: 4px;
        border: solid 1px rgba(255, 255, 255, 0.05);
        background-color: rgba(255, 255, 255, 0.1);
        white-space: nowrap;
        font-family: Noto Sans;
        font-size: 12px;
        line-height: 1.2;
        text-align: left;
        color: #fff;
        cursor: pointer;
        width: 85px;
        display: flex;
        align-items: center;
        justify-content: center;
        &:focus{
          border: solid 1px #70ff00;
        }
      }
    }

    .textOfCondition {
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: 300;
      line-height: 1.6;
      text-align: left;
      color: #fff;
      opacity: 0.6;
    }
  }
}

.SubmitApp {
  .dialogContent {
    max-width: 461px;
    width: 100%;
    height: 650px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding: 32px 32px 40px 32px;
    object-fit: contain;
    border-radius: 10px;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(0, 0, 0, 0.72);
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    position: relative;
    overflow: hidden;

    li {
      margin-bottom: 15px;
      margin-left: 20px;
    }

    .closeIcon {
      position: absolute;
      top: 10px;
      right: 12px;
      cursor: pointer;

      svg {
        height: 25px;
        width: 25px;
        color: white;

        path {
          fill: #fff
        }
      }
    }

    .disclaimer {
      padding: 16px 24px;
      border-radius: 4px;
      background-color: #d9d9d9;
      font-family: Noto Sans;
      font-size: 20px;
      font-weight: 600;
      line-height: 1.6;
      color: #000;
      text-align: center;
      position: relative;
      margin-bottom: 24px;

      .leftIcon {
        position: absolute;
        left: 24px;
        top: 12px;
      }

      .rightIcon {
        position: absolute;
        right: 24px;
        top: 12px;
      }
    }

    .flx {
      display: flex;
      justify-content: right;
      margin-top: 30px;
      gap: 10px;

      .submitBtn {
        transition: all 0.1s;
        font-family: Noto Sans;
        font-size: 16px;
        line-height: 1.6;
        text-align: center;
        color: #70ff00;
        background-color: transparent;
        border: 0;
        opacity: 0.7;
        margin-left: 20px;
        border: 0.5px solid transparent;
        padding: 0px 5px 0px 5px;

        &:disabled {
          cursor: not-allowed;
          color: #fff !important;
          opacity: 0.5 !important;
        }

        &:hover {
          color: #70ff00;
          opacity: unset;
        }
        &:focus {
          opacity: unset;
          border: 0.5px solid #70ff00;
        }
      }

      .cancelBtn {
        opacity: 0.7;
        font-family: Noto Sans;
        font-size: 16px;
        line-height: 1.6;
        text-align: center;
        color: #fff;
        transition: all 0.1s;
        background-color: transparent;
        border: 0.5px solid transparent;
        padding: 0px 5px 0px 5px;

        &:hover {
          opacity: unset;
        }
        &:focus-within {
          border: 0.5px solid #70ff00;
        }
      }
    }
  }
}

.successPopup {
  .dialogContent {
    height: 479px;

    .successPopupTitle {
      font-family: Noto Sans;
      font-size: 18px;
      font-weight: normal;
      line-height: 1.6;
      text-align: center;
      color: #70ff00;
    }
  }

}

.ErrorDialog {
  .dialogContent {
    max-width: 333px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 30px 34px 30px 34px;
    object-fit: contain;
    border-radius: 10px;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(0, 0, 0, 0.72);
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: center;
    color: #fff;

    .closeIcon {
      position: absolute;
      top: 10px;
      right: 12px;
      cursor: pointer;
      opacity: 0.5;

      &:hover {
        opacity: unset;
      }

      svg {
        height: 20px;
        width: 20px;
        color: white;

        path {
          fill: #fff
        }
      }
    }

    // p {
    //   margin-bottom: 20px;
    // }
    .youJustMissetext {
      height: 32px;
      align-self: stretch;
      flex-grow: 0;
      font-family: Noto Sans;
      font-size: 20px;
      font-weight: 600;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: 0.5px;
      text-align: center;
      color: #fff;
      margin-bottom: 18px;
    }

    .thisOrderMissedtest {
      height: 60px;
      align-self: stretch;
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: center;
      color: #fff;

    }

    .missedtest {
      height: 60px;
      align-self: stretch;
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: left;
      color: #fff;
    }

    .claimAnotherOrderbtn {
      height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background-color: transparent;
      font-family: Noto Sans;
      font-size: 16px;
      font-weight: normal;
      line-height: 1.6;
      text-align: center;
      color: #fff;
      margin-top: 20px;
      transition: all 0.1s;
      border: none;

      &:hover {
        color: #70ff00;
      }

    }

    .submitBtn {
      height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 10px 24px;
      border-radius: 4px;
      border: solid 0.5px #fff;
      background-color: transparent;
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: center;
      color: #fff;
      margin-top: 20px;
      transition: all 0.1s;

      &:hover {
        background-color: #70ff00;
        border: solid 0.5px #70ff00;
        color: #000;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          border: solid 0.5px #fff;
          background-color: transparent;
          color: #fff;
        }
      }
    }


  }

}
.nextprevdisabledBtn{
  color: rgb(255, 255, 255);
  border: solid 0.5px rgb(255, 255, 255);
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0.2;
  font-weight: 300;
  cursor: not-allowed;

  &:hover,
  &:focus {
    border: solid 0.5px rgb(255, 255, 255);
  }
}