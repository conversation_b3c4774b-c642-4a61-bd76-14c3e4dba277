import { useQuery } from '@tanstack/react-query';
import { reactQueryKeys } from '../common';
import axios from 'axios';
import { Device } from '@capacitor/device';
import { getDeviceId } from '../helper';

const useGetCustomNotification = (userData: any) => {
    return useQuery(
        [reactQueryKeys.getCustomNotification],
        async () => {
            const deviceId = await getDeviceId();
            const { data } = await axios.get(import.meta.env.VITE_API_NOTIFICATION_SERVICE + '/notification/getCustomNotification', { params: { device_id: deviceId } });
            return data.data;
        },
        {
            enabled: !!userData,
            retry: false,
            refetchOnWindowFocus: false,
            refetchOnMount: false,
        }
    );
};

export default useGetCustomNotification;
