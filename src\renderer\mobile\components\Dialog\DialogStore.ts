import { create } from 'zustand';

export interface DialogAction{
    name: string,
    action: any
}

const dialogStoreInit = {
    openDialog: false,
    header: null,
    actions: [],
    content: null
}

const useDialogStore = create((set) => ({
    ...dialogStoreInit,
    setOpenDialog: (isOpen: boolean) => set({ openDialog: isOpen }),
    setDialogHeader: (msg: string) => set({ header: msg }),
    setDialogActions: (actions: DialogAction) => set({ snackbarActions: actions }),
    showCommonDialog: (msg: string, actions:DialogAction[], header: string|null = null) => set({openDialog: true,content: msg, actions: actions, header }),
    resetDialogStore: () => set((state:any) => ({
        ...dialogStoreInit
    }))
}));

export default useDialogStore;