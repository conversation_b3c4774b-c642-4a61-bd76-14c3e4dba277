// @ts-nocheck
import React, { useEffect, useRef, useState } from 'react'
import { Amplify, Auth } from 'aws-amplify';
import { Redirect, Route } from 'react-router-dom';
import { LocalNotifications } from '@capacitor/local-notifications';
import rg4js from 'raygun4js';
import useCognitoUser from './library/hooks/useCognitoUser';
import useOurLogin from './library/hooks/useOurLogin';
import OnboardingMobileDetails from './pages/onboarding/OnboardingDetails';
import OnboardingTnc from './pages/onboarding/OnboardingTnC';
import InstantPurchasing from './pages/buyer/InstantPurchasing/instantPurchasing';
import Search from './pages/search/Search';
import Setting from './pages/buyer/setting/Setting';
import Profile from './pages/buyer/setting/profile';
import CompanyInfo from './pages/buyer/setting/companyInfo';
import DeliveryDetail from './pages/buyer/setting/deliveryTo';
import ReceivingHours from './pages/buyer/setting/receivingHours';
import DocumentInfo from './pages/buyer/setting/documentsLibrary';
import MyReports from './pages/buyer/setting/myReports';
import Payment from './pages/buyer/setting/payment';
import Header from './pages/header';
import { IonContent, IonHeader, IonRouterOutlet, useIonRouter, BackButtonEvent, setupIonicReact } from '@ionic/react';
import { createPusherChannel } from './common/pusher';
import useGetUnreadNotifications from './library/hooks/useGetUnreadNotifications';
import { notificationSchedule } from './common/notificationHandler';
import styles from './App.module.scss'
import { VERSION_NUMBER, getDeviceId, getProductMapping, markNotificationAsRead, redirectLandingPage, updatedAllProductsData } from './library/helper';
import Login from './pages/Login';
import Success from './pages/Success';
import SellerInfoSetting from './pages/seller/setting/Setting';
import SellerProfile from './pages/seller/setting/profile/Profile';
import SellerCompanyInfo from './pages/seller/setting/companyInfo/CompanyInfo';
import StockingLocation from './pages/seller/setting/stockingLocation';
import FundingSetting from './pages/seller/setting/fundingSetting';
import SellerDocumentInfo from './pages/seller/setting/documentsLibrary/DocumentInfo';
import YourProducts from './pages/seller/setting/yourProducts';
import SellerMyReports from './pages/seller/setting/myReports/MyReports';
import ShareApp from './components/ShareApp';
import Dialog from './components/Dialog/Dialog';
import axios from 'axios';
import { customNotification, mobileDiaglogConst, mobileRoutes, mobileSnackbarMessageContent, roleBasedRoutes, snackbarSeverityType, userRole, splashScreenConst, android15SdkVersion } from './library/common';
import OrderListing from './pages/seller/orders/orderListing';
import YourOrderListing from './pages/seller/orderHistory/yourOrderListing';
import YourOrderPreview from './pages/seller/orderHistory/orderPreview';
import AcceptOrder from './pages/seller/orders/acceptOrder';
import OnboardingThankYou from './pages/onboarding/OnboardingThankYou';
import {App as NativeApp} from '@capacitor/app';
import { Capacitor } from '@capacitor/core';
import ToastSnackbar from './library/component/Snackbar/ToastSnackbar';
import { SafeArea } from 'capacitor-plugin-safe-area';
import useDialogStore from './components/Dialog/DialogStore';
import useCreateNewSocketConnection from './library/hooks/useCreateNewSocketConnection';
import packageData from "../../../package.json";
import Loader from './components/Loader/Loader';
import useSnackbarStore from './library/component/Snackbar/snackbarStore';
import NoInternet from './components/NoInternet/NoInternet';
import { Network } from '@capacitor/network';
import TnCPage from './pages/TnC';
import { ReactComponent as CloseIcon } from '../assets/mobile-images/Icon_Close.svg';
import dayjs from 'dayjs';

if (Capacitor.isNativePlatform()) {
  rg4js("apiKey", import.meta.env.VITE_RAYGUN_KEY);
  rg4js("enableCrashReporting", true);
  rg4js('setVersion', VERSION_NUMBER);
  rg4js('ignore', 'web_request_timing');
  rg4js('options', {
    ignore3rdPartyErrors: true
  });
}
import ErrorBoundary from './components/Error/ErrorBoundary';
import ErrorPage from './components/Error/ErrorPage';
import useGetCustomNotification from './library/hooks/useGetCustomNotification';
import { Chat } from '../component/chat/Chat';
import { useSellerOrderStore, socketDisconnect, useGlobalStore, addAxiosInterceptor, initializeAxiosResponseInterceptor, generateHashFromEncryptedData, useGetSecurityData, excludeSecurityHashApiList, includeInitialPageList, setEnvironment, reconnectToSocket, commomKeys, commonAppEventsofPusher, stegnography, migratePasswordConst , useAuthStore, compareVersions} from '@bryzos/giss-ui-library';
import { liveUpdate } from './CapgoUpdate';
import { CapacitorUpdater } from '@capgo/capacitor-updater';
import VideoLibrary from './pages/VideoLibrary/VideoLibrary';
import ChangePassword from './pages/auth/changePassword/changePassword';
import ForgotPassword from './pages/auth/forgotPassword/forgotPassword';
import AuthenticationWrapper from './components/AuthenticationWrapper/AuthenticationWrapper';
import {  useSplashScreenStore } from './library/stores/splashScreenStore';
import { Device } from '@capacitor/device';
import { AndroidSdkPlugin } from './AndroidSdkPlugin';

Amplify.configure({
  Auth: {
    region: import.meta.env.VITE_AWS_COGNITO_REGION,
    userPoolId: import.meta.env.VITE_AWS_COGNITO_USER_POOL_ID,
    userPoolWebClientId: import.meta.env
      .VITE_AWS_COGNITO_USER_POOL_WEB_CLIENT_ID,
    cookieStorage: {
      domain: 'localhost',
      path: '/',
      expires: 365,
      secure: false,
    }
  },
  Analytics: { disabled: Boolean(import.meta.env.VITE_AWS_COGNITO_ANALYTICS_DISABLED) },
});

setupIonicReact({ hardwareBackButton: false });

let isDiscountApiInprocess = false; 

const App = () => {
  const { setDecryptionEntity, setReferenceProductVersion, userData, setUserData, setSellerSettingsData,  setShowLoader, setForceLogout, setDeepLinkNavigate, deepLinkNavigate, pusherEvent, setPusherEvent, setReferenceProductChanged, referenceProductChanged, setNavigationStateForNotification, setPusherActionEvent, isUserLoggedIn, setRefreshTheApp, pusherActionEvent, hasGlobalErrorOccured, backdropOverlay, setBackdropOverlay, discountData, setDiscountData, sdk, showErrorPopup, setShowErrorPopup, hasLoginProcessCompleted, setSecurityHash, setSignupUser, isAppStateChangeActive, setIsAppStateChangeActive,  setProductData,setProductMapping,setReferenceData,currentUser, setDeviceId} = useGlobalStore();
  const {  socketDisconnectAfterMaxRetries } = useSellerOrderStore();
  const ourLogin = useOurLogin()
  const {data: getNotifications, isLoading: isNotificationsLoading, isFetching: isNotificationsFetching, refetch: refetchNotification } = useGetUnreadNotifications(userData.data?.email_id)
  const { data: customNotifications, isLoading: isCustomNotificationsLoading, isFetching: isCustomNotificationsFetching , refetch: refetchCustomNotifications } = useGetCustomNotification(userData.data)
  const router = useIonRouter()
  const { routeInfo } = router;
  const [appInsets, setAppInsets] = useState({ top: 0, bottom: 0 })
  const { showCommonDialog, resetDialogStore } = useDialogStore();
  const [resetStoreOnLogout, setResetStoreOnLogout] = useState(false);
  const { showToastSnackbar, resetSnackbarStore }: any = useSnackbarStore();
  const [noInternet, setNoInternet] = useState(false);
  const [disableInitialSecurityApiCall, setDisableInitialSecurityApiCall] = useState(false);
  const [lastCapgoUpdateCheck, setLastCapgoUpdateCheck] = useState(dayjs());
  const {initiateLogin} = useAuthStore();
  const {setShowSplashScreen, setCapgoUpdateDisplayText, setUpdatesFound} = useSplashScreenStore()
  const deviceId = useRef<string | null>(null);
  const [isAndroid15Plus, setIsAndroid15Plus] = useState(false);

  const {
    data: cognitoUser,
  } = useCognitoUser(isUserLoggedIn);
  useCreateNewSocketConnection(cognitoUser);
  const {
    refetch: refetchGetSecurity
  } = useGetSecurityData(cognitoUser);

  useEffect(() => {
    if (userData?.data && discountData) {
      const store = useGlobalStore.getState();
      store.userData.data.disc_is_discounted = discountData.isDiscounted;
      store.userData.data.disc_discount_rate = discountData.discountRate;
      store.userData.data.disc_discount_pricing_column = discountData.discountPricingColumn;
      setProductMapping(getProductMapping(store.productData, store.userData.data))
      setUserData({...store.userData});
    }
  }, [discountData]);

  useEffect(() => {
    (async function(){
      if (!isNotificationsLoading && !isNotificationsFetching && !isCustomNotificationsLoading && !isCustomNotificationsFetching) {
        const notificationList = getNotifications?.notificationList ?? [];
        await showLocalNotification({ notificationList: notificationList, customNotificationList: customNotifications ?? [] });
        localStorage.setItem("isUserLoggedIn", true);
      };
    })();
  }, [getNotifications, customNotifications, isNotificationsLoading, isNotificationsFetching, isCustomNotificationsLoading, isCustomNotificationsFetching])

  useEffect(() => {
    if (referenceProductChanged) {
      checkReferenceProductVersion();
      setReferenceProductChanged(false);
    }
  }, [referenceProductChanged])

  async function getAllProductsData(hideSnackbar) {
    try {
      const response = await axios.get(import.meta.env.VITE_API_SERVICE + '/reference-data/getAllProducts');
      const products = updatedAllProductsData(response.data.data);
      const store = useGlobalStore.getState();
      setProductData(products)
      setProductMapping(getProductMapping(products, store.userData.data))
      setUserData({ ...store.userData });
      if(!hideSnackbar){
        redirectLandingPage(store.userData.data.type);
        showToastSnackbar(mobileSnackbarMessageContent.productReferenceDataChanged, snackbarSeverityType.warning, null, resetSnackbarStore);
      }
    } catch (err) {
      setShowLoader(false);
    }
  }

  const showLocalNotification = async (notificationSchema) => {
    let {notificationList, customNotificationList} = notificationSchema;
    
    notificationList = notificationList.map(x => x.notification)
    customNotificationList = customNotificationList.map(x => x.notification);

    const filteredNotificationList = notificationList.filter((notificationSchema) => notificationSchema.event !== commonAppEventsofPusher.privateEvents.userUiUploadLog )
    const filteredCustomNotificationList = customNotificationList.filter((notificationSchema) => notificationSchema.event !== commonAppEventsofPusher.privateEvents.userUiUploadLog  )

    if (filteredNotificationList?.length ) {
        const notificationListToBeShown  = filteredNotificationList.slice(filteredNotificationList.length - 10);
        const isUserLoggedIn = localStorage.getItem("isUserLoggedIn");
        for (const notification of notificationListToBeShown) {
          if (notification.event === commonAppEventsofPusher.mobileEvents.capgoUpdate) {
            showCapgoUpdateSnackBar(notification);
          }
          if (!isUserLoggedIn) {
            notificationSchedule(notification.title, notification.body, notification);
          }
        }
      }
        try{
          const arr = [...notificationList, ...customNotificationList];
          if (arr?.length) {
            await markNotificationAsRead(arr);
          }
        }
        catch(err){
          console.error(err);
        }
      if (filteredCustomNotificationList?.length) {
        const lastCustomNotification = filteredCustomNotificationList[filteredCustomNotificationList.length - 1];
        showCustomNotification(lastCustomNotification);
      }
      return true;
  }

  async function getReferenceData() {
    setShowLoader(true);
    try {
      const response = await axios.get(import.meta.env.VITE_API_SERVICE + '/reference-data')
      const referenceData = response.data;
      const store = useGlobalStore.getState();
      setReferenceData(referenceData)
      setUserData({ ...store.userData });      
    }
    catch (e) {
      setShowLoader(false);
    }
  }

  const configureAxiosInterceptor = async (noAccessTokenRequire) => {
    const res = await refetchGetSecurity();
    const securityHash = await generateHashFromEncryptedData(res.data, import.meta.env.VITE_SECRET_KEY);
    setSecurityHash(securityHash);
    setDisableInitialSecurityApiCall(true);
    axios.interceptors.request.clear();
    addAxiosInterceptor(() => setForceLogout(true),(request) => {
      request.headers.security = securityHash;
    }, noAccessTokenRequire);
  }

  const createDeadSimpleChatUser = async () => {
    try {
      const response = (await axios.post(import.meta.env.VITE_API_SERVICE + '/user/createUser', null)).data?.data;
      return { unique_user_identifier: response };
    } catch (error) {
      return null;
    }
  }

  useEffect(()=>{
    if(hasGlobalErrorOccured){
      router.push(mobileRoutes.error,{animate:true});
    }
  },[hasGlobalErrorOccured])

  useEffect(()=>{
    if(socketDisconnectAfterMaxRetries && !noInternet){
      reconnectToSocket();
    }
  },[socketDisconnectAfterMaxRetries, noInternet])

  useEffect(()=>{
    (async ()=> {if (isAppStateChangeActive) {
      if(hasLoginProcessCompleted){
        await configureAxiosInterceptor()
        checkReferenceProductVersion();
        refetchNotification();
        refetchCustomNotifications();
        if (!isDiscountApiInprocess) {
          isDiscountApiInprocess = true;
          checkDiscountPricing(true);
        }
      } else {
        const currentTime = dayjs();
        const elapsed = currentTime.diff(lastCapgoUpdateCheck, 'hour', true);
        if (elapsed >= 1) {
          setCapgoUpdateDisplayText(splashScreenConst.starting)
          updateUsingCapacitor();
          setLastCapgoUpdateCheck(currentTime);
        }
      }
    }})()
  },[isAppStateChangeActive])

  useEffect(()=>{
    const handleAppStateChange = (event) => {
      setIsAppStateChangeActive(event.isActive);
    }
    NativeApp.addListener('appStateChange', handleAppStateChange);
    return () => {
      NativeApp.removeAllListeners('appStateChange');
    }
  },[])

  useEffect(()=>{
    if(showErrorPopup){
      showCommonDialog(commomKeys.errorContent,[{name: mobileDiaglogConst.ok, action: handleCloseErrorPopup}]);
      setShowErrorPopup(false);
    }
  },[showErrorPopup])

  const handleCloseErrorPopup = () => {
    configureAxiosInterceptor(true)
    resetDialogStore(); 
  }

  const updateUsingCapacitor = async () => {
    setUpdatesFound(true);
    try{
      const store: any = useGlobalStore.getState();
      if(!store.secuirtyHash){
        await configureAxiosInterceptor(true);
      }
      await liveUpdate()
    }catch(error){
      console.log("error ", error);
    }
  }

  const initData = async () => {
    const steg_fun = stegnography(document);
    steg_fun.decode("/asset/space_5.png", (_data) => {
      setDecryptionEntity(JSON.parse(_data));
    })
  }

  async function getSdkVersion() {
    console.log("getSdkVersion called" , Capacitor.getPlatform())
    if (Capacitor.getPlatform() === 'android') {
      try {
        const isPluginAvailable = await Capacitor.isPluginAvailable("AndroidSdkPlugin");
        if(isPluginAvailable){
          const result = await AndroidSdkPlugin.getTargetSdkVersion();
          return result.targetSdkVersion;
        }else{
          return 0;
        }
      } catch (error) {
        console.error('Error getting target SDK version:', error);
        return 0;
      }
    }else{
      return 0;
    }
  }


  const initializeApp = async () =>{
    await updateUsingCapacitor()
    await initData();
    deviceId.current = await getDeviceId();
    setDeviceId(deviceId.current)
    const currentSdkVersion = await getSdkVersion();
    if (Capacitor.getPlatform() === 'android' && currentSdkVersion >= android15SdkVersion) {
      setIsAndroid15Plus(true);
    } else{
      setIsAndroid15Plus(false);
    } 
  }

  const compareMobileAppVersion = (version1, version2) => {
    const version1Split = version1.split('-');
    const version2Split = version2.split('-');
    const versionCompare = compareVersions(version1Split[0],version2Split[0]);
    if(!(version1Split[1] && version2Split[1])){
      return versionCompare >= 0 
    }
    if(versionCompare === 0){
      const envVersion1 = version1Split[1].substring(1);
      const envVersion2 = version2Split[1].substring(1);
      return Number(envVersion1) >= Number(envVersion2)
    }
    
    return versionCompare >= 0
  };

  const handleSplashScreen = async () => {
    const platform = (await Device.getInfo()).platform;
    const capgoVersion = packageData.version
    const appInfo = (platform === 'web') ? {version:capgoVersion} : await NativeApp.getInfo();
    const localCapgoVersion = localStorage.getItem("localCapgoVersion");
    if((compareMobileAppVersion(appInfo.version, import.meta.env.VITE_SPLASHSCREEN_ANDROID_VERSION) && platform === "android")  || (compareMobileAppVersion(appInfo.version,import.meta.env.VITE_SPLASHSCREEN_IOS_VERSION) && platform !== "android")){
      if(!localCapgoVersion){
            setShowSplashScreen(true)
      }else{
        if(localCapgoVersion === capgoVersion){
              setShowSplashScreen(true)
        }else{
          setShowLoader(true)
          setShowSplashScreen(false)
        }
      }
    } else if (compareMobileAppVersion(capgoVersion, import.meta.env.VITE_SPLASHSCREEN_CAPGO_VERSION)) {
      if (localCapgoVersion && localCapgoVersion === capgoVersion) {
        setShowSplashScreen(true)
      } else {
        setShowLoader(true)
        setShowSplashScreen(false)
      }
    }
    localStorage.setItem("localCapgoVersion", capgoVersion)
  }

  useEffect(() => {
    setEnvironment(import.meta.env);
    setCapgoUpdateDisplayText(splashScreenConst.starting)
    handleSplashScreen()
    initializeApp()
  },[]);

  useEffect(() => {
    LocalNotifications.checkPermissions().then(x => {
      if (x.display !== 'granted' && x.display !== 'denied') {
        LocalNotifications.requestPermissions()
      }
    })

    const listner = LocalNotifications.addListener('localNotificationActionPerformed', (actionPerformedData) => {
      const state = useGlobalStore.getState();
      const notificationData = actionPerformedData.notification.extra;
      if (notificationData) {
        setPusherActionEvent(notificationData);
      }
      if (notificationData.navigation) {
        if (notificationData.navigation.state) setNavigationStateForNotification(notificationData.navigation.state);
        if (notificationData.navigation.mobileRoutePath && roleBasedRoutes[state.userData.data.type].has("/" + notificationData.navigation.mobileRoutePath)) router.push("/" + notificationData.navigation.mobileRoutePath, { animate: true })
      }
    })

    const setupWithInsets = async function () {
      const insets = await SafeArea.getSafeAreaInsets()
      setAppInsets({ top: insets.insets.top, bottom: insets.insets.bottom })
    }
    setupWithInsets();

    const quitApp = () => {
      resetDialogStore();
      handleRefreshApp();
      const state = useGlobalStore.getState();
      if(location.pathname !== mobileRoutes.TnCPage && location.pathname !== mobileRoutes.changePassword){
        if( state?.userData?.data?.type === userRole.buyerUser){
          router.push("/instantpurchasing");
        }else if( state?.userData?.data?.type === userRole.sellerUser){
          router.push("/order-listing");
        }
      }
      setTimeout(()=>{
        NativeApp.minimizeApp();
      },300)
    } 
    
    const handleBackButton = (ev: BackButtonEvent) => {
      ev.detail.register(10, (event) => {
        showCommonDialog("Quit App?", [{ name: mobileDiaglogConst.yes, action: quitApp }, { name: mobileDiaglogConst.no, action: resetDialogStore }]);
      });
    }

    if (Capacitor.isNativePlatform()) document.addEventListener('ionBackButton', handleBackButton);

    NativeApp.addListener('appUrlOpen', (event) => {
      const url = new URL(event.url);
      const path = url.pathname;
      setDeepLinkNavigate({ 'routePath': path });
    });

    setCurrentNetworkStatus();

    Network.addListener('networkStatusChange', (status: ConnectionStatus) => {
      setNoInternet(!status.connected);
    });

    if(!disableInitialSecurityApiCall){
      configureAxiosInterceptor(true);
    }
    initializeAxiosResponseInterceptor(setForceLogout, async(response)=>{
      await handleAxiosResponseData(response);
    });
    return () => {
      listner.remove();
      socketDisconnect();
      document.removeEventListener('ionBackButton', handleBackButton);
      Network.removeAllListeners();
    }
  }, [])

  const handleAxiosResponseData = async(response) => {
    const urlSplit = response.config.url.split("/");
      const pathName = urlSplit[urlSplit.length - 1];
      if((!excludeSecurityHashApiList.find((excludeSecurityHashApi) => pathName.startsWith(excludeSecurityHashApi)) && response?.data?.data === 'Success' && response.status === 200)){
        if (currentUser) initiateLogin(currentUser.email_id, false, response.config.method)
      }
  }

  useEffect(() => {
      if(pusherEvent){
        switch (pusherEvent.event) {
          case commonAppEventsofPusher.privateEvents.userForceLogout:
            setForceLogout(true);
            break;
          case commonAppEventsofPusher.privateEvents.userDiscountPriceChange:
            // showCommonDialog(mobileDiaglogConst.discountPricingChangesMsg,[{name: mobileDiaglogConst.refresh, action: handleRefreshApp }]);
            break;
          case commonAppEventsofPusher.publicEvents.referenceProductChanges:
          case commonAppEventsofPusher.publicEvents.referencePriceChanges:
          case commonAppEventsofPusher.publicEvents.referencePriceAndProductChanges:
            setReferenceProductChanged(true);
            break;
          case commonAppEventsofPusher.publicEvents.customNotification:
            showCustomNotification(pusherEvent);
            break;
          case commonAppEventsofPusher.mobileEvents.capgoUpdate:
            showCapgoUpdateSnackBar(pusherEvent);
            break;
          default:
            break;
        }
        setPusherEvent(null);
      }
  }, [pusherEvent])

  useEffect(()=>{
    if(pusherActionEvent){
      switch (pusherActionEvent.event) {
        case commonAppEventsofPusher.privateEvents.userDiscountPriceChange:
          if(!isDiscountApiInprocess){
            isDiscountApiInprocess= true;
            checkDiscountPricing(false);
          }
          break;
        case commonAppEventsofPusher.mobileEvents.capgoUpdate:
          showCapgoUpdateSnackBar(pusherActionEvent);
          break;
        default:
          break;
      }
      setPusherActionEvent(null);
    }
  },[pusherActionEvent])


  const handleRefreshApp = () => {
    resetDialogStore();
    resetSnackbarStore()
    setBackdropOverlay(false);
    setRefreshTheApp(true)
    if (userData.data){
      initiateLogin(userData.data.email_id, true)
    }
  }

  useEffect(() => {
    if (!userData && sdk) {
      sdk.logout();
    }
    
    if (userData.data && deepLinkNavigate && isUserLoggedIn) {
      if (roleBasedRoutes[userData.data.type].has(deepLinkNavigate.routePath)) {
        router.push(deepLinkNavigate.routePath, { animate: true });
        setNavigationStateForNotification(deepLinkNavigate.state ?? null);
        setDeepLinkNavigate(null);
      } 

    }
  }, [userData, deepLinkNavigate, isUserLoggedIn])

  const getInitialData = async (data, devicePlatform, isRefresh, axiosMethod) => {
    try{
      setShowLoader(true);
      setUserData({ "data": data })      
      await getReferenceData();
      const currentTandC = data.current_tnc_version;
      const acceptedTandC = data.accepted_terms_and_condition;
      const initialPage = location.pathname.replace(/[/]/g, '');
      
      if (currentTandC !== acceptedTandC || currentTandC === null || acceptedTandC === null) {
        router.push(mobileRoutes.TnCPage, { animate: true })
      } 
      else if(data.is_migrated_to_password === 1){
        handleMigratePasswordPopup();
      }
      else if(includeInitialPageList.find((includeInitialPage) => initialPage.startsWith(includeInitialPage)) || initialPage.length === 0 || isRefresh) {
        const isMigratePasswordPopupShown = localStorage.getItem("isMigratePasswordPopupShown")
        let isRedirectedToChangePassword  = false;
        if(data.is_migrated_to_password === 0 ){
          if(isMigratePasswordPopupShown){
            router.push(mobileRoutes.changePassword, { animate: true },undefined,{isLogin:true});
            isRedirectedToChangePassword = true
          }else{
            showCommonDialog(migratePasswordConst.dialogContent,[{name: mobileDiaglogConst.ok, action: handleMigratePasswordPopup}], migratePasswordConst.dialogTitle);
            localStorage.setItem("isMigratePasswordPopupShown",true)
          } 
        }
        if(!isRedirectedToChangePassword){
          router.push(redirectLandingPage(data.type), { animate: true });
        }
      } 
      else if(axiosMethod === 'post'){
        showCommonDialog(commomKeys.errorContent,[{name: mobileDiaglogConst.ok, action: resetDialogStore}]);
      }
      if (data.type === userRole.sellerUser) {
        isSellerSettingAvailable();
      }
      await getAllProductsData(true);
      if (!isRefresh && devicePlatform !== 'web') {
        createPusherChannel(data, data.pusher_id, data.email_id, cognitoUser.signInUserSession.accessToken.jwtToken)
      }
      await checkReferenceProductVersion();
      setShowLoader(false);
    }
    catch(err){
      setShowLoader(false);
      setSignupUser(null);
    }
  }
  const setCurrentNetworkStatus = async () => {
    const status = await Network.getStatus();
    setNoInternet(!status.connected);
  };

  const handleMigratePasswordPopup = () => {
    router.push(mobileRoutes.changePassword, { animate: true },undefined,{isLogin:true});
  }

  const isSellerSettingAvailable = () => {
    axios.get(import.meta.env.VITE_API_SERVICE + "/user/sellingPreference")
      .then((response) => {
        if (response.data && response.data.data) {
          if (
            typeof response.data.data === "object" &&
            "err_message" in response.data.data
          ) {
            setSellerSettingsData(null);
          } else {
            const sellerSettingData = response.data.data;
            if (sellerSettingData?.company_name &&
              sellerSettingData?.company_address_line1 &&
              sellerSettingData?.company_address_city &&
              sellerSettingData?.company_address_state_id &&
              sellerSettingData?.company_address_zip &&
              sellerSettingData?.first_name &&
              sellerSettingData?.last_name &&
              sellerSettingData?.email_id &&
              sellerSettingData?.phone) {
              setSellerSettingsData(true);
            } else {
              setSellerSettingsData(null)
            }
          }
        } else {
          setSellerSettingsData(null);
        }
      })
      .catch(() => {
        setShowLoader(false);
        setSellerSettingsData(null);
      });
}

const checkReferenceProductVersion = async()=>{
  const refProductDetail = await axios.get(import.meta.env.VITE_API_SERVICE + "/reference-data/getReferenceDataVersions");
  let versionName = refProductDetail.data.data.filter((x)=> x.status === 'active')[0].version_name;
  let store = useGlobalStore.getState();
  if(store.referenceProductVersion && versionName !== store.referenceProductVersion){
    getAllProductsData();
  }
  setReferenceProductVersion(versionName);
}

  const checkDiscountPricing = async (showDialog) => {
    try {
      const userDiscountData = await axios.get(import.meta.env.VITE_API_SERVICE + "/user/discountData");
      const userDiscount = userDiscountData.data.data;
      const store = useGlobalStore.getState();
      const user = store.userData.data;
      const newUserDiscountData = { ...user };
      newUserDiscountData.disc_is_discounted = userDiscount.is_discount;
      newUserDiscountData.disc_discount_rate = userDiscount.discount_rate;
      newUserDiscountData.disc_discount_pricing_column = userDiscount.discount_pricing_column;
      const newUpdateUserData = { ...store.userData, data: newUserDiscountData };

      setUserData(newUpdateUserData)

      if (!(user.disc_is_discounted === userDiscount.is_discount &&  user.disc_discount_rate === userDiscount.discount_rate && user.disc_discount_pricing_column === userDiscount.discount_pricing_column)) {
        const discountObj = {
          "isDiscounted": userDiscount.is_discount,
          "discountRate": userDiscount.discount_rate,
          "discountPricingColumn": userDiscount.discount_pricing_column
        }
        setDiscountData(discountObj)
        if (showDialog) {
          // showCommonDialog(mobileDiaglogConst.discountPricingChangesMsg, [{ name: mobileDiaglogConst.refresh, action: handleRefreshApp }]);

        } else if (!discountData) {
          handleRefreshApp()
        }
      }
      isDiscountApiInprocess = false;

    } catch (error) {
      isDiscountApiInprocess = false;
      console.log(error)
    }

  }


  const showCustomNotification = (notification: any) => {
    if (notification) {
      let action = notification.action;
      const title = notification.title;
      const message = notification.body;
      const severity = notification.priority;
      let showOverlay = false;
      let buttons;
      if (action === customNotification.action.refresh) {
        buttons = [{ name: customNotification.action.refresh, handler: handleRefreshApp }];
        showOverlay = true;
      } else if (action === customNotification.action.close) {
        buttons = [{ name: "", handler: handleSnackbarClose, icon: <CloseIcon /> }];
      }
      showOverlayToaster(message, severity, buttons, null, showOverlay)
    }
  }

  const showCapgoUpdateSnackBar = async (notification: any) => {
    try{
      if (notification) {
        const latest = await CapacitorUpdater.getLatest();
        if (latest.version === packageData.version) {
          return;
        } else {
          const message = notification.body;
          const severity = customNotification.priorty.high;
          showOverlayToaster(message, severity, [{ name: commomKeys.updateText, handler: handleSnackbarCloseForCapgoUpdate }], null, true)
        }
      }
    }catch(error) {
      console.log("error ", error);
    }
  }

  const handleSnackbarCloseForCapgoUpdate = () => {
    resetSnackbarStore();
    setBackdropOverlay(false);
    setCapgoUpdateDisplayText(splashScreenConst.checkingForUpdates)
    updateUsingCapacitor()
  }

  const handleSnackbarClose = (event, reason) => {
    resetSnackbarStore();
    setBackdropOverlay(false);
  };

  const showOverlayToaster = (message, severity, buttons, closeHandler, showOverlay) => {
    showToastSnackbar(message, severity, buttons, closeHandler);
    if (showOverlay){
      setBackdropOverlay(true);
    }
  }

  return (
    <div className={styles.appBgMain} style={{
      paddingTop: (routeInfo.pathname === '/login') ? '' : appInsets.top,
      paddingBottom: (routeInfo.pathname === '/instantpurchasing' && !isAndroid15Plus) ? '' : appInsets.bottom,
    }}>
      <ErrorBoundary>
        <div style={{position:'absolute',top:(appInsets.top + 10),width:'100%'}}>
          <ToastSnackbar />
        </div>
        {backdropOverlay &&
          <>
            <div className='backdropOverlay'></div>
          </>
        }
        <Loader />
        <NoInternet open={noInternet} />
        <AuthenticationWrapper noInternet={noInternet} cognitoUser={cognitoUser} resetStoreOnLogout={resetStoreOnLogout} setResetStoreOnLogout={setResetStoreOnLogout} getInitialData={getInitialData} configureAxiosInterceptor={configureAxiosInterceptor} deviceId={deviceId}>
          {(userData.data &&
            routeInfo.pathname !== mobileRoutes.loginPage &&
            routeInfo.pathname !== mobileRoutes.onboardingDetails &&
            routeInfo.pathname !== mobileRoutes.onboardingTnc &&
            routeInfo.pathname !== mobileRoutes.onboardingThankYou &&
            routeInfo.pathname !== mobileRoutes.TnCPage &&
            routeInfo.pathname !== mobileRoutes.error &&
            routeInfo.pathname !== mobileRoutes.forgotPassword
          ) &&
            <IonHeader>
              <Header />
            </IonHeader>
          }
          <IonContent className="ion-padding">
            <IonRouterOutlet animated='false'>
              <Route exact={true} path="/login" render={() => <Login />} />
              <Route path="/tnc" render={() => <TnCPage />} />
              <Route exact={true} path="/onboarding-detail" component={OnboardingMobileDetails} />
              <Route exact={true} path="/onboarding-tnc" component={OnboardingTnc} />
              <Route exact={true} path="/onboarding-thank-you" component={OnboardingThankYou} />
              <Route exact={true} path="/chat" component={Chat} />
              <Route path="/instantpurchasing" component={InstantPurchasing} />
              <Route path="/search" component={Search} />
              <Route path="/success" component={Success} />
              <Route path="/setting" render={() => <Setting />} />
              <Route path="/profile" component={Profile} />
              <Route path="/company-information" component={CompanyInfo} />
              <Route path="/delivery-detail" component={DeliveryDetail} />
              <Route path="/receiving-hours" component={ReceivingHours} />
              <Route path="/document-info" component={DocumentInfo} />
              <Route path="/my-reports" component={MyReports} />
              <Route path="/payment" component={Payment} />
              <Route path="/seller-setting" render={() => <SellerInfoSetting />} />
              <Route path="/seller-profile" component={SellerProfile} />
              <Route path="/seller-company-information" component={SellerCompanyInfo} />
              <Route path="/stocking-location" component={StockingLocation} />
              <Route path="/funding-setting" component={FundingSetting} />
              <Route path="/seller-document-info" component={SellerDocumentInfo} />
              <Route path="/your-products" component={YourProducts} />
              <Route path="/seller-my-reports" component={SellerMyReports} />
              <Route path="/shareapp" component={ShareApp} />
              <Route path="/order-listing" component={OrderListing} />
              <Route path="/accept-order" component={AcceptOrder} />
              <Route path="/your-order-listing" component={YourOrderListing} />
              <Route path="/your-order-preview" component={YourOrderPreview} />
              <Route path="/error" render={() => <ErrorPage getInitialData={getInitialData} />} />
              <Route path="/video-library" component={VideoLibrary} />
              <Route path={mobileRoutes.changePassword} component={ChangePassword} />
              <Route path={mobileRoutes.forgotPassword} component={ForgotPassword} />
              {/* <Redirect exact from="/" to="/login"/> */}
            </IonRouterOutlet>
          </IonContent>
        </AuthenticationWrapper>
      </ErrorBoundary>

      <Dialog />

    </div>
  )
}

export default App