import { useElementSize } from '@mantine/hooks';
import { useEffect } from 'react'
import { channelWindowList } from '../../common';
const heightHeader = 42 // 44;
export const useHeightListener = (isHeaderHeightNotRequired?: null) => {
    const { ref, height } = useElementSize();
    useEffect(() => {
        // 44 Height of header
        // Todo: buyer setting height is wrongly calculated
        const newHeight = isHeaderHeightNotRequired ? ref?.current.offsetHeight : ref?.current.offsetHeight  + heightHeader;
        // window.electron.send({ channel: channelUpdateWindowHeight, data:newHeight })
        // const newHeight= height + heightHeader < minHeight ? minHeight :  height + heightHeader
        (window as any).electron.send({ channel: channelWindowList.updateHeight, data:newHeight })
    }, [height]);
    return ref
}
