import { IonContent, IonPage } from "@ionic/react";
import { mobileRoutes } from "../../library/common";
import { useGlobalStore } from "@bryzos/giss-ui-library";
import { Device } from "@capacitor/device";
import { ReactComponent as FatalErrorIcon } from '../../../assets/mobile-images/fatalError.svg';


const ErrorPage = (props: any) => {
    const { userData, setGlobalErrorOccured, setShowLoader}: any = useGlobalStore();

    const btnRefresh = async () => {
        const deviceInfo = await Device.getInfo();
        setGlobalErrorOccured(false);
        await props.getInitialData(userData.data, deviceInfo.platform, true)
        setShowLoader(false)
    }

    return (
        <IonPage>
            <IonContent>
                <div className="fatalErrorMain">
                    <div className={`errorPanel bgImg ${location.pathname === mobileRoutes.loginPage ? 'errorPanelMargin' : ''}`}>
                        <div className='errorPage'>
                            <div><FatalErrorIcon /></div>
                            <div className="fatalErrorTitle">Something went wrong !!!</div>
                            <p>Please restart the application.</p>
                            <p>If same issue still occurs then please contact Bryzos Support.</p>
                            <button className="restartAppBtn" onClick={btnRefresh}>Restart App</button>
                        </div>
                    </div>
                </div>
            </IonContent>
        </IonPage>
    )

}
export default ErrorPage;