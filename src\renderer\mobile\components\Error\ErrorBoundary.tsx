import React, { ErrorInfo, ReactNode } from "react";
import { dispatchRaygunError, unhandledError } from "../../library/helper";
import { useGlobalStore } from "@bryzos/giss-ui-library";

type Props = {
  children?: ReactNode;
}

type State = {
  hasError: boolean;
  errorMessage: Error|undefined;
}

class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      errorMessage: undefined,
    };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, errorMessage: error  };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    if (error) {
      this.setState({ hasError: true, errorMessage: error });
    }
  }

  render() {
    if (this.state.hasError) {
      const state: any = useGlobalStore.getState();
      state.setGlobalErrorOccured(true);
      if(!state.hasGlobalErrorOccured){
        dispatchRaygunError(this.state.errorMessage, unhandledError);
      }
      this.setState({ hasError: false });
    }
    return this.props.children;
  }
}

export default ErrorBoundary;
