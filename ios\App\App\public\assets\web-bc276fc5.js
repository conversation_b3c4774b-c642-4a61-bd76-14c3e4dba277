import{bK as s}from"./vendor-96deee04.js";class c extends s{constructor(){super(...arguments),this.pending=[],this.deliveredNotifications=[],this.hasNotificationSupport=()=>{if(!("Notification"in window)||!Notification.requestPermission)return!1;if(Notification.permission!=="granted")try{new Notification("")}catch(i){if(i.name=="TypeError")return!1}return!0}}async getDeliveredNotifications(){const i=[];for(const t of this.deliveredNotifications){const e={title:t.title,id:parseInt(t.tag),body:t.body};i.push(e)}return{notifications:i}}async removeDeliveredNotifications(i){for(const t of i.notifications){const e=this.deliveredNotifications.find(n=>n.tag===String(t.id));e==null||e.close(),this.deliveredNotifications=this.deliveredNotifications.filter(()=>!e)}}async removeAllDeliveredNotifications(){for(const i of this.deliveredNotifications)i.close();this.deliveredNotifications=[]}async createChannel(){throw this.unimplemented("Not implemented on web.")}async deleteChannel(){throw this.unimplemented("Not implemented on web.")}async listChannels(){throw this.unimplemented("Not implemented on web.")}async schedule(i){if(!this.hasNotificationSupport())throw this.unavailable("Notifications not supported in this browser.");for(const t of i.notifications)this.sendNotification(t);return{notifications:i.notifications.map(t=>({id:t.id}))}}async getPending(){return{notifications:this.pending}}async registerActionTypes(){throw this.unimplemented("Not implemented on web.")}async cancel(i){this.pending=this.pending.filter(t=>!i.notifications.find(e=>e.id===t.id))}async areEnabled(){const{display:i}=await this.checkPermissions();return{value:i==="granted"}}async changeExactNotificationSetting(){throw this.unimplemented("Not implemented on web.")}async checkExactNotificationSetting(){throw this.unimplemented("Not implemented on web.")}async requestPermissions(){if(!this.hasNotificationSupport())throw this.unavailable("Notifications not supported in this browser.");return{display:this.transformNotificationPermission(await Notification.requestPermission())}}async checkPermissions(){if(!this.hasNotificationSupport())throw this.unavailable("Notifications not supported in this browser.");return{display:this.transformNotificationPermission(Notification.permission)}}transformNotificationPermission(i){switch(i){case"granted":return"granted";case"denied":return"denied";default:return"prompt"}}sendPending(){var i;const t=[],e=new Date().getTime();for(const n of this.pending)!((i=n.schedule)===null||i===void 0)&&i.at&&n.schedule.at.getTime()<=e&&(this.buildNotification(n),t.push(n));this.pending=this.pending.filter(n=>!t.find(o=>o===n))}sendNotification(i){var t;if(!((t=i.schedule)===null||t===void 0)&&t.at){const e=i.schedule.at.getTime()-new Date().getTime();this.pending.push(i),setTimeout(()=>{this.sendPending()},e);return}this.buildNotification(i)}buildNotification(i){const t=new Notification(i.title,{body:i.body,tag:String(i.id)});return t.addEventListener("click",this.onClick.bind(this,i),!1),t.addEventListener("show",this.onShow.bind(this,i),!1),t.addEventListener("close",()=>{this.deliveredNotifications=this.deliveredNotifications.filter(()=>!this)},!1),this.deliveredNotifications.push(t),t}onClick(i){const t={actionId:"tap",notification:i};this.notifyListeners("localNotificationActionPerformed",t)}onShow(i){this.notifyListeners("localNotificationReceived",i)}}export{c as LocalNotificationsWeb};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
