// @ts-nocheck
import Header from './header';
import { reactQueryKeys, routes } from '../../common';
import { useEventListener } from '@mantine/hooks';
import { Auth } from 'aws-amplify';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from "yup";
import { useQueryClient } from '@tanstack/react-query';
import { useHeightListener } from '../hooks/useHeightListener';
import { useEffect, useState } from 'react';
import { OTPInputComponent } from '../component/OTPInput';
import { useStore } from '../helper/store';
import { useNavigate } from 'react-router';

const Login = () => {
    const navigate = useNavigate();
    const setShowLoader = useStore(state => state.setShowLoader);

    const { register, watch, handleSubmit, setError, setValue, clearErrors, trigger, formState: { errors, isValid, submitCount } } = useForm({
        resolver: yupResolver(
            yup.object({
                email: yup.string().matches(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/, {
                    message: 'Please enter valid email'
                }).required('Email is Required'),
                password: yup.array()
                    .of(yup.number().typeError('').required('req').integer('integer')).test('len', 'Password must be 6 digits', val => val?.filter(v => !isNaN(v)).length === 6),
            }).required()

        ),
        mode: 'onSubmit',
    });

    const [showPasswordInput, setShowPasswordInput] = useState(false);
    const resetOrderStore = useStore(state => state.resetOrderStore);
    const resetCommonStore = useStore(state => state.resetCommonStore);

    const handleFocus = () => {
        setShowPasswordInput(true);
    };

    const handleEmailFocus = () => {
        setShowPasswordInput(false);
    };

    useEffect(()=>{
        resetOrderStore();
        resetCommonStore();
    },[])

    useEffect(() => {
        if (errors.root?.serverError)
            clearErrors('root.serverError')
    }, [watch('email'), watch('password')])



    const query = useQueryClient()
    const ref = useHeightListener(true);

    async function submit(data) {
        setShowLoader(true);
        const password = data.password.join('')

        try {
            const user = await Auth.signIn(data.email, password);
            if (user.challengeName === "NEW_PASSWORD_REQUIRED") {
                const completeUser = await Auth.completeNewPassword(user, password);
            }
            // clickOnEnterSession(data.email)
            query.invalidateQueries([reactQueryKeys.cognitoUser]);
        } catch (error) {
            console.log('error signing in', error.name, error);
            if (error.name === 'NotAuthorizedException')
                setError('root.serverError', { message: 'Wrong username or password' })
            setShowLoader(false);
        }
    }

    const keydownRef = useEventListener('keydown', function (event) {
        if (event.key === 'Enter') {
            handleSubmit(submit)()
        }
    }
    );


    return (
        <div ref={ref}>
        <table ref={keydownRef}>
            <tbody>
                <tr className='inputBody'>
                    <td className='enterEmail'>
                        <input type="email" autoFocus onFocus={handleEmailFocus} {...register("email")}
                            placeholder='Enter Email' />
                        <p className='errorText'>{errors.email?.message}</p>
                        {(isValid && !errors.root?.serverError?.message) ?
                            <button className={`pressBtn`} onClick={handleSubmit(submit)} type="button">Press ENTER to start session</button>
                            :
                            <p className='errorText'>{errors.root?.serverError?.message}</p>
                        }
                    </td>
                    <td className='enterEmail'>
                        <div>
                            {!watch('password') && !showPasswordInput ? (
                                <input
                                    id="inputPlaceholder"
                                    placeholder='Enter Password'
                                    onFocus={handleFocus}
                                    readOnly
                                />
                            ) : (
                                <OTPInputComponent register={register}
                                registerName= {'password'}
                                inputType= {'password'}
                                    className='passwordBox'
                                    autoFocus
                                />
                            )}
                        </div>
                        {/* <p className='errorText'>{errors.password?.message}</p> */}
                        {/* <p className='forgotPassText' onClick={() => navigate(routes.forgotPassword)} >Forgot Password</p> */}
                    </td>
                </tr>
            </tbody>
        </table>
        </div>
    );
};

export default Login;