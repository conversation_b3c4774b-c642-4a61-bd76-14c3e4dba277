// @ts-nocheck
import { IonContent, IonPage, useIonRouter, useIonViewWillEnter } from '@ionic/react';
import { Dialog } from '@mui/material';
import { useEffect, useRef, useState } from 'react';
import { migratePasswordConst, mobileDiaglogConst, useGlobalStore, useAuthStore } from '@bryzos/giss-ui-library';
import { mobileRoutes, fileType } from '../library/common';
import axios from 'axios';
import { downloadFiles, redirectLandingPage } from '../library/helper';
import useDialogStore from '../components/Dialog/DialogStore';

function TnCPage(props: any) {
    const [tandCData, setTandCData] = useState({});
    const [tnc, setTnc] = useState('');
    const { userData, setShowLoader , referenceData , hasLoginProcessCompleted } = useGlobalStore();
    const router = useIonRouter();
    const tncContainerRef = useRef();
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const {setTriggerLogout, initiateLogout} = useAuthStore()

    useEffect(()=>{
        if(hasLoginProcessCompleted){
            setShowLoader(false);
        }
    },[hasLoginProcessCompleted])
   
    const handleSubmitTnc = () => {
        const payload = {
            data: {
                "bryzos_terms_condtion_id": tandCData.id,
                "terms_conditions_version": tandCData.terms_conditions_version,
                "email_id": userData.data.email_id
            }
        };
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/saveTermsCondition', payload).then(response => {
            if (response.data !== null) {
                const isMigratePasswordPopupShown = localStorage.getItem("isMigratePasswordPopupShown")
                let isRedirectedToChangePassword  = false;
                if(userData?.data?.is_migrated_to_password === 0){
                    if(isMigratePasswordPopupShown){
                        router.push(mobileRoutes.changePassword, { animate: true },undefined,{isLogin:true});
                        isRedirectedToChangePassword = true
                      }else{
                        showCommonDialog(migratePasswordConst.dialogContent,[{name: mobileDiaglogConst.ok, action: handleMigratePasswordPopup}], migratePasswordConst.dialogTitle);
                        localStorage.setItem("isMigratePasswordPopupShown",true)
                      } 
                }
                if(userData?.data?.is_migrated_to_password === 1) handleMigratePasswordPopup()
                else{
                    if(!isRedirectedToChangePassword){
                        router.push(redirectLandingPage(userData.data.type),{animate:true});
                    } 
                }
            }
        })
            .catch(error => { console.error(error) });
    }

    const handleMigratePasswordPopup = () => {
        router.push(mobileRoutes.changePassword, { animate: true },undefined,{isLogin:true});
    }

    useEffect(() => {
        if(Object.keys(userData).length && Object.keys(referenceData).length ){
            referenceData?.ref_bryzos_terms_conditions?.forEach(termsAndCondition => {
                if (userData?.data?.type === termsAndCondition?.type) {
                    setTandCData(termsAndCondition)
                }
            });   
        }
    }, [userData, referenceData]);

    const FetchHtml = async () => {
        const response = await fetch(tandCData.cloudfront_url+"?ver="+new Date().getTime());
        return await response.text(); // Returns it as Promise
    };
    const SetHtml = async () => {
        await FetchHtml().then((text) => {
            setTnc(text);
        });
    };
    useEffect(() => {
        if (Object.keys(tandCData).length !== 0) {
            SetHtml(true)
        }
    }, [tandCData]);

    useIonViewWillEnter(()=>{
        scrollToTopOfTncPage();
    })

    const scrollToTopOfTncPage = () => {
        tncContainerRef.current.scrollTop = 0;
    }

    // const downloadReports = (fileUrl, fileName, fileType) => {
    //     setShowLoader(true);
    //     const showError = downloadFiles(fileUrl, fileName, fileType)
    //     showError.then(res => {
    //         setShowLoader(false);
    //         if (res) {
    //             setOpenErrorDialog(false);
    //         } else {
    //             setOpenErrorDialog(true);
    //         }
    //     })
    //     .catch(e => {
    //         setShowLoader(false);
    //     });

    // }

    return (
        <>
            <IonPage>
                <IonContent>
                    <div className='tncBgMob'>
                        <div className='termsAndConditionsMain'>
                            <div className='TncContainer'>
                                <div className='termsAndConditions' ref={tncContainerRef}>
                                    <div>
                                        <div className='tncHead'>
                                            <div>Bryzos Instant Pricing Desktop Widget Terms of Use</div>
                                        </div>
                                        <p className='effectiveDate'>Effective as of: March 8, 2023</p>
                                        <div dangerouslySetInnerHTML={{ __html: tnc }}></div>

                                    </div>
                                </div>
                            </div>

                        </div>
                        <div className='tncButtons'>
                            {/* {isViewMode ?
                        <>
                            <button onClick={()=>navigate(backNavigation)} className='disagreeBtn'>Back</button>
                            <div onClick={() => { downloadReports(import.meta.env.VITE_FILE_URL_DOWNLOAD_TNC_PDF, 'Tnc', fileType.pdf) }} className='downloadTnCBtn'> Click here to download T&C </div>
                        </>
                        : */}
                            <>
                                <button onClick={() => initiateLogout()} className='disagreeBtn'>Disagree</button>
                                {/* <div className='downloadTnCBtn' onClick={() => { downloadReports(import.meta.env.VITE_FILE_URL_DOWNLOAD_TNC_PDF, 'Tnc', fileType.pdf)  }}> Click here to download T&C </div> */}
                                <button className='agreeBtn1' onClick={handleSubmitTnc}>Agree</button>
                            </>
                            {/* } */}
                        </div>
                    </div>
                </IonContent>
            </IonPage>




        </>
    );
}
export default TnCPage;
