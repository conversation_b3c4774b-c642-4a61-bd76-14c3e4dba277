// @ts-nocheck
import { Auth } from 'aws-amplify';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from "yup";
import { useEffect, useState } from 'react';
import { changePasswordConst, commomKeys, decryptData, encryptData, mobileDiaglogConst, mobileRoutes, useGlobalSignoutApi, useGlobalStore, usePostMigrateToPassword, userRole } from '@bryzos/giss-ui-library';
import styles from './changePassword.module.scss';
import { getDeviceId, redirectLandingPage } from '../../../library/helper';
import useDialogStore from '../../../components/Dialog/DialogStore';
import { IonContent, IonPage, useIonRouter } from '@ionic/react';
import { Directory, Filesystem } from '@capacitor/filesystem';
import { ReactComponent as ErrorEmailIcon } from '../../../../assets/mobile-images/errorEmail.svg';
import { ReactComponent as ShowPassIcon } from '../../../../assets/mobile-images/show-pass.svg';
import { ReactComponent as HidePassIcon } from '../../../../assets/mobile-images/hide-pass.svg';
import { ReactComponent as BackBtnArrowIcon } from '../../../../assets/mobile-images/back_left_arrow.svg';
import { ReactComponent as  CrossLogo} from '../../../../assets/mobile-images/crossBtn.svg';


import { clsx } from 'clsx';
import { Fade, Tooltip } from '@mui/material';

const ChangePassword = () => {
    const router = useIonRouter();
    const { decryptionEntity, userData, setAutoLogin, setGlobalForceLogout, setShowLoader, isManualLogin } = useGlobalStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const changePasswordConfirmation = usePostMigrateToPassword();
    const [showOldPassword, setShowOldPassword] = useState(true);
    const routeData = router.routeInfo.routeOptions;
    const [passwordVisibility, setPasswordVisibility] = useState({
        password1: true,
        password2: true,
        password3: true,
    });
    const globalSignout = useGlobalSignoutApi();
    const togglePasswordVisibility = (field) => {
        setPasswordVisibility((prevState) => ({
            ...prevState,
            [field]: !prevState[field],
        }));
    };
    const { register, handleSubmit, setValue, setError, clearErrors, getValues, formState: { isDirty, errors, isValid } } = useForm({
        resolver: yupResolver(
            yup.object({
                currentPassword: yup.string().test('len', 'Password must be 6 digits', val => val?.length >= 6),
                newPassword: yup.string().required('Password is required').min(6,'Password must be 6 digits'),
                confirmPassword: yup.string().test("isRequired", "Password does not match!", function (value) {
                    const password = this.parent.newPassword;
                    if (password.trim() === value.trim()) return true;
                    return false;
                })
            }).required()

        ),
        mode: 'onBlur',
    });
    const isModuleOpenedOnLogin = routeData?.isLogin;
    const [isEdited,setIsEdited] = useState(false)

    useEffect(()=>{
        (async ()=>{
            setShowLoader(false);
            if(decryptionEntity){
                try{
                    const cred = await Filesystem.readFile({
                        path: 'user-data.txt',
                        directory: Directory.Cache,
                    });
                    if(cred.data && isModuleOpenedOnLogin && isManualLogin){
                        const data = JSON.parse(await decryptData(cred.data.toString(), decryptionEntity.decryption_key, true));
                        setValue('currentPassword', data.password);
                        setShowOldPassword(false);
                    }
                }
                catch(e){
                    setShowOldPassword(true);
                }
            }
        })()
    }, [])

    async function submit({ newPassword, currentPassword }) {
        try {
            if (userData) {
                setShowLoader(true);
                const user = await Auth.currentAuthenticatedUser();
                await Auth.changePassword(user, currentPassword, newPassword);
                const email = userData.data?.email_id
                if(isModuleOpenedOnLogin){
                    const _encryptCredential = await encryptData(JSON.stringify({ email, password: newPassword }), decryptionEntity.decryption_key);
                    await Filesystem.writeFile({
                        path: 'user-data.txt',
                        data: _encryptCredential,
                        directory: Directory.Cache
                    });
                    changePasswordConfirmation.mutateAsync(email).then(async () => {
                        onChangePassword(email);
                    })
                }
                else
                onChangePassword(email);
            }
        } catch (error) {
            console.error('Error changing password:', error);
            setShowLoader(false);
            if (error.message === changePasswordConst.changePasswordCognitoError) error.message = changePasswordConst.incorrectOldPasswordError;
            showCommonDialog(error.message ?? changePasswordConst.onError, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
        }
    }

    const onChangePassword = async (email_id) => {
        try{
            const device_id = await getDeviceId() ?? 'invalid_id';
            await globalSignout.mutateAsync({data:{email_id,device_id}});
        }catch(err){
            console.error(err)
        }
        finally{
            setShowLoader(false);
            showCommonDialog(changePasswordConst.onSuccess, [{ name: commomKeys.continue, action: resetDialog }]);
        }
    }

    const resetDialog = () => {
        resetDialogStore();
        if(isModuleOpenedOnLogin) setAutoLogin(true);
        else setGlobalForceLogout(mobileRoutes.loginPage);
    }

    const routeBackToSetting = () => {
        if (userData.data.type === userRole.buyerUser)
            router.push('/setting', { animate: true, direction: 'forward' });
        else
            router.push('/seller-setting', { animate: true, direction: 'forward' });
        resetDialogStore();
    }

    const backToSetting = () => {
        if(isDirty)
        showCommonDialog(mobileDiaglogConst.unsavedChangesMsg, [{name: mobileDiaglogConst.stay, action: resetDialogStore},{name: mobileDiaglogConst.goBackToSetting, action: routeBackToSetting}]);
        else
        routeBackToSetting();
    }

    const handleForgotPassword = () => {
        showCommonDialog(changePasswordConst.forgotPasswordPrompt, [{ name: commomKeys.yes, action: naviagateToForgotPassword },{ name: commomKeys.no, action: resetDialogStore }]);
    }

    const naviagateToForgotPassword = () => {
        resetDialogStore();
        setGlobalForceLogout(mobileRoutes.forgotPassword);
    }

    const handlePasswordBlur = () => {
        const password = getValues('newPassword')?.trim();
        const confirmPassword = getValues('confirmPassword')?.trim();
        if(password.length && confirmPassword?.length){
            if (password === confirmPassword) {
                clearErrors(["newPassword", "confirmPassword"]);
            } else {
                setError("newPassword", { message: "Password does not match!" });
            }
        }
    }

    return (
        <IonPage>
            <IonContent>
            <h2 className={styles.heading}>{!isModuleOpenedOnLogin && <BackBtnArrowIcon onClick={backToSetting}/>}<span> {!isModuleOpenedOnLogin ?  'Change your password' : 'Set Your New Password'}</span></h2>
            <div className={clsx(styles.resetPasscontainer, (!isModuleOpenedOnLogin && styles.changePassPopup))}>
                {isModuleOpenedOnLogin &&
                    <p className={styles.noteText}>
                        {userData?.data?.is_migrated_to_password === 0 ?
                            "As part of our recent system improvements, we’re asking you to set a new password for enhanced security. Please take a moment to update your credentials below."
                            :
                            "We need you to reset your password since it was recently reset by an admin. Please take a moment to update your credentials below for continued access."
                        }
                    </p>
                }
                <div className={styles.changePassInnerContent}>
                    {showOldPassword && <div className={styles.currentPassword}>
                        <div className={clsx(styles.FormInputGroup)}>
                            <span className={styles.lblInput}>Current Password</span>
                            <Tooltip
                                title={errors.currentPassword?.message}
                                arrow
                                placement={"top"}
                                disableInteractive
                                TransitionComponent={Fade}
                                TransitionProps={{ timeout: 100 }}
                                classes={{
                                    tooltip: "inputQtyTooltip",
                                }}
                            >
                                <span className={clsx(styles.inputSection, (errors?.currentPassword) && styles.errorEmail)}>
                                    <input type={passwordVisibility.password1 ? 'password' : 'text'} {...register("currentPassword")} placeholder='Enter your current password'
                                        onChange={(e) => {
                                            e.target.value = e.target.value.trim();
                                            register('currentPassword').onChange(e);
                                        }}
                                    />
                                    <button className={styles.showPassBtn} onClick={() => togglePasswordVisibility('password1')}>
                                        {passwordVisibility.password1 ? <ShowPassIcon /> : <HidePassIcon />}
                                    </button>
                                </span>
                            </Tooltip>
                        </div>
                        <span className={styles.forgotPasswordTxt} onClick={handleForgotPassword}>Forgot Password?</span>
                    </div>}

                    <div className={styles.passwordErrorContainer}>
                        <div className={clsx(styles.FormInputGroup)}>
                            <span className={styles.lblInput}>New Password</span>
                            <Tooltip
                                title={errors?.newPassword?.message || errors?.confirmPassword?.message}
                                arrow
                                placement={"top"}
                                disableInteractive
                                TransitionComponent={Fade}
                                TransitionProps={{ timeout: 200 }}
                                classes={{
                                    tooltip: "inputQtyTooltip",
                                }}
                            >
                                <span className={clsx(styles.inputSection, (errors?.newPassword || errors?.confirmPassword) && styles.errorEmail)}>
                                    <input type={passwordVisibility.password2 ? 'password' : 'text'} {...register("newPassword")} placeholder='Enter your new password'
                                        onChange={(e) => {
                                            e.target.value = e.target.value.trim();
                                            register('newPassword').onChange(e);
                                        }}
                                        onBlur={(e) => {
                                            register("newPassword").onBlur(e);
                                            handlePasswordBlur()
                                        }}
                                    />
                                    <button className={styles.showPassBtn} onClick={() => togglePasswordVisibility('password2')}>
                                        {passwordVisibility.password2 ? <ShowPassIcon /> : <HidePassIcon />}
                                    </button>
                                </span>
                                </Tooltip>

                        </div>

                        <div className={clsx(styles.FormInputGroup)}>
                            <span className={styles.lblInput}>Confirm Password</span>
                            <span className={clsx(styles.inputSection, (errors?.newPassword || errors?.confirmPassword) && styles.errorEmail)}>
                                <input type={passwordVisibility.password3 ? 'password' : 'text'} {...register("confirmPassword")} placeholder='Confirm password'
                                    onChange={(e) => {
                                        e.target.value = e.target.value.trim();
                                        register('confirmPassword').onChange(e);
                                    }}
                                    onBlur={(e) => {
                                        register("confirmPassword").onBlur(e);
                                        handlePasswordBlur()
                                    }}
                                />
                                <button className={styles.showPassBtn} onClick={() => togglePasswordVisibility('password3')}>
                                    {passwordVisibility.password3 ? <ShowPassIcon /> : <HidePassIcon />}
                                </button>
                            </span>
                        </div>

                        {(errors?.newPassword || errors?.confirmPassword) &&
                            <div className={styles.errorBorder}>
                                <ErrorEmailIcon />
                            </div>
                        }
                         {(errors?.newPassword || errors?.confirmPassword )  && 
                            <span className='errorText2 errorTextForget'><CrossLogo />{errors?.newPassword?.message || errors?.confirmPassword?.message}</span>
                        }
                    </div>

                </div>
                <div className={styles.btnSection}>
                    <button className={clsx(styles.btnReset,styles.saveBtnChangePass)} onClick={() => { handleSubmit(submit)() }} disabled={Object.keys(errors).length > 0  || !isValid }>Save</button>
                </div>
            </div>
            </IonContent>
        </IonPage>
    );
};

export default ChangePassword;