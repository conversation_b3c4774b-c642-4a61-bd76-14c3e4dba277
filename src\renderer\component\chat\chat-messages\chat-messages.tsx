import clsx from 'clsx';
import styles from './chat-messages.module.scss';
import React, { useState } from 'react';
import { Badge } from '@mui/material';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import { ChatMessage } from './chat-message';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

type Props = {
    sdk: any;
    channelMessages: any[];
    userData: any;
    startLoader: boolean,
    autoScrollToBottom:Function,
    isFetching: boolean
};
export const ChatMessages: React.FC<Props> = ({
    sdk,
    channelMessages,
    userData,
    startLoader,
    autoScrollToBottom,
    isFetching
}) => {
    const { showLoader }: any = useGlobalStore();

    const chatUserId = userData?.chat_data?.user_id;

    const [showMenu, setShowMenu] = useState(-1);

    const isDateCurrentDate = (utcTime: string) => {
        const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const userLocalTime = dayjs.utc(utcTime).tz(userTimezone);
        const currentDate = dayjs().tz(userTimezone);
        return userLocalTime.isSame(currentDate, 'day');
    }

    function convertUTCToLocal(utcTime: string) {
        const localDateTime = dayjs.utc(utcTime).local();

        const date: any = isDateCurrentDate(utcTime) ? "Today" : localDateTime.format("D MMM YYYY");
        const time = localDateTime.format("h:mm A");
        return { date, time };
    }

    if (channelMessages.length > 0) {
        const chatElement: any[] = [];
        const len = channelMessages.length - 1;

        for (let i = len; i >= 0; i--) {
            const obj = channelMessages[i];

            const userName = obj.user.username.split("_");
            const userFirstLetter = userName[0][0]?.toUpperCase();
            let userLastLetter = "";
            if (userName.length > 1) {
                userLastLetter = userName[1][0]?.toUpperCase();
            }
            obj.username = userFirstLetter + userLastLetter;

            const dateObj = convertUTCToLocal(obj.created);
            obj.date = dateObj.date;
            obj.time = dateObj.time;

            chatElement.push(obj);
        }

        const sortByDate: any = {};

        chatElement.forEach((obj, i) => {
            const date = obj.date;

            if (i > 0) {
                obj.groupSameUserMsg = obj.user._id === chatElement[i - 1].user._id;
            }

            if (!Array.isArray(sortByDate[date])) {
                sortByDate[date] = []
            }
            sortByDate[obj.date] = [...sortByDate[obj.date], obj];
        });

        const handleReactionSelect = (reaction: string, msgObj: any) => {
            const alreadyLiked = msgObj.likes.filter((like: any) => like.userId === chatUserId && like.reaction === reaction);
            if (alreadyLiked.length === 0)
                sdk.likeMessage(msgObj._id, reaction, "liked");
            else
                sdk.likeMessage(msgObj._id, reaction, "unliked");
            setShowMenu(-1); // Hide the menu once a reaction is selected
        };

        return Object.keys(sortByDate)?.map(key => {
            const messages = sortByDate[key];

            return <div key={key}>
                <div className={styles.dateMain}>
                  <div className={styles.date}>{isFetching ? 'Loading...' : key}</div>
                </div> 
            {messages.map((obj: any, index: number) => (<div className={styles.chatMessagesContent} key={index} id={`message-${obj._id}`}>
                {!obj.groupSameUserMsg && <Badge overlap="circular" classes={{ badge: obj.user._id === chatUserId ? styles.ownMessagesBedge : styles.ownMessagesBedge1 }} badgeContent={obj.username} color="secondary" className={clsx(styles.userName, obj.user._id === chatUserId && styles.ownMessagesMainContent)}></Badge>}
                <div className={clsx(obj.user._id === chatUserId && styles.ownMessagesSection, styles.messageContent)}>
                    <ChatMessage obj={obj} sdk={sdk} chatUserId={chatUserId} handleReactionSelect={handleReactionSelect} autoScrollToBottom={autoScrollToBottom}/>
                </div>


            </div>))}
        </div>;
        });
    } else {
        return <div className={styles.noChatFound}>
            {!showLoader && !startLoader && <p>No Chats Found.</p>}
        </div>;
    }
};
