// @ts-nocheck
import { IonContent, IonPage, useIonRouter } from '@ionic/react';
import { ReactComponent as EnterIcon } from '../../assets/images/icon-enter.svg';

function Success() {
    const router = useIonRouter()

    const handleSuccessClick = () => {
        router.push('/search',{animate:true,direction:'forward'})
    }
    return (
        <>
            <IonPage>
                <IonContent>
                    <div className='successPanel bgImg'>
                        <div className='successBody'>
                            <div className='greenText'>Invitation Sent Successfully</div>
                            <div onClick={handleSuccessClick} className='whiteText'>Return to Search <EnterIcon /></div>
                        </div>
                    </div>
                </IonContent>
            </IonPage>
        </>
    );
}

export default Success;