import { Select, MenuItem } from '@mui/material';
import { ReactComponent as ResetIcon } from '../../../assets/images/icon-reset.svg';
import { ReactComponent as ResetIconHover } from '../../../assets/images/icon-reset-hover.svg';
import { ReactComponent as ShareIcon } from '../../../assets/images/icon-share.svg';
import { ReactComponent as ShareIconHover } from '../../../assets/images/icon-share-hover.svg';
import { ReactComponent as DropdownIcon } from '../../../assets/images/icon_Triangle.svg';
import { useState } from 'react';
import { useGlobalStore, unitDropdownChangeHandler, handleSelectSingleProduct, prizeFormatter } from '@bryzos/giss-ui-library';
import { ProductPricingModel, ProductQtyType } from "../../types/Search";
import DisplayProductDescription from '../../components/DisplayProductDescription';
import PricingFeedback from './pricingFeedback';

type SelectedProductListState = {
    selectedProducts: ProductPricingModel[],
    setSelectedProducts: React.Dispatch<ProductPricingModel[]>,
    isUserChangedSelectedOption: boolean,
    setIsUserChangedSelectedOption: React.Dispatch<boolean>,
    setSelectedOption: React.Dispatch<ProductQtyType>,
    selectedOption: ProductQtyType,
    setShowWidgetPanel: React.Dispatch<boolean>,
    sessionId: string,
    dataOpticsApi1: (payload: any) => void
};

const SelectedProductPricing: React.FC<SelectedProductListState> = ({selectedProducts, setSelectedProducts, isUserChangedSelectedOption, setIsUserChangedSelectedOption, setSelectedOption, selectedOption, sessionId, setShowWidgetPanel, dataOpticsApi1}) => {
    const { userData, setUserData } = useGlobalStore();
    const [focusSingleProduct, setFocusSingleProduct] = useState<{ [key: number]: boolean }>({});

    const handleResetData = () => {
        setSelectedProducts([]); 
        setUserData({ ...userData, selectedProducts: [], feedBackData: {}, singleProduct: []});
        setIsUserChangedSelectedOption(false);
        // const newSessionId = uuidv4();
        // setSessionId(newSessionId)
      }

    const handleSharePrice = () => {
        setShowWidgetPanel(true);
    }
    
    return (
        <div className="selectProduct">
            <div className='headingSelect'>
                <div className='firstDiv'>
                    <div className='resetIcon cp' onClick={() => handleResetData()}>
                        <span className='img1'><ResetIcon /></span>
                        <span className='img2'><ResetIconHover /></span>
                    </div>
                    <div className='resetIcon cp' onClick={() => handleSharePrice()}>
                        <span className='img1'><ShareIcon /></span>
                        <span className='img2'><ShareIconHover /></span>
                        {Object.keys(focusSingleProduct).length !== 0 ? <span className='img3'><ShareIconHover /></span> : ''}
                    </div>
                <span>
                    <Select
                    value={selectedOption}
                    onChange={(event) => { unitDropdownChangeHandler(event.target.value, setSelectedOption, setIsUserChangedSelectedOption, sessionId, selectedProducts) }}
                    className={`unitDropdown ${selectedOption === "cwt,ft" ? "multipleunitDropdown" : ""}`}
                    IconComponent={DropdownIcon}
                    MenuProps={{
                        classes: {
                        paper: 'SelectUnitDropdown',
                        },
                    }}
                    >
                    {selectedOption === "cwt,ft" && <MenuItem value="cwt,ft" disabled={true} style={{ display: 'none' }}>$/CWT, $/FT</MenuItem>}
                    <MenuItem value="cwt">$/CWT</MenuItem>
                    <MenuItem value="ft">$/FT</MenuItem>
                    <MenuItem value="lb">$/LB</MenuItem>
                    </Select>
                </span>
                </div>
            </div>

            <div className='selectedPordListScroll'>
                {Array.isArray(selectedProducts) &&
                selectedProducts.map((product, index) => (
                    <div key={product.id}>
                    <table className={`liBodyList ${focusSingleProduct[product.id] ? 'clickToShare' : ''}`} onClick={() => handleSelectSingleProduct(product, focusSingleProduct, setFocusSingleProduct)} >
                        <tbody>
                        <tr>
                            <td>
                            <DisplayProductDescription description={product?.UI_Description}/>
                            </td>
                            <td>
                            <div className='priceRating'>
                                <div>
                                <p className='selectPrize'>
                                    <span className='doller'>$</span>
                                    <span className='prize'>{prizeFormatter(product, isUserChangedSelectedOption, selectedOption)}</span>
                                </p>
                                </div>
                                
                            </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <PricingFeedback
                    product={product} 
                    isUserChangedSelectedOption={isUserChangedSelectedOption} 
                    selectedOption= {selectedOption} 
                    index={index} />
                    <div className='lineH'></div>
                    </div>
                )
                )}
            </div>
        </div>
    )
}

export default SelectedProductPricing;