import{bR as a,bS as i,bT as c,bU as d,bV as l}from"./vendor-96deee04.js";/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const m=()=>{const e=window;e.addEventListener("statusTap",()=>{a(()=>{const o=e.innerWidth,s=e.innerHeight,n=document.elementFromPoint(o/2,s/2);if(!n)return;const t=i(n);t&&new Promise(r=>c(t,r)).then(()=>{d(async()=>{t.style.setProperty("--overflow","hidden"),await l(t,300),t.style.removeProperty("--overflow")})})})})};export{m as startStatusTap};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
