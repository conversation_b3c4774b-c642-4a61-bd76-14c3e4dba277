.shareAppContent {
    padding: 58px 16px 24px 16px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    @media (max-width:360px) {
        padding: 56px 16px 24px 16px;
    }
}

.shareAppTitle {
    font-family: Noto Sans Display;
    font-size: 18px;
    font-weight: 600;
    line-height: 1.4;
    text-align: left;
    color: #fff;
    margin-top: 0px;
    margin-bottom: 12px;
    @media (max-width:360px) {
        font-size: 16px;
    }
}

.AppShareInput.AppShareInput {
    position: relative;
    label {
        display: block;
        opacity: 0.7;
        font-family: Noto Sans Display;
        font-size: 12px;
        font-weight: normal;
        line-height: 1.4;
        text-align: left;
        color: #fff;
        margin-bottom: 4px;
    }

    input {
        width: 100%;
        height: 40px;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        padding: 10px 8px 10px 12px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.1);
        border: 0px;
        box-shadow: none;
        font-family: Noto Sans Display;
        font-size: 14px;
        line-height: 1.6;
        letter-spacing: normal;
        text-align: left;
        color: #fff;
        margin-bottom: 16px;

        &.inputError{
            border: 0.5px solid #ff0000;
        }

        &:focus {
            outline: none;
        }

        &::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
    }


    textarea {
        height: 110px;
        flex-grow: 0;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        padding: 10px 8px 12px 12px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.1);
        border: 0px;
        box-shadow: none;
        font-family: Noto Sans Display;
        font-size: 14px;
        line-height: 1.6;
        letter-spacing: normal;
        text-align: left;
        color: #fff;

        &:focus {
            outline: none;
        }

        &::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
    }
}

.btnSectionGrid {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

.cancelBtn {
    height: 40px;
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 10px 24px;
    border-radius: 8px;
    border: solid 1px #fff;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    text-align: center;
    color: #fff;
}

.invitationSentSuccessfully {
    font-family: Noto Sans Display;
    font-size: 18px;
    font-weight: 600;
    line-height: 1.4;
    text-align: left;
    color: #70ff00;
    margin-bottom: 8px;
}

.sendBtn {
    height: 40px;
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 10px 24px;
    border-radius: 8px;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    text-align: center;
    background-color: #70ff00;
    color: #000;

    &[disabled]{
        background-color: rgba(255, 255, 255, 0.1);
        opacity: 0.3;
    }
}

.submitPopupSection {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.returnSearchBtn {
    opacity: 0.8;
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #fff;
}

.doneBtn {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    padding: 10px 24px;
    border-radius: 8px;
    border: solid 1px rgba(255, 255, 255, 0.5);
    background-color: #fff;
    font-family: Noto Sans Display;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.4;
    text-align: center;
    color: #000;
    margin-top: auto;
}

.errorText1{
    font-family: Noto Sans Display;
    font-size: 13px;
    font-weight: normal;
    line-height: 1;
    text-align: left;
    color: #ff0000;
    position: absolute;
    bottom: -1px;
}